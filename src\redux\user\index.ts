import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { getUserInfoWithToken } from '@/api/login'



// 创建异步 thunk 来获取用户信息
export const asuncUserInfoWithToken: any = createAsyncThunk(
  'user/userInfoWithToken',
  // async (userId: string, { rejectWithValue }) => {
  async (_, { rejectWithValue }) => {
    try {
      const response: any = await getUserInfoWithToken();
      if (response.code == 200) {
        return response.data;
      }
      return {}
    } catch (error: any) {
      return rejectWithValue(error.message);
    }
  }
);


export interface userState {
  user: {};
  UserInfoWithToken: {}
}

const initialState: userState = {
  user: {},
  UserInfoWithToken: {}
};

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    setuserSlice: (state: userState, action) => {
      // console.log(action.payload);
      // localStorage.setItem('userInfo', action.payload)
      state.user = action.payload
    },
  },
  // 处理异步 thunk 的额外 reducer
  extraReducers: (builder) => {
    builder.addCase(asuncUserInfoWithToken.pending, (state: any) => {
      state.loading = true;
      state.error = null;
    }).addCase(asuncUserInfoWithToken.fulfilled, (state: any, action) => {
      state.loading = false;
      state.UserInfoWithToken = action.payload;
    })
      .addCase(asuncUserInfoWithToken.rejected, (state: any, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const { setuserSlice } = userSlice.actions;

export default userSlice.reducer;
