import { createBrowserRouter, useLocation } from 'react-router-dom';
import {
  Error400Page,
  Error403Page,
  Error404Page,
  Error500Page,
  Error503Page,
  ErrorPage,
  SignInPage,
  InjectionPage,
  ExchangePage,
  TenantRevenuePage,
  PaymentPage,
  AccountManagementPage,
  CardAccountPage,
  AccountDetailsPage,
  CardAccountDetailsPage,
  InfoPage,
  SecurityPage,
  OverviewPage,
  MenuPage, RolePage,
  SystemUserPage,
  OrganizPage,
  OrganizApiPage,
  OrganizCardPage,
  CardMerchantPage,
  CurrencyPage,
  CardListPage,
  WebHookPage,
  CardAccountSyncPage,
  CardAccountTransactionLogPage,
  KYCPage,
  KYCUpdatePage,
  InventoryPage,
  // AccountIndexPage,
} from '@/pages';
import {
  DashboardLayout,
  // GuestLayout,
} from '@/layouts';
// import { AboutPage } from '@/pages/About.tsx';
import { TransactionsPage } from '@/pages/Transactions.tsx';


import React, { ReactNode, useEffect } from 'react';

export const ScrollToTop: React.FC = () => {
  const { pathname } = useLocation();

  useEffect(() => {
    window.scrollTo({
      top: 0,
      left: 0,
      behavior: 'smooth',
    }); // Scroll to the top when the location changes
  }, [pathname]);

  return null; // This component doesn't render anything
};

type PageProps = {
  children: ReactNode;
};

// Create an HOC to wrap your route components with ScrollToTop
const PageWrapper = ({ children }: PageProps) => {
  return (
    <>
      <ScrollToTop />
      {children}
    </>
  );
};

// Create the router
const router = createBrowserRouter([
  {
    path: '/',
    errorElement: <ErrorPage />,
    children: [
      {
        index: true,
        path: '',
        element: <SignInPage />,
      },
    ],
  },
  {
    path: '/convenient',
    element: <PageWrapper children={<DashboardLayout />} />,
    errorElement: <ErrorPage />,
    children: [
      {
        index: true,
        path: 'injection',
        element: <InjectionPage />,
      },
      {
        index: true,
        path: 'tenantRevenue',
        element: <TenantRevenuePage />,
      },
      {
        path: 'payment',
        element: <PaymentPage />,
        children: [
          {
            path: 'receiveExpenses',
            element: <PaymentPage />,
          },
          {
            path: 'transfer',
            element: <PaymentPage />,
          },
        ]
      },
      {
        path: 'exchange',
        element: <ExchangePage />,
      },

    ],
  },
  {
    path: '/auth',
    errorElement: <ErrorPage />,
    children: [
      {
        path: 'signin',
        element: <SignInPage />,
      }
    ],
  },
  {
    path: '/acccess',
    element: <PageWrapper children={<DashboardLayout />} />,
    errorElement: <ErrorPage />,
    children: [
      {
        index: true,
        path: 'account-management',
        element: <AccountManagementPage />,
      },
      {
        path: 'account-management/details',
        element: <AccountDetailsPage />,
      },
      {
        path: 'card-account',
        element: <CardAccountPage />,
      },
      {
        path: 'card-account/details',
        element: <CardAccountDetailsPage />,
      },
      {
        path: 'Card',
        element: <CardListPage />,
      },

    ],
  },
  {
    path: '/assets',
    element: <PageWrapper children={<DashboardLayout />} />,
    errorElement: <ErrorPage />,
    children: [
      {
        path: 'fiat',
        element: <CardAccountPage />,
      },
      {
        path: 'digital',
        element: <CardAccountPage />,
      },
      {
        path: 'exchange-sub-account',
        element: <CardAccountPage />,
      },

    ],
  },
  {
    path: '/overview',
    element: <PageWrapper children={<DashboardLayout />} />,
    errorElement: <ErrorPage />,
    children: [
      {
        index: true,
        path: '',
        element: <OverviewPage />,
      },
    ],
  },
  {
    path: '/transactions',
    element: <PageWrapper children={<DashboardLayout />} />,
    errorElement: <ErrorPage />,
    children: [
      {
        index: true,
        path: '',
        element: <TransactionsPage />,
      },
    ],
  },
  {
    path: '/webhook',
    element: <PageWrapper children={<DashboardLayout />} />,
    errorElement: <ErrorPage />,
    children: [
      {
        index: true,
        path: '',
        element: <WebHookPage />,
      },
    ],
  },
  {
    path: '/cardAccountSync',
    element: <PageWrapper children={<DashboardLayout />} />,
    errorElement: <ErrorPage />,
    children: [
      {
        index: true,
        path: '',
        element: <CardAccountSyncPage />,
      },
    ],
  },
  {
    path: '/cardAccountTransactionLog',
    element: <PageWrapper children={<DashboardLayout />} />,
    errorElement: <ErrorPage />,
    children: [
      {
        index: true,
        path: '',
        element: <CardAccountTransactionLogPage />,
      },
    ],
  },
  {
    path: '/kyc',
    element: <PageWrapper children={<DashboardLayout />} />,
    errorElement: <ErrorPage />,
    children: [
      {
        index: true,
        path: '',
        element: <KYCPage />,
      },
      {
        path: 'update',
        element: <KYCUpdatePage />,
      },
    ],
  },
  {
    path: '/profile',
    element: <PageWrapper children={<DashboardLayout />} />,
    errorElement: <ErrorPage />,
    children: [
      {
        path: 'info',
        element: <InfoPage />,
      },
      {
        path: 'security',
        element: <SecurityPage />,
      },
    ],
  },
  {
    path: '/sys',
    element: <PageWrapper children={<DashboardLayout />} />,
    errorElement: <ErrorPage />,
    children: [
      {
        index: true,
        path: 'menu',
        element: <MenuPage />,
      },
      {
        path: 'role',
        element: <RolePage />,
      },
      {
        path: 'cardMerchant',
        element: <CardMerchantPage />,
      },
      {
        path: 'currency',
        element: <CurrencyPage />,
      },
      {
        path: 'system-user',
        element: <SystemUserPage />,
      },
      {
        path: 'organiz',
        element: <OrganizPage />,
      },
      {
        path: 'organiz/apiConfig',
        element: <OrganizApiPage />,
      },
      {
        path: 'organiz/cardConfig',
        element: <OrganizCardPage />,
      },
      {
        path: 'inventory',
        element: <InventoryPage />,
      },
    ],
  },
  {
    path: 'errors',
    errorElement: <ErrorPage />,
    children: [
      {
        path: '400',
        element: <Error400Page />,
      },
      {
        path: '403',
        element: <Error403Page />,
      },
      {
        path: '404',
        element: <Error404Page />,
      },
      {
        path: '500',
        element: <Error500Page />,
      },
      {
        path: '503',
        element: <Error503Page />,
      },
    ],
  },
],
  {
    basename: "/admin", // 配置跟路径
  });

export default router;
