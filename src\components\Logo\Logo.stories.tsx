// import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
// import { withRouter } from 'storybook-addon-react-router-v6';

import { Logo } from './Logo.tsx';

const meta = {
  title: 'Components/Logo',
  component: Logo,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  decorators: [],
}
export default meta;


export const Black: any = {
  args: {
    color: 'black',
  },
};

export const White: any = {
  args: {
    color: 'white',
    bgColor: 'black',
  },
};

export const AsLink: any = {
  args: {
    color: 'black',
    asLink: true,
  },
};

export const CustomImageHeight: any = {
  args: {
    color: 'black',
    imgSize: {
      h: 36,
    },
  },
};
