import {
    Table, Input, Select, Modal, Form, Upload, Tabs,
    message, Card, Flex, Button, InputNumber, Image
} from 'antd';
import {
    HomeOutlined,
    LoadingOutlined,
    PlusOutlined,
} from '@ant-design/icons';
import { PageHeader } from '@/components';
import { useTranslation } from 'react-i18next';
import { useEffect, useRef, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom'
import {
    getMerchantCardTypeList,
    getMerchantCardAdd,
    getMerchantCardUpload,
    getMerchantCard,
    postQueryTenantCardFee,
    postUpdTenantCardFee,
} from '@/api/sys'
import { useSelector } from 'react-redux';
import {
    STATUS_ENUM,
    EVENT_FEEENUM
} from '@/utils/enum';

export const OrganizCardPage = () => {
    const navigate = useNavigate();
    // const context = useStylesContext();
    const { t } = useTranslation()
    const [current, setCurrent] = useState<number>(1)
    const [total, setTotal] = useState<number>(0)
    const loadingRef = useRef(true)
    const [loading, setLoading] = useState(false)
    const [open, setOpen] = useState(false);
    const [data, setData] = useState([])
    const formRef: any = useRef()
    const [activeData, setActive] = useState<any>()
    const location = useLocation()
    const [configListData, setConfigListData] = useState<any>([])
    const [front, setFront] = useState()
    const [back, setBack] = useState()
    const [activeFeeList, setActiveFeeList] = useState([])
    const [openFee, setOpenFee] = useState(false);
    const [tabActiveKey, setTabActiveKey] = useState('1')
    const userStore = useSelector((state: any) => { return state.user });


    useEffect(() => {
        if (loadingRef.current) {
            loadingRef.current = false
            query()
        }
    }, [])
    const pageChange = (page: number) => {
        setCurrent(page)
    }

    const query = (value?: any, type?: string) => {
        if (!loading) {
            getMerchantCardTypeList({
                pageNum: 1,
                pageSize: 20,
                appid: location?.state.id,
                name: type == 'search' ? value : undefined
            }).then((res: any) => {
                if (res.code == 200) {
                    setTotal(res.data?.total)
                    setData(res.data?.list || [])
                }
            }).catch(err => {
                console.log(err);
            }).finally(() => {
                setLoading(false)
                queryConfigList()
            })
        }
    }

    const queryFee = (row: any) => {
        if (!loading) {
            postQueryTenantCardFee({
                appid: row.appid,
                productId: row.id
            }).then((res: any) => {
                if (res.code == 200) {
                    const obj: any = {}
                    res.data.forEach((v: any) => {
                        if (obj[v.type]) {
                            obj[v.type].push(v)
                        } else {
                            obj[v.type] = [v]
                        }
                    })
                    console.log(obj);

                    const list: any = Object.keys(obj).map((v: any) => {
                        const item = obj[v]
                        return {
                            label: v,
                            options: item.map((t: any) => {
                                return {
                                    ...t,
                                    value: t.id,
                                    label: `${EVENT_FEEENUM()[t.event]}-${t.currency}`
                                    // -${t.currency}-${t.name}`
                                }
                            })
                        }
                    })
                    setTabActiveKey(list[0]?.label || '1')
                    setActiveFeeList(list)
                    console.log(list);

                    setOpenFee(true)
                }
            }).catch(err => {
                console.log(err);
            }).finally(() => {
                setLoading(false)
            })
        }
    }

    const queryConfigList = () => {
        getMerchantCard({}).then((res: any) => {
            if (res.code == 200) {
                setConfigListData(res.data || [])
            }
        }).catch(err => {
            console.log(err);
        }).finally(() => {
        })
    }

    const updataFee = () => {
        //@ts-ignore
        const list: any = (activeFeeList.filter((v: any) => v.label == tabActiveKey))[0]?.options
        if (!list || list.length === 0) {
            setLoading(false)
            return
        }
        setLoading(true)
        Promise.allSettled(
            list.map((v: any) =>
                postUpdTenantCardFee({
                    ...v,
                    multiplicationFactor: parseFloat((v?.multiplicationFactor).toFixed(4)),
                })
            )
        ).then((res) => {
            const successes = res.filter((res: any) => res.status === "fulfilled")
            if (successes.length === list.length) {
                message.open({
                    type: 'success',
                    content: activeData?.id ? t('修改成功') : t('添加成功'),
                })
                cancel()
                query()
            } else {
                message.open({
                    type: 'error',
                    content: t('部分请求失败，请检查网络或数据'),
                })
            }
        }).finally(() => {
            setLoading(false)
        })
    }

    const cancel = () => {
        setOpen(false);
        setActive({})
        setOpenFee(false)
        // setActiveFeeType(true)
        setFront(undefined)
        setBack(undefined)
        setTabActiveKey('0')
    }
    const addAccount = () => {
        let api = getMerchantCardAdd
        if (activeData?.id) api = getMerchantCardUpload
        formRef.current?.validateFields().then((values: any) => {
            setLoading(true)
            api({
                appid: location?.state?.id,
                ...values,
                cardImage: front,
                cardPhysicalImage: back,
                id: activeData?.id || undefined
            }).then((res: any) => {
                const { code } = res
                if (code == 200) {
                    message.open({
                        type: 'success',
                        content: activeData?.id ? t('修改成功') : t('添加成功')
                    })
                    cancel()
                    query()
                    return
                }
                message.open({
                    type: 'error',
                    content: t(res.message)
                })
            }).finally(() => {
                setLoading(false)
            })
        })

    }
    const add = () => {
        setOpen(true);
    }

    const goBack = () => {
        navigate('/sys/organiz')
    }




    const beforeUpload = () => false;

    const getBase64 = (img: any, callback: (url: string) => void) => {
        const reader = new FileReader();
        reader.addEventListener('load', () => callback(reader.result as string));
        reader.readAsDataURL(img);
    };

    const handleChange: any = (info: any) => {
        getBase64(info.file, (url: any) => {
            setFront(url);
        });
    };
    const handleChangeBack: any = (info: any) => {
        getBase64(info.file, (url: any) => {
            setBack(url);
        });
    };

    const uploadButton = (
        <button style={{ border: 0, background: 'none' }} type="button">
            {loading ? <LoadingOutlined /> : <PlusOutlined />}
            <div style={{ marginTop: 8 }}>Upload</div>
        </button>
    );

    function imageUrlToBase64(url: string, callback: any) {
        const xhr = new XMLHttpRequest();
        xhr.open('GET', url, true);
        xhr.responseType = 'blob';

        xhr.onload = function () {
            if (xhr.status === 200) {
                // 创建一个FileReader对象
                const reader = new FileReader();
                reader.onloadend = function () {
                    callback(reader.result);
                }
                reader.readAsDataURL(xhr.response); // 将Blob对象读取为Base64编码的字符串
            } else {
                console.error('Unable to fetch image');
            }
        };

        xhr.send();
    }

    return (
        <div>
            <PageHeader
                title=""
                breadcrumbs={[
                    {
                        title: (
                            <span onClick={goBack} className=' cursor-pointer'>
                                <HomeOutlined />
                                <span>{t('组织管理')}</span>
                            </span>
                        ),

                    },
                    {
                        title: (
                            <>
                                <span>{t('信用卡产品')}</span>
                            </>
                        ),

                    },
                ]}
            />
            <Card className='mt-[20px]'>
                <Flex justify='space-between'>
                    {
                        userStore?.user?.user?.roleType == "0" ? <Button onClick={add} type='primary'> {t('新增配置')}</Button> : ''
                    }

                </Flex>
                <Table
                    loading={loading}
                    className='mt-[20px]'
                    rowKey={(v: any) => v?.id}
                    scroll={{ x: '1000px' }}
                    columns={[
                        {
                            title: t('iD'),
                            dataIndex: 'id',
                            key: 'id',
                        },
                        {
                            title: t('卡商'),
                            dataIndex: 'cardMerchantName',
                            key: 'cardMerchantName',
                        },
                        {
                            title: t('卡片'),
                            dataIndex: 'creditCardName',
                            key: 'creditCardName',
                        },
                        {
                            title: t('卡片类型'),
                            dataIndex: 'cardType',
                            key: 'cardType',
                            render: (_, row: any) => {
                                return row.cardType === 'physical' ? t('实体卡') : t('虚拟卡');
                            }
                        },
                        {
                            title: t('每日消费限额'),
                            dataIndex: 'dailyPurchaseLimit',
                            key: 'dailyPurchaseLimit',
                        },
                        {
                            title: t('每日ATM取款限额'),
                            dataIndex: 'dailyAtmLimit',
                            key: 'dailyAtmLimit',
                        },
                        {
                            title: t('每日限额'),
                            dataIndex: 'dailyLimit',
                            key: 'dailyLimit',
                        },
                        {
                            title: t('单笔消费限额'),
                            dataIndex: 'singleTransactionLimit',
                            key: 'singleTransactionLimit',
                        },
                        {
                            title: t('开卡手续费'),
                            dataIndex: 'cardIssuanceFee',
                            key: 'cardIssuanceFee',
                        },
                        {
                            title: t('状态'),
                            dataIndex: 'status',
                            key: 'status',
                            render: (_, row: any) => {
                                return STATUS_ENUM[row.status]
                            }
                        },

                        {
                            title: t('操作'),
                            key: 'action',
                            fixed: 'right',
                            render: (_, row: any) => {
                                return <Flex>
                                    <Button className=' cursor-pointer _primary' onClick={() => {
                                        imageUrlToBase64(row.imageUrl, (base64: any) => {
                                            setFront(base64);
                                        })
                                        imageUrlToBase64(row.physicalImageUrl, (base64: any) => {
                                            setBack(base64);
                                        })
                                        setActive(row)
                                        setOpen(true)
                                        setTimeout(() => {
                                            formRef.current?.setFieldsValue({
                                                ...row,
                                                cardMerchantProductId: row.cardMerchantProductId || 1900,
                                                cardImage: row.imageUrl,
                                                cardPhysicalImage: row.physicalImageUrl,
                                            })
                                        }, 10)
                                    }} size='small' type="text">{
                                            userStore?.user?.user?.roleType == "0" ? t('修改') : t('详情')}</Button>
                                    <Button type="text" className='cursor-pointer _primary' size='small' onClick={() => {
                                        setActive(row)
                                        queryFee(row)
                                    }}>{t('费率')}</Button>
                                </Flex>
                            },
                        },
                    ]}
                    dataSource={data}
                    pagination={{
                        hideOnSinglePage: true,
                        pageSize: 10,
                        current,
                        total,
                        onChange: pageChange,
                        position: ['bottomRight']
                    }}
                />
            </Card>
            <Modal
                maskClosable={false}
                keyboard={false}
                title={activeData?.id ? t("修改配置") : t("新增配置")}
                open={open}
                onOk={addAccount}
                centered
                onCancel={cancel} 
                okText={t('确认')}

                {...(userStore?.user?.user?.roleType == "0" ? {} : { footer: null })}
                confirmLoading={loading}
                width={800}
                destroyOnClose={true}
                cancelText={t("取消")}
            >
                <Form
                    ref={formRef}
                    name="sign-up-form"
                    layout="horizontal"
                    autoComplete="off"
                    labelCol={{ flex: '120px' }}
                    labelWrap
                    disabled={userStore?.user?.user?.roleType !== "0"}
                    defaultValue={{ ...activeData }}
                    requiredMark={false}
                >
                    <Flex justify='s'>
                        <div className='w-[50%]'>
                            <Form.Item
                                label={t('卡商')}
                                name="cardMerchantId"
                                rules={[
                                    { required: true, message: t('卡商') },
                                ]}
                            >
                                <Select
                                    disabled={activeData?.id}
                                    placeholder={t('类型')}
                                    options={configListData.map((v: any) => {
                                        return { label: v.cardMerchantName, value: v?.id }
                                    })} />
                            </Form.Item>
                            <Form.Item
                                label={t('卡片名称')}
                                name="creditCardName"
                                rules={[
                                    { required: true, message: t('卡片名称') },
                                ]}
                            >
                                <Input />
                            </Form.Item>
                            <Form.Item
                                label={t('最大每日消费限额')}
                                name="dailyPurchaseLimit"
                                rules={[
                                    { required: true, message: t('最大每日消费限额') },
                                ]}
                            >
                                <Input />
                            </Form.Item>
                            <Form.Item
                                label={t('最大每日提款限额')}
                                name="dailyAtmLimit"
                                rules={[
                                    { required: true, message: t('最大每日提款限额') },
                                ]}
                            >
                                <Input />
                            </Form.Item>
                            <Form.Item
                                label={t('最大每日限额')}
                                name="dailyLimit"
                                rules={[
                                    { required: true, message: t('最大每日限额') },
                                ]}
                            >
                                <Input />
                            </Form.Item>
                            <Form.Item
                                label={t('最大单一交易限额')}
                                name="singleTransactionLimit"
                                rules={[
                                    { required: true, message: t('最大单一交易限额') },
                                ]}
                            >
                                <Input />
                            </Form.Item>
                            <Form.Item
                                label={t('开卡手续费')}
                                name="cardIssuanceFee"
                                rules={[
                                    { required: true, message: t('开卡手续费') },
                                ]}
                            >
                                <Input />
                            </Form.Item>

                            <Form.Item
                                label={t('备注')}
                                name="remarks"
                                rules={[
                                    { required: true, message: t('备注') },
                                ]}
                            >
                                <Input />
                            </Form.Item>
                        </div>
                        <div className='w-[50%]'>
                            {activeData?.id ? <Form.Item
                                label={t('产品ID')}
                                name="cardMerchantProductId"
                            >
                                {activeData?.id}
                            </Form.Item> : ''}
                            <Form.Item
                                label={t('卡商产品ID')}
                                name="cardMerchantProductId"
                                initialValue={1900}
                                rules={[
                                    { required: true, message: t('卡商产品ID') },
                                ]}
                            >
                                <Input defaultValue={1900} disabled={activeData?.id} />
                            </Form.Item>

                            <Form.Item
                                label={t('卡片类型')}
                                name="cardType"
                                rules={[
                                    { required: true, message: t('卡片类型') },
                                ]}
                            >
                                <Select placeholder={t('选择卡片类型')}>
                                    <Select.Option value="physical">{t('实体卡')}</Select.Option>
                                    <Select.Option value="virtual">{t('虚拟卡')}</Select.Option>
                                </Select>
                            </Form.Item>

                            <Form.Item
                                label={t('卡-效果图')}
                                name="cardImage"
                                initialValue={front}
                                rules={[
                                    { required: true, message: t('卡-效果图') },
                                ]}
                            >
                                <Flex align='center'>
                                    {front ?
                                        <Image
                                            className='w-full h-auto max-w-[100px] max-h-[100px] '
                                            src={front}
                                        /> : ''}
                                    <Upload
                                        name="front"
                                        listType="picture-card"
                                        className="avatar-uploader ml-[20px]"
                                        showUploadList={false}
                                        accept='image/png'
                                        beforeUpload={beforeUpload}
                                        onChange={handleChange}
                                    >
                                        {uploadButton}
                                        {/* // <img src={front} alt="avatar" className='w-full h-auto max-w-[100px] max-h-[100px]' /> : uploadButton} */}
                                    </Upload>
                                </Flex>
                            </Form.Item>
                            <Form.Item
                                label={t('卡-实体图')}
                                name="cardPhysicalImage"
                                rules={[
                                    { required: true, message: t('卡-实体图') },
                                ]}
                            >
                                <Flex align='center'>
                                    {back ?
                                        <Image
                                            className='w-full h-auto max-w-[100px] max-h-[100px] '
                                            src={back}
                                        /> : ''}
                                    <Upload
                                        name="back"
                                        accept='image/png'
                                        listType="picture-card"
                                        className="avatar-uploader ml-[20px]"
                                        showUploadList={false}
                                        beforeUpload={beforeUpload}
                                        onChange={handleChangeBack}
                                    >
                                        {uploadButton}
                                        {/* {back ? <img src={back} alt="avatar" className='w-full h-auto max-w-[100px] max-h-[100px]' /> : uploadButton} */}
                                    </Upload>
                                </Flex>
                            </Form.Item>
                            <Form.Item
                                label={t('卡片描述信息')}
                                name="cardImageDetails"
                                rules={[
                                    { required: true, message: t('备注') },
                                ]}
                            >
                                <Input.TextArea />
                            </Form.Item>
                        </div>
                    </Flex>

                </Form>
            </Modal>
            <Modal
                maskClosable={false}
                keyboard={false}
                title={t("费率")}
                open={openFee}
                onOk={updataFee}
                onCancel={cancel} 
                confirmLoading={loading}
                centered
                width={800}
                {...(userStore?.user?.user?.roleType !== "0" && tabActiveKey == "2") ? { footer: null } : {}}
                okText={t('修改当前页')}
                destroyOnClose={true}
                cancelText={t("关闭")}
            >
                <Tabs activeKey={tabActiveKey} items={
                    activeFeeList.map((v: any) => ({
                        label: <div className='text-center'>
                            {t('第')} {v.label - 1} {t('级')}
                            <div>{v.label == 2 ? t('代理') : t('额外收费')}</div>
                        </div>,
                        key: v.label,
                        children: <Table
                            dataSource={v.options}
                            pagination={false}
                            columns={[
                                {
                                    title: <div className='pl-[5px]'>{t('费率类型')}</div>,
                                    dataIndex: 'id',
                                    className: 'p-0 pl-[5px]',
                                    key: 'id',
                                    render: (_, row: any) => {
                                        return row.label
                                    }
                                },
                                {
                                    title: t('百分比'),
                                    width: 150,
                                    className: 'pl-[5px]',
                                    dataIndex: 'multiplicationFactor',
                                    key: 'multiplicationFactor',
                                    render: (_, row: any) => {
                                        return <InputNumber
                                            className='w-full'
                                            min={0}
                                            max={100}
                                            value={parseFloat((row?.multiplicationFactor * 100).toFixed(4))}
                                            controls={false}
                                            suffix={"%"}
                                            onChange={(e: any) => {
                                                setActiveFeeList((list: any) => {
                                                    return list.map((t: any) => {
                                                        if (t.label == v.label) {
                                                            return {
                                                                ...t,
                                                                options: t.options.map((m: any) => {
                                                                    if (m.id == row.id) {
                                                                        return {
                                                                            ...m,
                                                                            multiplicationFactor: (e || 0) / 100
                                                                        }
                                                                    }
                                                                    return m
                                                                })
                                                            }
                                                        }
                                                        return {
                                                            ...t,
                                                            options: [...t.options]
                                                        }
                                                    })
                                                })
                                            }}
                                            maxLength={20} />
                                    }
                                },
                                {
                                    title: t('固定金额'),
                                    dataIndex: 'fixedAmount',
                                    key: 'fixedAmount',
                                    width: 150,
                                    className: 'pl-[5px]',
                                    render: (_, row: any) => {
                                        return <InputNumber
                                            className='w-full'
                                            min={0}
                                            controls={false}
                                            value={row?.fixedAmount}
                                            maxLength={20}
                                            onChange={(e: any) => {
                                                setActiveFeeList((list: any) => {
                                                    return list.map((t: any) => {
                                                        if (t.label == v.label) {
                                                            return {
                                                                ...t,
                                                                options: t.options.map((m: any) => {
                                                                    if (m.id == row.id) {
                                                                        return {
                                                                            ...m,
                                                                            fixedAmount: e
                                                                        }
                                                                    }
                                                                    return m
                                                                })
                                                            }
                                                        }
                                                        return {
                                                            ...t,
                                                            options: [...t.options]
                                                        }
                                                    })
                                                })
                                            }}
                                        />
                                    }
                                },
                                {
                                    title: t('最低收费'),
                                    className: 'pl-[5px]',
                                    width: 150,
                                    dataIndex: 'minimumCharge',
                                    key: 'minimumCharge',
                                    render: (_, row: any) => {
                                        return <InputNumber
                                            className='w-full'
                                            min={0}
                                            controls={false}
                                            value={row?.minimumCharge}
                                            maxLength={20}
                                            onChange={(e: any) => {
                                                setActiveFeeList((list: any) => {
                                                    return list.map((t: any) => {
                                                        if (t.label == v.label) {
                                                            return {
                                                                ...t,
                                                                options: t.options.map((m: any) => {
                                                                    if (m.id == row.id) {
                                                                        return {
                                                                            ...m,
                                                                            minimumCharge: e
                                                                        }
                                                                    }
                                                                    return m
                                                                })
                                                            }
                                                        }
                                                        return {
                                                            ...t,
                                                            options: [...t.options]
                                                        }
                                                    })
                                                })
                                            }}
                                        />
                                    }
                                },
                                {
                                    title: t('备注'),
                                    dataIndex: 'remark',
                                    className: 'pl-[5px]',
                                    key: 'remark',
                                    render: (_, row: any) => {
                                        return <Input
                                            onChange={(e: any) => {
                                                setActiveFeeList((list: any) => {
                                                    return list.map((t: any) => {
                                                        if (t.label == v.label) {
                                                            return {
                                                                ...t,
                                                                options: t.options.map((m: any) => {
                                                                    if (m.id == row.id) {
                                                                        return {
                                                                            ...m,
                                                                            remark: e.target.value
                                                                        }
                                                                    }
                                                                    return m
                                                                })
                                                            }
                                                        }
                                                        return {
                                                            ...t,
                                                            options: [...t.options]
                                                        }
                                                    })
                                                })
                                            }}
                                        />
                                    }
                                },
                            ]} />
                    }))
                } onChange={setTabActiveKey} />

            </Modal>
        </div>
    );
};