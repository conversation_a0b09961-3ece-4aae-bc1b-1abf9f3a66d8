import request from '../request.ts'

// getAccountList
export function getAccountList(params: any) {
    return request({
        url: '/admin/tenant/account/listpage',
        method: 'get',
        params,
    })
}

//入金
export function postDepositRecord(data: any) {
    return request({
        url: "/admin/tenant/depositRecord/add",
        method: "post",
        data,
    });

}

//
export function postAccountAdd(data: any) {
    return request({
        url: "/admin/tenant/account/add",
        method: "post",
        data,
    });
}

export function postAccountUpdate(data: any) {
    return request({
        url: "/admin/tenant/account/update",
        method: "post",
        data,
    });
}



//账户详情与结余
export function getAccountDetails(data: any) {
    return request({
        url: "/admin/tenant/account/balance",
        method: "post",
        data,
    });
}

//账户详情与结余
export function postAddAccount(data: any) {
    return request({
        url: "/admin/system/account/addAccount",
        method: "post",
        data,
    });
}

//账户转账户
export function postAccountToAccount(data: any) {
    return request({
        url: "/admin/tenant/account/transfer/create",
        method: "post",
        data,
    });
}
//账户转卡账户
export function postAccountTocardAccount(data: any) {
    return request({
        url: "/admin/system/cardAccount/transfer",
        method: "post",
        data,
    });
}
//提取卡账户余额
export function postAccountWithdrawAccount(data: any) {
    return request({
        url: "/admin/system/cardAccount/withdraw",
        method: "post",
        data,
    });
}