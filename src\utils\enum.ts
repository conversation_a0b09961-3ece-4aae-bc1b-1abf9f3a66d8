import i18n from '@/i18n'


export const STATUS_ENUM: any = { 1: () => i18n.t('可用'), 0: i18n.t('停止') }

export const EVENT_FEEENUM: any = () => ({
    Account2Account: i18n.t('账户到账户'),
    Account2CardAccount: i18n.t('账户到卡账户'),
    CardAccount2Account: i18n.t('卡账户到账户'),
    CardAccount2CardAccount: i18n.t('卡账户到卡账户'),
    Local: i18n.t('本地消费'),
    Oversea: i18n.t('海外消费'),
    LocalATM: i18n.t('本地ATM'),
    OverseaATM: i18n.t('海外ATM'),
    AuthorizationFail: i18n.t('授权失败'),
    RefundTransaction: i18n.t('退费'),
    Exchange: i18n.t('兑换'),
    DormantAccount: i18n.t('休眠账号'),
    FreezeAccount: i18n.t('冻结账号'),
    Inject: i18n.t('注入'),
    ExpendReward: i18n.t('消费奖励'),
})

//交易事件类型
export const TRANSACTION_ENUM: any = () => {
    return [
        { label: i18n.t('全部'), value: 'all' },
        { label: i18n.t('账户交易'), value: 'account_transaction' },
        { label: i18n.t('注入'), value: 'account_inject' },
        { label: i18n.t('奖励'), value: 'account_expend_reward' },
        { label: i18n.t('兑换'), value: 'account_exchange' },
        { label: i18n.t('卡交易'), value: 'card_account_transaction' },
        { label: i18n.t('卡消费'), value: 'card_expend' },
        { label: i18n.t('卡休眠'), value: 'card_dormant' },
    ]
}

// 交易状态
export const TRANSACTION_STATUS_ENUM: any = () => ({
    "pending": i18n.t('待处理'),
    "posted": i18n.t('已过账'),
    "declined": i18n.t('拒绝'),
    "void": i18n.t('无效'),
})

// 收费类型1-前置，2-后置
export const FEETYPE_ENUM: any = {
    "1": i18n.t('前置'),
    "2": i18n.t('后置'),
}

// 发起人层级
export const LEVE_ENUM: any = (() => ({
    // "1": ()=>i18n.t('前置'),
    "2": i18n.t('第二层'),
    "3": i18n.t('第三层'),
    "4": i18n.t('第四层'),
    "5": i18n.t('第五层'),
    // "6": ()=>i18n.t('后置'),
}))

export const CARD_STATUS_ENUM: any = () => ({
    "pending-activation": i18n.t("待激活"),
    "locked": i18n.t('已锁定'),
    "active": i18n.t('已激活'),
    "suspended": i18n.t('已暂停'),
    "cancelled": i18n.t('已取消'),
    // "unavailable": i18n.t('卡商不可用'),
})

export const CARD_STATUS_COLOR_ENUM: any = {
    "pending-activation": '#FFA500',
    "locked": '#FF0000',
    "active": '#32CD32',
    "suspended": '#FF0000',
    "cancelled": '#A9A9A9',
    "unavailable": '#A9A9A9',
}

//xXxxX
export const SYS_ROLE_ENUM: any = () => ({
    0: i18n.t('系统管理员'),
    1: i18n.t('代理'),
    2: i18n.t('APP'),
})


//kyc状态
export const AUDIOS_ENUM: any = () => ({
    0: i18n.t('待处理'),
    1: i18n.t('通过'),
    2: i18n.t('拒绝'),
})

//
export const INVENTORY_CARD_EMUN: any = () => ({
    1: i18n.t('正常'),
    2: i18n.t('处理中'),
    3: i18n.t('已使用'),
    4: i18n.t('挂起'),
    5: i18n.t('已转出'),
    6: i18n.t('已取消'),
    7: i18n.t('卡商不可用'),
})