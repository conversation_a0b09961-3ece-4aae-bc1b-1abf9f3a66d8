import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
//自定义
import en from './locales/en.ts';
import zh from './locales/zh.ts';
import zh_hk from './locales/zh_hk.ts';
//常规模式
import zh_CN from 'antd/locale/zh_CN'
import en_GB from 'antd/locale/en_GB'//英
import zh_HK from 'antd/locale/zh_HK'
//针对 时间组件
import 'dayjs/locale/zh-cn';//必须
import 'dayjs/locale/en-gb';
import 'dayjs/locale/zh-hk';



export const localesList: any[] = [
    {
        code: 'en',
        label: "英语",
        value: en,
        antdLanguage: en_GB
    },
    {
        code: 'zh',
        label: "简体中文",
        value: zh,
        antdLanguage: zh_CN
    },
    {
        code: 'zh_HK',
        label: "繁体中文",
        value: zh_hk,
        antdLanguage: zh_HK
    }
]

let resources: any = {}
for (let i = 0; i <= localesList.length; i++) {
    resources[localesList[i]?.code] = {
        translation: localesList[i]?.value,
        antdLanguage: localesList[i]?.antdLanguage,
    }
}

export const LanguageObj = { ...resources }

i18n
    .use(initReactI18next)
    .init({
        resources,
        lng: 'zh', // 默认语言
        fallbackLng: 'zh', // 如果当前语言的翻译不存在，则回退到英文
        interpolation: {
            escapeValue: false, // React 已经自动防止 XSS 攻击，不需要转义
        }
    });

export default i18n;
