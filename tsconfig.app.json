{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": [
      "ES2020",
      "DOM",
      "DOM.Iterable"
    ],
    "module": "ESNext",
    "skipLibCheck": true,
    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "isolatedModules": true,
    "moduleDetection": "force",
    "noEmit": true,
    "jsx": "react-jsx",
    /* Linting */
    "strict": false,
    "noImplicitAny": false,
    "strictNullChecks": false,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "noFallthroughCasesInSwitch": false,
    "ignoreDeprecations": "5.0",
    "useUnknownInCatchVariables": false,
    "allowUnreachableCode": true,
    "noImplicitThis": false,
    "noPropertyAccessFromIndexSignature": false,
    "exactOptionalPropertyTypes": false,
    "allowJs": true,
    "checkJs": false,
    "noImplicitReturns": false,
    "noErrorTruncation": true,
    "forceConsistentCasingInFileNames": false,
    "baseUrl": "./",
    "paths": {
      "@/*": [
        "src/*"
      ],
      "@components/*": [
        "src/components/*"
      ],
      "@assets/*": [
        "src/utils/*"
      ]
    }
  },
  "include": [
    "src"
  ]
}