import {
  Tooltip, Table, Input, Modal, Form, Select, Card,
  message, Flex, Button
} from 'antd';
import {
  // HomeOutlined,
  RocketOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import { PageHeader } from '@/components';
import { useTranslation } from 'react-i18next';
import { useEffect, useState, useRef } from 'react';
import { getAccountList, postDepositRecord } from '@/api/account'
// let loadingRef = true

export const InjectionPage = () => {
  const { t } = useTranslation()
  const [current, setCurrent] = useState<number>(1)
  const [total, setTotal] = useState<number>(0)
  const [loading, setLoading] = useState(false)
  const [open, setOpen] = useState(false);
  const [data, setData] = useState([])
  const formRef: any = useRef()
  const [activeData, setActiveData]: any = useState({})
  const [submitLoading, setSubmitLoading]: any = useState(false)
  const loadingRef = useRef(true)
  // const navigate = useNavigate();

  useEffect(() => {
    if (loadingRef.current) {
      loadingRef.current = false
      query()
    }
  }, [current])

  const pageChange = (page: number) => {
    setCurrent(page)
  }

  const query = (value?: any, type?: string) => {
    if (!loading) {
      setLoading(true)
      getAccountList({
        pageNum: current,
        pageSize: 10,
        name: type == 'search' ? value : undefined
      }).then((res: any) => {
        if (res.code == 200) {
          setTotal(res.data?.total)
          setData(res.data?.list || [])
        }
      }).catch(err => {
        console.log(err);
      }).finally(() => {
        setLoading(false)
        loadingRef.current = true
        // loadingRef = true
      })
    }
  }


  const cancel = () => {
    formRef?.current?.resetFields()
    setOpen(false);
    setActiveData({})
  }
  const injection = () => {
    // console.log(formRef.current);

    formRef.current?.validateFields().then((values: any) => {
      setSubmitLoading(true)
      postDepositRecord({
        tenantAccountId: activeData?.id,
        currency: values?.outCurrencyCode,
        ...values,
        fee: 0
      }).then((res: any) => {
        const { code } = res
        cancel()
        query()
        if (code == 200) {
          message.open({
            type: 'success',
            content: t('注入成功')
          })

          return
        }
        message.open({
          type: 'error',
          content: t(res.message)
        })
      }).finally(() => setSubmitLoading(false))
    })
    // postDepositRecord({})
    // cancel()

  }

  return (
    <div>
      <PageHeader
        title={t('注入')}
        breadcrumbs={[]}
      />
      <Card className='mt-[20px]'>
        <Flex justify='space-between'>
          <Input
            prefix={<SearchOutlined />}
            allowClear
            placeholder={t('请输入关键字进行搜索')}
            className='w-[30%] max-w-[300px]'
            onChange={(e: any) => query(e.target.value, 'search')}
          />
        </Flex>
        <Table
          loading={loading}
          className='mt-[20px]'
          columns={[
            {
              title: t('组织名称'),
              dataIndex: 'companyName',
              key: 'companyName',
              render: (text) => <a>{text}</a>,
            },
            {
              title: t('账户'),
              dataIndex: 'accountName',
              key: 'accountName',
            },
            {
              title: t('账户号码'),
              dataIndex: 'tenantAccountNumber',
              key: 'tenantAccountNumber',
            },
            {
              title: t('价值'),
              dataIndex: 'tenantAccountAssets',
              key: 'tenantAccountAssets',
              render: (_, row: any) => {
                const d = (row?.tenantAccountAssets?.filter((v: any) => v?.asset == 'USD')?.map((v: any) => v?.balanceAmount)) || '0'
                return d && d?.length && d || '0'
              }
            },
            {
              title: t('操作'),
              key: 'action',
              render: (_, row) => (//t('操作')
                <Tooltip title={t('注入')} >
                  <span className=' cursor-pointer ' onClick={() => {
                    setActiveData(row)
                    setOpen(true)
                  }}><RocketOutlined /></span>
                </Tooltip>
              ),
            },
          ]}
          dataSource={data}
          rowKey={(row: any) => row.id}
          pagination={{
            hideOnSinglePage: true,
            pageSize: 10,
            current,
            total,
            onChange: pageChange,
            position: ['bottomRight']
          }}
        />
      </Card>
      <Modal
        title={t("注入")}
        open={open}
        onOk={injection}
        onCancel={cancel}
        centered
        footer={null}
        okText={t('确认')}
        cancelText={t("取消")}
      >
        <Form
          name="sign-up-form"
          layout="vertical"
          ref={formRef}
          labelCol={{ span: 24 }}
          wrapperCol={{ span: 24 }}
          autoComplete="off"
          requiredMark={false}
          initialValues={{
            outCurrencyCode: 'USD'
          }}
        >
          <Form.Item
            label={t('资产')}
            name="outCurrencyCode"
            rules={[
              { required: true, message: t('请选择资产') },
            ]}
          >
            <Select options={[
              // { label: 'USDT', value: 'USDT' },
              { label: 'USD', value: 'USD' }
            ]} />
          </Form.Item>
          <Form.Item
            label={t('入金金额')}
            name="inAmount"
            rules={[
              { required: true, message: t('入金金额') },
            ]}
          >
            <Input />
          </Form.Item>
          {/* <Form.Item
            label={t('手续费')}
            name="fee"
            rules={[
              { required: true, message: t('手续费') },
            ]}
          >
            <Input />
          </Form.Item> */}
          <Form.Item
            label={t('汇率')}
            name="exchangeRate"
            rules={[
              { required: true, message: t('汇率') },
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            label={t('备注')}
            name="remarks"
          >
            <Input.TextArea />
          </Form.Item>
          <Flex>
            <Button className='w-full mr-[5px]' shape='round'
              onClick={cancel}
            >{t('取消')}</Button>
            <Button className='w-full ml-[5px]' shape='round' type='primary' loading={submitLoading} onClick={injection}>{t('确认')}</Button>
          </Flex>
        </Form>
      </Modal>
    </div >
  );
};
