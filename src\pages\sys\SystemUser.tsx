import {
    Tooltip, Table, Input,
    TreeSelect,
    Modal, Form, message, Card, Flex, Button, DatePicker
    // Radio,
} from 'antd';
import {
    HomeOutlined,
    // RocketOutlined,
    SearchOutlined
} from '@ant-design/icons';
import { PageHeader } from '@/components';
import { useTranslation } from 'react-i18next';
import { useEffect, useRef, useState } from 'react';
import {
    // getOrganiz,
    getOrganizList,
    getSysUserList,
    postSysUserAdd,
    postSysUserUpdate,
    treeList
} from '@/api/sys'
import { DebounceSelect } from '@/components/DebounceSelect/Index'
import { SYS_ROLE_ENUM } from '@/utils/enum'
import { OrganizationSelect } from '@/components/OrganizationSelect';

const { SHOW_PARENT } = TreeSelect;
const { RangePicker } = DatePicker;

interface SearchParams {
    appid?: string;
    nickname?: string;
    startTime?: number;
    endTime?: number;
}

export const SystemUserPage = () => {
    // const context = useStylesContext();
    const { t } = useTranslation()
    const [current, setCurrent] = useState<number>(1)
    const [total, setTotal] = useState<number>(0)
    const loadingRef = useRef(true)
    const [loading, setLoading] = useState(false)
    const [open, setOpen] = useState(false);
    const [data, setData] = useState([])
    const formRef: any = useRef()
    const [activeData, setActive] = useState<any>();
    const [value, setValue] = useState<any>('');
    const [defaultList, setDefaultList] = useState([])
    const [treeValue, setTreeValue]: any = useState();
    const [treeData, settreeData]: any = useState([])
    const [searchParams, setSearchParams] = useState<SearchParams>({});
    const [dateRange, setDateRange]: any = useState([]);

    useEffect(() => {
        if (loadingRef.current) {
            loadingRef.current = false
            query()
            qyeryDefaultList()
        }
    }, [current])

    const onChange = (newValue: string[]) => {
        console.log('onChange ', newValue);
        setTreeValue(newValue);
    };

    async function fetchUserList(username?: string): Promise<any> {
        // console.log('fetching user', username);
        return new Promise((resolve) => {
            getOrganizList({
                pageNum: 1,
                // pageSize: 20,
                companyName: username || undefined
            }).then((res: any) => {
                if (res.code == 200) {
                    const list = res.data?.map((v: any) => {
                        return {
                            value: v.appid,
                            label: v.companyName
                        }
                    })
                    resolve(list)
                    return
                }
                resolve([])
            })
        })
    };

    const qyeryDefaultList = async () => {
        const list = await fetchUserList()
        setDefaultList(list)
    }

    const pageChange = (page: number) => {
        setCurrent(page)
    }

    const query = (value?: any, type?: string) => {
        if (!loading) {
            getSysUserList({
                pageNum: 1,
                pageSize: 20,
                ...searchParams,
                beginTime: new Date(dateRange[0])?.getTime() || undefined,
                endTime: new Date(dateRange[1])?.getTime() || undefined,
            }).then((res: any) => {
                if (res.code == 200) {
                    setTotal(res.data?.total)
                    setData(res.data?.list || [])
                }
            }).catch(err => {
                console.log(err);
            }).finally(() => {
                setLoading(false)
            })
        }
    }

    const handelGenTenantId = () => {
        return "TN-" + Math.floor(Math.random() * 999999 + 100000);
    }
    const cancel = () => {
        setOpen(false);
        setActive({})
        setValue('')
    }
    const addAccount = () => {
        // cancel()
        let api = postSysUserAdd
        if (activeData?.uid) api = postSysUserUpdate
        formRef.current?.validateFields().then((values: any) => {
            api({
                merchantId: handelGenTenantId(),
                ...values,
                appid: values.appid?.value,
                uid: activeData?.uid || undefined
            }).then((res: any) => {
                const { code } = res
                if (code == 200) {
                    message.open({
                        type: 'success',
                        content: activeData?.uid ? t('修改成功') : t('添加成功')
                    })
                    cancel()
                    query()
                    return
                }
                message.open({
                    type: 'error',
                    content: t(res.message)
                })
            })
        })

    }

    const add = () => {
        setOpen(true);
    }

    const handleTree = (list: any) => {
        return list.map((v: any) => {
            const node: any = {
                value: v.id,
                label: v.label,
                key: v.id,
            };
            if (v.children) {
                node.children = handleTree(v.children);
            }
            return node;
        });
    };

    /** 查询菜单树结构 */
    const getMenuTreeselect = (uid = undefined) => {
        treeList({ uid })
            .then((res: any) => {
                if (res.code == 200) {
                    const { menuTrees, checkedKeys } = res.data;
                    settreeData(handleTree(menuTrees))
                    setTreeValue(checkedKeys)
                    setTimeout(() => {
                        formRef.current?.setFieldValue('menuIds', checkedKeys)
                    }, 100)
                }
            });
    }

    return (
        <div>
            <PageHeader
                title=""
                breadcrumbs={[
                    {
                        title: (
                            <>
                                <HomeOutlined />
                                <span>{t('用户管理')}</span>
                            </>
                        ),

                    },
                ]}
            />
            <Card className='mt-[20px]'>
                <Flex wrap="wrap" gap="small" className="mb-[20px]">
                    <OrganizationSelect
                        allowClear
                        className="w-[200px]"
                        placeholder={t("请选择组织")}
                        onChange={(value) => {
                            setSearchParams(prev => ({ ...prev, appid: value }));
                        }}
                    />
                    <Input
                        allowClear
                        className="w-[200px]"
                        placeholder={t("请输入账号")}
                        onChange={(e) => {
                            setSearchParams(prev => ({ ...prev, nickname: e.target.value }));
                        }}
                        onPressEnter={() => query()}
                    />
                    <RangePicker
                        showTime
                        allowClear
                        className="w-[300px]"
                        onChange={(_, dateStrings) => {
                            setDateRange(dateStrings);
                        }}
                    />
                    <Flex className="ml-auto gap-[10px]">
                        <Button type="primary" onClick={() => query()}>
                            {t("搜索")}
                        </Button>
                        <Button
                            onClick={() => {
                                add();
                                getMenuTreeselect();
                                qyeryDefaultList();
                            }}
                            type="primary"
                        >
                            {t("新增用户")}
                        </Button>
                    </Flex>
                </Flex>
                <Table
                    loading={loading}
                    className='mt-[20px]'
                    rowKey={(row: any) => row.uid}
                    columns={[
                        {
                            title: t('组织名称'),
                            dataIndex: 'companyName',
                            key: 'companyName',
                        },
                        {
                            title: t('组织ID'),
                            dataIndex: 'appid',
                            key: 'appid',
                        },
                        {
                            title: t('账号'),
                            dataIndex: 'nickname',
                            key: 'nickname',
                        },
                        {
                            title: t('角色'),
                            dataIndex: 'roleType',
                            key: 'roleType',
                            render: (_, row: any) => SYS_ROLE_ENUM()[row.roleType]
                        },
                        {
                            title: t('操作'),
                            key: 'action',
                            render: (_, row: any) => (//t('操作')
                                <Tooltip title={t('修改')} >
                                    <span className=' cursor-pointer _primary' onClick={() => {
                                        setActive(row)
                                        getMenuTreeselect(row.uid)
                                        setOpen(true)
                                        setTimeout(() => {
                                            formRef.current?.setFieldsValue({ ...row })
                                        }, 30)
                                    }}>{t('修改')}</span>
                                </Tooltip>
                            ),
                        },
                    ]}
                    dataSource={data}
                    pagination={{
                        hideOnSinglePage: true,
                        pageSize: 10,
                        current,
                        total,
                        onChange: pageChange,
                        position: ['bottomRight']
                    }}
                />
            </Card>
            <Modal
                maskClosable={false}
                keyboard={false}
                title={activeData?.uid ? t("修改用户信息") : t("新增用户")}
                open={open}
                onOk={addAccount}
                onCancel={cancel} 
                okText={t('确认')}
                destroyOnClose={true}
                cancelText={t("取消")}
            >
                <Form
                    ref={formRef}
                    name="sign-up-form"
                    layout="vertical"
                    labelCol={{ span: 24 }}
                    wrapperCol={{ span: 24 }}
                    autoComplete="off"
                    defaultValue={{ ...activeData }}
                    requiredMark={false}
                >
                    <Form.Item
                        label={t('组织名称')}
                        name="appid"
                        rules={[
                            { required: true, message: t('请输入组织名称') },
                        ]}
                    >
                        <DebounceSelect
                            // mode="multiple"
                            value={value}
                            placeholder=""
                            defaultList={defaultList}
                            fetchOptions={fetchUserList}
                            onChange={(newValue) => {
                                setValue(newValue);
                            }}
                            style={{ width: '100%' }}
                        />
                    </Form.Item>
                    <Form.Item
                        label={t('账号')}
                        name="nickname"
                        rules={[
                            { required: true, message: t('账号') },
                        ]}
                    >
                        <Input />
                    </Form.Item>
                    <Form.Item
                        label={t('密码')}
                        name="password"
                        rules={[
                            // { required: true, message: t('密码') },
                        ]}
                    >
                        <Input />
                    </Form.Item>
                    {/* <Form.Item
                        label={t('角色')}
                        name="roleType"
                        rules={[]}
                    > */}
                    {/* <Radio.Group disabled={activeData?.uid}> */}
                    {/* {
                                Object.keys(SYS_ROLE_ENUM()).map((v: any) => {
                                    return <Radio value={v}>{SYS_ROLE_ENUM()[v]}</Radio>
                                })
                            } */}
                    {/* </Radio.Group> */}
                    {/* </Form.Item> */}
                    <Form.Item
                        label={t('API权限')}
                        name="menuIds"
                        rules={[
                            { required: true, message: t('API权限') },
                        ]}
                    >
                        <TreeSelect
                            treeData={treeData}
                            value={treeValue}
                            listHeight={400}
                            onChange={onChange}
                            treeCheckable={true}
                            showCheckedStrategy={SHOW_PARENT}
                            placeholder={t('API权限')}
                            style={{
                                width: '100%',
                            }}
                        />
                    </Form.Item>

                </Form>
            </Modal>
        </div>
    );
};
