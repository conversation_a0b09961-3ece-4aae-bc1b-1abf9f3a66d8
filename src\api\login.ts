import request from './request.ts'

// 登录方法
export function login(data: any) {
    return request({
        url: '/auth/login',
        method: 'post',
        data: data
    })
}

// 退出方法
export function logout() {
    return request({
        url: '/auth/logout',
        method: 'get'
    })
}


// captchaImage
export function captchaImage() {
    return request({
        url: '/captchaImage',
        method: 'get'
    })
}

// getUserInfoWithToken
export function getUserInfoWithToken() {
    return request({
        url: '/user/getUserInfoWithToken',
        method: 'get'
    })
}