import {
  Card,
  Flex,
  Table,
  Input,
  Select,
  message,
  Space,
  Button,
  DatePicker,
} from "antd";
import dayjs from "dayjs"; // 顶部添加导入
import { PageHeader } from "@/components";
import { useTranslation } from "react-i18next";
import { useEffect, useRef, useState } from "react";
import { useNavigate } from "react-router-dom";
import cardAccountSyncApi from "@/api/cardAccountSync";
import { OrganizationSelect } from "@/components/OrganizationSelect";
import type { AxiosResponse } from "axios";

const { RangePicker } = DatePicker;

interface SearchParams {
  appid?: string;
  accountName?: string;
  id?: string;
  tenantAccountNumber?: string;
  beginTime?: string;
  endTime?: string;
  needSync?: number;
}

interface ApiResponse<T = any> extends Partial<AxiosResponse<T>> {
  code: number;
  data: T;
  message?: string;
}

export const CardAccountSyncPage = () => {
  const { t } = useTranslation();
  const [current, setCurrent] = useState<number>(1);
  const [total, setTotal] = useState<number>(0);
  const loadingRef = useRef(true);
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState([]);
  const navigate = useNavigate();
  const formRef: any = useRef();
  const [searchParams, setSearchParams] = useState<SearchParams>({
    needSync: 1, // 设置默认值
  });
  const [dateRange, setDateRange]: any = useState([]);

  useEffect(() => {
    if (loadingRef.current) {
      loadingRef.current = false;
      query();
      qyeryDefaultList();
    }
  }, [current]);

  const pageChange = (page: number) => {
    setCurrent(page);
  };

  const query = () => {
    if (!loading) {
      setLoading(true);
      cardAccountSyncApi
        .listpage({
          pageNum: current,
          pageSize: 10,
          ...searchParams,
          beginTime: dateRange[0]
            ? new Date(dateRange[0]).getTime()
            : undefined,
          endTime: dateRange[1] ? new Date(dateRange[1]).getTime() : undefined,
        })
        .then((response) => {
          const res = response as unknown as ApiResponse;
          if (res.code === 200) {
            setTotal(res.data?.total);
            setData(res.data?.list || []);
          }
        })
        .catch((err) => {
          console.error(err);
        })
        .finally(() => {
          setLoading(false);
          loadingRef.current = true;
        });
    }
  };

  const handelSyncAll = () => {
    if (data.length == 0) {
      message.open({
        type: "error",
        content: "无数据",
      });
      return;
    }
    const accList = data.map((row: any) => {
      return {
        appid: row.accAppid,
        cardAccountId: row.accCardAccountId,
      };
    });
    const params = {
      list: accList,
    };
    setLoading(true);
    cardAccountSyncApi
      .proc(params)
      .then((response) => {
        const res = response as unknown as ApiResponse;
        if (res.code === 200) {
          message.open({
            type: "success",
            content: "处理成功",
          });
        }
      })
      .catch((err) => {
        console.error(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };
  const qyeryDefaultList = async () => {};

  const goProc = (row: any) => {
    const params = {
      list: [
        {
          cardAccountId: row.accCardAccountId,
          appid: row.accAppid,
        },
      ],
    };
    setLoading(true);
    cardAccountSyncApi
      .proc(params)
      .then((response) => {
        const res = response as unknown as ApiResponse;
        if (res.code === 200) {
          message.open({
            type: "success",
            content: "处理成功",
          });
        }
      })
      .catch((err) => {
        console.error(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  return (
    <div className="relative">
      <PageHeader title={t("卡账户同步异常")} breadcrumbs={[]} />

      <Card className="mt-[20px]">
        <Flex wrap="wrap" gap="small" className="mb-[20px]">
          <OrganizationSelect
            allowClear
            className="w-[200px]"
            placeholder={t("请选择组织")}
            onChange={(value) => {
              setSearchParams((prev) => ({
                ...prev,
                appid: value || undefined,
              }));
            }}
          />
          <Select
            allowClear
            className="w-[200px]"
            placeholder={t("请选择同步状态")}
            value={searchParams.needSync}
            onChange={(value) => {
              setSearchParams((prev) => ({
                ...prev,
                needSync: value || undefined, // 清除时传undefined
              }));
            }}
            options={[
              // { value: undefined, label: t("全部") },
              { value: 0, label: t("不需要") },
              { value: 1, label: t("需要同步") },
              { value: 2, label: t("异常需处理") },
            ]}
          />
          {/* <Input
            allowClear
            className="w-[200px]"
            placeholder={t("请输入账户ID")}
            value={searchParams.id}
            onChange={(e) => {
              setSearchParams((prev) => ({ ...prev, id: e.target.value }));
            }}
            onPressEnter={() => {
              query();
            }}
          /> */}
          {/* <RangePicker
            showTime
            allowClear
            className="w-[300px]"
            onChange={(_, dateStrings) => {
              setDateRange(dateStrings as [string, string]);
            }}
          /> */}
          <Flex className="ml-auto gap-[10px]">
            <Button type="primary" onClick={() => query()}>
              {t("搜索")}
            </Button>
            <Button type="primary" onClick={() => handelSyncAll()}>
              {t("全部同步")}
            </Button>
          </Flex>
        </Flex>
        <Table
          loading={loading}
          rowKey={(row: any) => row.id}
          className="mt-[20px]"
          columns={[
            {
              title: t("组织名称"),
              dataIndex: "companyName",
              key: "companyName",
            },

            {
              title: t("账户ID"),
              dataIndex: "accCardAccountId",
              key: "accCardAccountId",
              render: (text) => <span>{text}</span>,
            },
            {
              title: t("账户状态"),
              dataIndex: "status",
              key: "status",
              render: (text: number) => {
                const statusMap = {
                  0: t("frozen"),
                  1: t("active"), 
                };
                return <span>{statusMap[text] || "N/A"}</span>;
              },
            },
            {
              title: t("卡商结余"),
              dataIndex: "balance",
              key: "balance",
              render: (text) => <span>{text}</span>,
            },
            {
              title: t("系统结余"),
              dataIndex: "downBalance",
              key: "downBalance",
              render: (text) => <span>{text}</span>,
            },
            {
              title: t("重试次数"),
              dataIndex: "tryCount",
              key: "tryCount",
              render: (text) => <span>{text}</span>,
            },
            {
              title: t("状态"),
              dataIndex: "syncStatus",
              key: "syncStatus",
              render: (text: number) => {
                const statusMap = {
                  0: t("未执行"),
                  1: t("成功"),
                  2: t("失败"),
                };
                return <span>{statusMap[text] || "N/A"}</span>;
              },
            },
            {
              title: t("最后同步时间"),
              dataIndex: "lastTime",
              key: "lastTime",
              render: (text: number) => (
                <span>
                  {text ? dayjs(text).format("YYYY-MM-DD HH:mm:ss") : "-"}
                </span>
              ),
            },
            {
              title: t("需要同步"),
              dataIndex: "needSync",
              key: "needSync",
              render: (text: number) => {
                const syncMap = {
                  0: t("不需要"),
                  1: t("需要同步"),
                  2: t("异常需处理"),
                };
                return <span>{syncMap[text] || "N/A"}</span>;
              },
            },
            {
              title: t("触发时间"),
              dataIndex: "eventTime",
              key: "eventTime",
              render: (text: number) => (
                <span>
                  {text ? dayjs(text).format("YYYY-MM-DD HH:mm:ss") : "-"}
                </span>
              ),
            },

            {
              title: t("操作"),
              key: "action",
              render: (_, record) => (
                <Space size="middle">
                  <Button
                    type="text"
                    className="_primary"
                    onClick={goProc.bind(this, record)}
                  >
                    {t("同步")}{" "}
                  </Button>
                </Space>
              ),
            },
          ]}
          dataSource={data}
          pagination={{
            hideOnSinglePage: true,
            showSizeChanger: false,
            pageSize: 10,
            current,
            total,
            onChange: pageChange,
            position: ["bottomRight"],
          }}
        />
      </Card>
    </div>
  );
};
