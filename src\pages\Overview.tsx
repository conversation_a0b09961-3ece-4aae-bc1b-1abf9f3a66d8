import {
  Button,
  ButtonProps,
  Col,
  Flex,
  Popover,
  Row,
  Space,
  Tag,
  Typography,
  Card
} from 'antd';
import {
  PageHeader,
} from '@/components';
import { Area, Pie } from '@ant-design/charts';
import {
  ArrowDownOutlined,
  ArrowUpOutlined,
  HomeOutlined,
  QuestionOutlined,
} from '@ant-design/icons';
import { CSSProperties } from 'react';
import { useTranslation } from 'react-i18next';
import CountUp from 'react-countup';
// import { numberWithCommas } from '../../utils';

const { Text, Title } = Typography;

const SalesChart = () => {
  const data = [
    {
      country: 'Online Store',
      date: 'Jan',
      value: 1390.5,
    },
    {
      country: 'Online Store',
      date: 'Feb',
      value: 1469.5,
    },
    {
      country: 'Online Store',
      date: 'Mar',
      value: 1521.7,
    },
    {
      country: 'Online Store',
      date: 'Apr',
      value: 1615.9,
    },
    {
      country: 'Online Store',
      date: 'May',
      value: 1703.7,
    },
    {
      country: 'Online Store',
      date: 'Jun',
      value: 1767.8,
    },
    {
      country: 'Online Store',
      date: 'Jul',
      value: 1806.2,
    },
    {
      country: 'Online Store',
      date: 'Aug',
      value: 1903.5,
    },
    {
      country: 'Online Store',
      date: 'Sept',
      value: 1986.6,
    },
    {
      country: 'Online Store',
      date: 'Oct',
      value: 1952,
    },
    {
      country: 'Online Store',
      date: 'Nov',
      value: 1910.4,
    },
    {
      country: 'Online Store',
      date: 'Dec',
      value: 2015.8,
    },
    {
      country: 'Facebook',
      date: 'Jan',
      value: 109.2,
    },
    {
      country: 'Facebook',
      date: 'Feb',
      value: 115.7,
    },
    {
      country: 'Facebook',
      date: 'Mar',
      value: 120.5,
    },
    {
      country: 'Facebook',
      date: 'Apr',
      value: 128,
    },
    {
      country: 'Facebook',
      date: 'May',
      value: 134.4,
    },
    {
      country: 'Facebook',
      date: 'Jun',
      value: 142.2,
    },
    {
      country: 'Facebook',
      date: 'Jul',
      value: 157.5,
    },
    {
      country: 'Facebook',
      date: 'Aug',
      value: 169.5,
    },
    {
      country: 'Facebook',
      date: 'Sept',
      value: 186.3,
    },
    {
      country: 'Facebook',
      date: 'Oct',
      value: 195.5,
    },
    {
      country: 'Facebook',
      date: 'Nov',
      value: 198,
    },
    {
      country: 'Facebook',
      date: 'Dec',
      value: 211.7,
    },
  ];

  const config = {
    data,
    xField: 'date',
    yField: 'value',
    seriesField: 'country',
    slider: {
      start: 0.1,
      end: 0.9,
    },
  };

  return <Area {...config} />;
};

const CategoriesChart = () => {
  const data = [
    {
      type: 'Appliances',
      value: 27,
    },
    {
      type: 'Electronics',
      value: 25,
    },
    {
      type: 'Clothing',
      value: 18,
    },
    {
      type: 'Shoes',
      value: 15,
    },
    {
      type: 'Food',
      value: 10,
    },
    {
      type: 'Cosmetice',
      value: 5,
    },
  ];

  const config = {
    appendPadding: 10,
    data,
    angleField: 'value',
    colorField: 'type',
    radius: 1,
    innerRadius: 0.5,
    label: {
      type: 'inner',
      offset: '-50%',
      content: '{value}%',
      style: {
        textAlign: 'center',
        fontSize: 16,
      },
    },
    interactions: [
      {
        type: 'element-selected',
      },
      {
        type: 'element-active',
      },
    ],
    statistic: {
      title: false,
      content: {
        style: {
          whiteSpace: 'pre-wrap',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          fontSize: 18,
        },
        content: '18,935\nsales',
      },
    },
  };

  // @ts-ignore
  return <Pie {...config} />;
};

// const CustomerRateChart = () => {
//   const data = [
//     {
//       title: '',
//       ranges: [40, 70, 100],
//       measures: [30, 70],
//       target: 100,
//     },
//   ];
//   const config = {
//     data,
//     measureField: 'measures',
//     rangeField: 'ranges',
//     targetField: 'target',
//     xField: 'title',
//     color: {
//       range: ['#FFbcb8', '#FFe0b0', '#bfeec8'],
//       measure: ['#5B8FF9', '#61DDAA'],
//       target: '#39a3f4',
//     },
//     label: {
//       measure: {
//         position: 'middle',
//         style: {
//           fill: '#fff',
//         },
//       },
//     },
//     xAxis: {
//       line: null,
//     },
//     yAxis: false,
//     tooltip: {
//       showMarkers: false,
//       shared: true,
//     },
//     // customize legend
//     legend: {
//       custom: true,
//       position: 'bottom',
//       items: [
//         {
//           value: 'First time',
//           name: 'First time buying',
//           marker: {
//             symbol: 'square',
//             style: {
//               fill: '#5B8FF9',
//               r: 5,
//             },
//           },
//         },
//         {
//           value: 'Returning',
//           name: 'Returning',
//           marker: {
//             symbol: 'square',
//             style: {
//               fill: '#61DDAA',
//               r: 5,
//             },
//           },
//         },
//       ],
//     },
//   };
//   // @ts-ignore
//   return <Bullet {...config} />;
// };

const OrdersStatusChart = () => {
  const data = [
    {
      type: 'Success',
      value: 27,
    },
    {
      type: 'Pending',
      value: 55,
    },
    {
      type: 'Failed',
      value: 18,
    },
  ];
  const config = {
    appendPadding: 10,
    data,
    angleField: 'value',
    colorField: 'type',
    radius: 0.9,
    label: {
      type: 'inner',
      offset: '-30%',
      content: ({ percent }: any) => `${(percent * 100).toFixed(0)}%`,
      style: {
        fontSize: 14,
        textAlign: 'center',
      },
    },
    interactions: [
      {
        type: 'element-active',
      },
    ],
  };

  // @ts-ignore
  return <Pie {...config} />;
};


const POPOVER_BUTTON_PROPS: ButtonProps = {
  type: 'text',
};

const cardStyles: CSSProperties = {
  height: '100%',
};

export const OverviewPage = () => {
  const { t } = useTranslation()

  return (
    <div>
      <PageHeader
        title={t('总览')}
        breadcrumbs={[
          {
            title: (
              <>
                <HomeOutlined />
                <span>home</span>
              </>
            ),
            path: '/',
          },

        ]}
      />
      <Row gutter={[16, 24]} justify="space-between">
        <Col xs={24} lg={12}>
          <Card
            title="Overall sales"
            extra={
              <Popover content="Total sales over period x" title="Total sales">
                <Button icon={<QuestionOutlined />} {...POPOVER_BUTTON_PROPS} />
              </Popover>
            }
            style={cardStyles}
          >
            <Flex vertical gap="middle">
              <Space>
                <Title level={3} style={{ margin: 0 }}>
                  $ <CountUp end={24485.67} />
                </Title>
                <Tag color="green-inverse" icon={<ArrowUpOutlined />}>
                  8.7%
                </Tag>
              </Space>
              <SalesChart />
            </Flex>
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card
            title="Categories"
            extra={
              <Popover content="Sales per categories" title="Categories sales">
                <Button icon={<QuestionOutlined />} {...POPOVER_BUTTON_PROPS} />
              </Popover>
            }
            style={cardStyles}
          >
            <CategoriesChart />
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card
            title="Orders by status"
            extra={
              <Popover content="Orders by status" title="Orders">
                <Button icon={<QuestionOutlined />} {...POPOVER_BUTTON_PROPS} />
              </Popover>
            }
            style={cardStyles}
          >
            <OrdersStatusChart />
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Flex vertical gap="middle">
            <Card
              title="Conversion rate"
              extra={
                <Popover
                  content="Customer conversion rate"
                  title="Conversion rate"
                >
                  <Button
                    icon={<QuestionOutlined />}
                    {...POPOVER_BUTTON_PROPS}
                  />
                </Popover>
              }
            >
              <Flex vertical gap="middle" justify="center">
                <Typography.Title style={{ margin: 0 }}>8.48%</Typography.Title>
                <Row>
                  <Col sm={24} lg={8}>
                    <Space direction="vertical">
                      <Text>Added to cart</Text>
                      <Text type="secondary">5 visits</Text>
                    </Space>
                  </Col>
                  <Col sm={24} lg={8}>
                    <Text className="text-end" strong>
                      $ <CountUp end={27483.7} decimals={2} />
                    </Text>
                  </Col>
                  <Col sm={24} lg={8}>
                    <Tag color="green-inverse" icon={<ArrowUpOutlined />}>
                      16.8%
                    </Tag>
                  </Col>
                </Row>
                <Row>
                  <Col sm={24} lg={8}>
                    <Space direction="vertical">
                      <Text>Reached to Checkout</Text>
                      <Text type="secondary">23 visits</Text>
                    </Space>
                  </Col>
                  <Col sm={24} lg={8}>
                    <Text className="text-end" strong>
                      $ <CountUp end={145483.7} decimals={2} />
                    </Text>
                  </Col>
                  <Col sm={24} lg={8}>
                    <Tag color="red-inverse" icon={<ArrowDownOutlined />}>
                      -46.8%
                    </Tag>
                  </Col>
                </Row>
              </Flex>
            </Card>
          </Flex>
        </Col>
      </Row>
    </div>
  );
};
