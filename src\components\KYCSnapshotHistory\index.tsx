import React, { useState, useEffect } from 'react';
import { Modal, Table, Button, message } from 'antd';
import { useTranslation } from 'react-i18next';
import { postKYCSnapshotHistory, postKYCSnapshotPreviewImage } from '@/api/kyc';

interface KYCSnapshotHistoryProps {
  open: boolean;
  onClose: () => void;
  kycId: string;
}

export const KYCSnapshotHistory: React.FC<KYCSnapshotHistoryProps> = ({
  open,
  onClose,
  kycId,
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState([]);
  const [detailModalOpen, setDetailModalOpen] = useState(false);
  const [detailData, setDetailData] = useState<any>(null);
  const [frontImageUrl, setFrontImageUrl] = useState<string>('');
  const [backImageUrl, setBackImageUrl] = useState<string>('');
  const [imageLoading, setImageLoading] = useState(false);

  useEffect(() => {
    if (open && kycId) {
      fetchSnapshotHistory();
    }
  }, [open, kycId]);

  const fetchSnapshotHistory = () => {
    setLoading(true);
    postKYCSnapshotHistory({ kycId })
      .then((res: any) => {
        if (res.code === 200) {
          setData(res.data || []);
        } else {
          message.error(t("获取快照记录失败"));
          setData([]);
        }
      })
      .catch((err) => {
        console.error(err);
        message.error(t("获取快照记录失败"));
        setData([]);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const handleClose = () => {
    setData([]);
    onClose();
  };

  const handleViewDetail = (record: any) => {
    // console.log('查看详情，记录数据:', record);
    // console.log('记录ID:', record.id);
    setDetailData(record);
    setDetailModalOpen(true);
    // 加载图片
    loadImages(record.id);
  };

  const loadImages = async (id: string) => {
    if (!id) return;

    setImageLoading(true);
    setFrontImageUrl('');
    setBackImageUrl('');

    try {
      // 加载正面图片
      const frontResponse: any = await postKYCSnapshotPreviewImage({
        id: id,
        imgType: 'front'
      });
      // console.log('正面图片API响应:', frontResponse);

      if (frontResponse.code === 200) {
        // console.log('正面图片数据:', frontResponse.data);
        if (frontResponse.data?.data) {
          setFrontImageUrl(frontResponse.data.data);
          // console.log('设置正面图片URL:', frontResponse.data.data);
        } else {
          // console.log('正面图片数据为空');
        }
      } else {
        // console.log('正面图片API返回错误:', frontResponse.code);
      }
    } catch (error) {
      console.error('加载正面图片失败:', error);
    }

    try {
      // 加载背面图片
      const backResponse: any = await postKYCSnapshotPreviewImage({
        id: id,
        imgType: 'back'
      });
      // console.log('背面图片API响应:', backResponse);

      if (backResponse.code === 200) {
        // console.log('背面图片数据:', backResponse.data);
        if (backResponse.data?.data) {
          setBackImageUrl(backResponse.data.data);
          // console.log('设置背面图片URL:', backResponse.data.data);
        } else {
          // console.log('背面图片数据为空');
        }
      } else {
        // console.log('背面图片API返回错误:', backResponse.code);
      }
    } catch (error) {
      console.error('加载背面图片失败:', error);
    }

    setImageLoading(false);
  };

  const handleCloseDetail = () => {
    setDetailModalOpen(false);
    setDetailData(null);
    setFrontImageUrl('');
    setBackImageUrl('');
    setImageLoading(false);
  };

  const getAuditStatusText = (status: number) => {
    const statusMap: { [key: number]: string } = {
      0: t("待处理"),
      1: t("通过"),
      2: t("拒绝")
    };
    return statusMap[status] || status;
  };

  const getDocumentTypeText = (type: string) => {
    const typeMap: { [key: string]: string } = {
      'passport': t("护照"),
      'drivers-license': t("驾照"),
      'national-id': t("国民身份证")
    };
    return typeMap[type] || type;
  };



  const columns = [
    {
      title: t("ID"),
      dataIndex: "id",
      key: "id",
      width: 80,
      // fixed: "left" as const,
    },
    {
      title: t("KYC资料ID"),
      dataIndex: "kycInfoId",
      key: "kycInfoId",
      width: 120,
      ellipsis: true,
    },
    {
      title: t("审核表ID"),
      dataIndex: "kycAuditId",
      key: "kycAuditId",
      width: 120,
      ellipsis: true,
    },
    {
      title: t("应用ID"),
      dataIndex: "appid",
      key: "appid",
      width: 100,
    },
    {
      title: t("用户名"),
      dataIndex: "username",
      key: "username",
      width: 100,
    },
    {
      title: t("邮箱"),
      dataIndex: "email",
      key: "email",
      width: 150,
      ellipsis: true,
    },
    {
      title: t("电话"),
      dataIndex: "countryCode",
      key: "phone",
      width: 120,
      render: (_: any, row: any) => {
        return `${row.countryCode} ${row.mobileNumber}`;
      },
    },
    {
      title: t("身份类型"),
      dataIndex: "identityType",
      key: "identityType",
      width: 100,
    },
    {
      title: t("账户类型"),
      dataIndex: "accountType",
      key: "accountType",
      width: 100,
    },
    {
      title: t("名"),
      dataIndex: "firstName",
      key: "firstName",
      width: 100,
    },
    {
      title: t("姓"),
      dataIndex: "lastName",
      key: "lastName",
      width: 100,
    },
    {
      title: t("名(Local)"),
      dataIndex: "firstNameLocal",
      key: "firstNameLocal",
      width: 100,
    },
    {
      title: t("姓(Local)"),
      dataIndex: "lastNameLocal",
      key: "lastNameLocal",
      width: 100,
    },
    {
      title: t("出生日期"),
      dataIndex: "dateOfBirth",
      key: "dateOfBirth",
      width: 100,
    },
    {
      title: t("年收入"),
      dataIndex: "annualIncome",
      key: "annualIncome",
      width: 100,
    },
    {
      title: t("职业"),
      dataIndex: "occupation",
      key: "occupation",
      width: 100,
    },
    {
      title: t("职位"),
      dataIndex: "position",
      key: "position",
      width: 100,
    },
    {
      title: t("地址"),
      dataIndex: "address",
      key: "address",
      width: 150,
      ellipsis: true,
    },
    {
      title: t("证件类型"),
      dataIndex: "type",
      key: "type",
      width: 100,
      render: (type: string) => getDocumentTypeText(type),
    },
    {
      title: t("证件号码"),
      dataIndex: "number",
      key: "number",
      width: 120,
      ellipsis: true,
    },
    {
      title: t("国家"),
      dataIndex: "country",
      key: "country",
      width: 80,
    },
    {
      title: t("证件有效期"),
      dataIndex: "expiryDate",
      key: "expiryDate",
      width: 100,
    },
    {
      title: t("添加时间"),
      dataIndex: "addTime",
      key: "addTime",
      width: 160,
      render: (timestamp: number) => {
        return timestamp ? new Date(timestamp).toLocaleString() : '-';
      },
    },
    {
      title: t("状态"),
      dataIndex: "status",
      key: "status",
      width: 100,
      render: (status: number) => {
        return status !== undefined ? getAuditStatusText(status) : '-';
      },
    },
    {
      title: t("审核人"),
      dataIndex: "auditorUsername",
      key: "auditorUsername",
      width: 100,
    },
    {
      title: t("审核时间"),
      dataIndex: "auditTime",
      key: "auditTime",
      width: 160,
      render: (timestamp: number) => {
        return timestamp ? new Date(timestamp).toLocaleString() : '-';
      },
    },
    {
      title: t("审核结果"),
      dataIndex: "auditStatus",
      key: "auditStatus",
      width: 100,
      fixed: "right" as const,
      render: (status: number) => {
        return getAuditStatusText(status);
      },
    },
    {
      title: t("原因"),
      dataIndex: "reason",
      key: "reason",
      width: 150,
      ellipsis: true,
      render: (text: string) => text || '-',
    },
    {
      title: t("操作"),
      key: "action",
      width: 80,
      fixed: "right" as const,
      render: (_: any, record: any) => (
        <Button
          type="text"
          className="_primary"
          size="small"
          onClick={() => handleViewDetail(record)}
        >
          {t("详情")}
        </Button>
      ),
    },
  ];

  return (
    <>
      <Modal
        title={t("快照记录")}
        open={open}
        onCancel={handleClose}
        footer={[
          <Button key="close" onClick={handleClose}>
            {t("关闭")}
          </Button>
        ]}
        width="80%"
        centered
        destroyOnClose
      >
        <Table
          loading={loading}
          rowKey={(row: any) => row.id}
          dataSource={data}
          pagination={false}
          size="small"
          scroll={{ x: 1000 }}
          columns={columns}
        />
      </Modal>

      {/* 详情弹窗 */}
      <Modal
        title={t("快照详情")}
        open={detailModalOpen}
        onCancel={handleCloseDetail}
        footer={[
          <Button key="close" onClick={handleCloseDetail}>
            {t("关闭")}
          </Button>
        ]}
        width={800}
        centered
        destroyOnClose
      >
        {detailData && (
          <div style={{ maxHeight: '60vh', overflowY: 'auto' }}>
            {/* 基本信息 */}
            <div style={{ marginBottom: '24px' }}>
              <h3 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px' }}>{t("基本信息")}</h3>
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '12px' }}>
                <div>
                  <span style={{ fontWeight: 'bold' }}>{t("ID")}:</span>
                  <span style={{ marginLeft: '8px' }}>{detailData.id}</span>
                </div>
                <div>
                  <span style={{ fontWeight: 'bold' }}>{t("KYC资料ID")}:</span>
                  <span style={{ marginLeft: '8px' }}>{detailData.kycInfoId}</span>
                </div>
                <div>
                  <span style={{ fontWeight: 'bold' }}>{t("审核表ID")}:</span>
                  <span style={{ marginLeft: '8px' }}>{detailData.kycAuditId}</span>
                </div>
                <div>
                  <span style={{ fontWeight: 'bold' }}>{t("应用ID")}:</span>
                  <span style={{ marginLeft: '8px' }}>{detailData.appid}</span>
                </div>
                <div>
                  <span style={{ fontWeight: 'bold' }}>{t("用户名")}:</span>
                  <span style={{ marginLeft: '8px' }}>{detailData.username}</span>
                </div>
                <div>
                  <span style={{ fontWeight: 'bold' }}>{t("邮箱")}:</span>
                  <span style={{ marginLeft: '8px' }}>{detailData.email}</span>
                </div>
                <div>
                  <span style={{ fontWeight: 'bold' }}>{t("电话")}:</span>
                  <span style={{ marginLeft: '8px' }}>{detailData.countryCode} {detailData.mobileNumber}</span>
                </div>
                <div>
                  <span style={{ fontWeight: 'bold' }}>{t("身份类型")}:</span>
                  <span style={{ marginLeft: '8px' }}>{detailData.identityType}</span>
                </div>
                <div>
                  <span style={{ fontWeight: 'bold' }}>{t("账户类型")}:</span>
                  <span style={{ marginLeft: '8px' }}>{detailData.accountType}</span>
                </div>
              </div>
            </div>

            {/* 个人信息 */}
            <div style={{ marginBottom: '24px' }}>
              <h3 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px' }}>{t("个人信息")}</h3>
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '12px' }}>
                <div>
                  <span style={{ fontWeight: 'bold' }}>{t("名")}:</span>
                  <span style={{ marginLeft: '8px' }}>{detailData.firstName}</span>
                </div>
                <div>
                  <span style={{ fontWeight: 'bold' }}>{t("姓")}:</span>
                  <span style={{ marginLeft: '8px' }}>{detailData.lastName}</span>
                </div>
                <div>
                  <span style={{ fontWeight: 'bold' }}>{t("名(Local)")}:</span>
                  <span style={{ marginLeft: '8px' }}>{detailData.firstNameLocal || '-'}</span>
                </div>
                <div>
                  <span style={{ fontWeight: 'bold' }}>{t("姓(Local)")}:</span>
                  <span style={{ marginLeft: '8px' }}>{detailData.lastNameLocal || '-'}</span>
                </div>
                <div>
                  <span style={{ fontWeight: 'bold' }}>{t("出生日期")}:</span>
                  <span style={{ marginLeft: '8px' }}>{detailData.dateOfBirth}</span>
                </div>
                <div>
                  <span style={{ fontWeight: 'bold' }}>{t("年收入")}:</span>
                  <span style={{ marginLeft: '8px' }}>{detailData.annualIncome}</span>
                </div>
                <div>
                  <span style={{ fontWeight: 'bold' }}>{t("职业")}:</span>
                  <span style={{ marginLeft: '8px' }}>{detailData.occupation}</span>
                </div>
                <div>
                  <span style={{ fontWeight: 'bold' }}>{t("职位")}:</span>
                  <span style={{ marginLeft: '8px' }}>{detailData.position}</span>
                </div>
                <div style={{ gridColumn: 'span 2' }}>
                  <span style={{ fontWeight: 'bold' }}>{t("地址")}:</span>
                  <span style={{ marginLeft: '8px' }}>{detailData.address}</span>
                </div>
              </div>
            </div>

            {/* 证件信息 */}
            <div style={{ marginBottom: '24px' }}>
              <h3 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px' }}>{t("证件信息")}</h3>
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '12px' }}>
                <div>
                  <span style={{ fontWeight: 'bold' }}>{t("证件类型")}:</span>
                  <span style={{ marginLeft: '8px' }}>{getDocumentTypeText(detailData.type)}</span>
                </div>
                <div>
                  <span style={{ fontWeight: 'bold' }}>{t("证件号码")}:</span>
                  <span style={{ marginLeft: '8px' }}>{detailData.number}</span>
                </div>
                <div>
                  <span style={{ fontWeight: 'bold' }}>{t("国家")}:</span>
                  <span style={{ marginLeft: '8px' }}>{detailData.country}</span>
                </div>
                <div>
                  <span style={{ fontWeight: 'bold' }}>{t("证件有效期")}:</span>
                  <span style={{ marginLeft: '8px' }}>{detailData.expiryDate}</span>
                </div>
              </div>

              {/* 证件图片 */}
              <div style={{ marginTop: '16px' }}>
                <h4 style={{ fontWeight: 'bold', marginBottom: '8px' }}>{t("证件图片")}</h4>
                {imageLoading ? (
                  <div style={{ textAlign: 'center', padding: '20px' }}>
                    <span>{t("加载中...")}</span>
                  </div>
                ) : (
                  <div style={{ display: 'flex', gap: '16px' }}>
                    {frontImageUrl && (
                      <div>
                        <p style={{ fontSize: '12px', color: '#666', marginBottom: '4px' }}>{t("正面")}</p>
                        <img
                          src={frontImageUrl}
                          alt={t("证件正面")}
                          style={{ width: '128px', height: '80px', objectFit: 'cover', border: '1px solid #d9d9d9', borderRadius: '4px' }}
                        />
                      </div>
                    )}
                    {backImageUrl && (
                      <div>
                        <p style={{ fontSize: '12px', color: '#666', marginBottom: '4px' }}>{t("背面")}</p>
                        <img
                          src={backImageUrl}
                          alt={t("证件背面")}
                          style={{ width: '128px', height: '80px', objectFit: 'cover', border: '1px solid #d9d9d9', borderRadius: '4px' }}
                        />
                      </div>
                    )}
                    {!frontImageUrl && !backImageUrl && !imageLoading && (
                      <div style={{ color: '#999', fontStyle: 'italic' }}>
                        {t("暂无图片")}
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>

            {/* 审核信息 */}
            <div>
              <h3 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px' }}>{t("审核信息")}</h3>
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '12px' }}>
                <div>
                  <span style={{ fontWeight: 'bold' }}>{t("添加时间")}:</span>
                  <span style={{ marginLeft: '8px' }}>{detailData.addTime ? new Date(detailData.addTime).toLocaleString() : '-'}</span>
                </div>
                <div>
                  <span style={{ fontWeight: 'bold' }}>{t("状态")}:</span>
                  <span style={{ marginLeft: '8px' }}>{detailData.status !== undefined ? getAuditStatusText(detailData.status) : '-'}</span>
                </div>
                <div>
                  <span style={{ fontWeight: 'bold' }}>{t("审核人")}:</span>
                  <span style={{ marginLeft: '8px' }}>{detailData.auditorUsername || '-'}</span>
                </div>
                <div>
                  <span style={{ fontWeight: 'bold' }}>{t("审核时间")}:</span>
                  <span style={{ marginLeft: '8px' }}>{detailData.auditTime ? new Date(detailData.auditTime).toLocaleString() : '-'}</span>
                </div>
                <div>
                  <span style={{ fontWeight: 'bold' }}>{t("审核结果")}:</span>
                  <span style={{ marginLeft: '8px' }}>{getAuditStatusText(detailData.auditStatus)}</span>
                </div>
                <div style={{ gridColumn: 'span 2' }}>
                  <span style={{ fontWeight: 'bold' }}>{t("原因")}:</span>
                  <span style={{ marginLeft: '8px' }}>{detailData.reason || '-'}</span>
                </div>
              </div>
            </div>
          </div>
        )}
      </Modal>
    </>
  );
};
