import { message } from 'antd';
import { CopyOutlined } from '@ant-design/icons';
import { Button } from 'antd';

interface CopyToClipboardProps {
  text: string;
}

export const CopyToClipboard = ({ text }: CopyToClipboardProps) => {
  const handleCopy = () => {
    navigator.clipboard.writeText(text);
    message.success('Copied to clipboard');
  };

  return (
    <Button 
      type="text" 
      icon={<CopyOutlined />} 
      onClick={handleCopy}
    />
  );
};
