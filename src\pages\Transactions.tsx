import {
  Flex, Table, Space, Button,
  Modal, Form, Select, DatePicker, Segmented,
  Config<PERSON><PERSON><PERSON>, Drawer, Card, Checkbox, Row, Col,
  Input
} from 'antd';
import { PageHeader } from '@/components';
import { useTranslation } from 'react-i18next';
import { useEffect, useRef, useState } from 'react';
import { queryTXList } from '@/api/transactions'
import {
  EVENT_FEEENUM, TRANSACTION_ENUM,
  TRANSACTION_STATUS_ENUM, FEETYPE_ENUM,
  LEVE_ENUM,
} from '@/utils/enum'
import { OrganizationSelect } from '@/components/OrganizationSelect';
const { RangePicker } = DatePicker;

interface SearchParams {
  appid?: string;
  currency?: string;
  [key: string]: string | undefined;  // Allow additional string keys with string or undefined values
}

export const TransactionsPage = () => {
  let defalutCheckedList = []
  localStorage.getItem('checkedList') && (defalutCheckedList = (JSON.parse(localStorage.getItem('checkedList') || '[]')))
  const { t } = useTranslation()
  const [current, setCurrent] = useState<number>(1)
  const [total, setTotal] = useState<number>(0)
  const loadingRef = useRef(true)
  const [loading, setLoading] = useState(false)
  // const [open, setOpen] = useState(false);
  const [data, setData]: any = useState([])
  const [isDetails, setIsDetails] = useState(false)
  const [downloadShow, setDownloadShow] = useState(false);
  const [active, setActive] = useState('all')
  const [updTime, setUpdTime] = useState(undefined)
  const [creTime, setCreTime] = useState(undefined)
  const [postedAt, setPostedAt] = useState(undefined)
  const [createdAt, setCreatedAt] = useState(undefined)
  const [status, setStatus] = useState(undefined)
  const [newBalance, setNewBalance] = useState(undefined)
  const [oldBalance, setOldBalance] = useState(undefined)
  const [level, setLevel] = useState(undefined)
  const [event, setEvent] = useState(undefined)

  const [amount, setAmount] = useState(undefined)
  const [id, setId] = useState(undefined)
  const [activeData, setActiveData] = useState<any>({})
  const [checkedList, setCheckedList]: any = useState(defalutCheckedList)
  const [forwardOrBackward, setForwardOrBackward] = useState(1)
  const [cardEmbossedName, setCardEmbossedName] = useState(undefined)
  const [timeValue, setTimeValue]: any = useState([])
  const formRef = useRef<any>(null)

  const [nextPageToken, setNextPageToken] = useState( );
  const [prevPageToken, setPrevPageToken] = useState( );
  const [pageToken, setPageToken] = useState( );
  const [queryPageSize, setQueryPageSize] = useState(10);
  const [searchParams, setSearchParams] = useState<SearchParams>({});

  useEffect(() => {
    if (loadingRef.current) {
      loadingRef.current = false;
      setQueryPageSize(10);
      query();
    }
  }, [
    current, active, updTime, event,
    creTime, postedAt, createdAt, status, newBalance,
    oldBalance, level, amount, id,
  ])

  useEffect(() => {
    localStorage.setItem('checkedList', JSON.stringify(checkedList))
  }, [checkedList])

  const pageChange = (page: number, pageSize: number) => {
    console.log("pageSize", pageSize);
    setQueryPageSize(pageSize); // 更新 pageSize
    if (page > current) {//向后
      // setForwardOrBackward(1)
      // setCardEmbossedName(data[data.length - 1]?.id || '')
      setPageToken(nextPageToken);
      console.log('next');
    } else {
      // setForwardOrBackward(0)
      // setCardEmbossedName(data[0]?.id || '')
      setPageToken(prevPageToken);
      console.log('prev');
    }
    if (current == 1) {
      // setCardEmbossedName(un)
    }
    setTimeout(() => {
      setCurrent(page);

    }, 16)
  }

  const query = () => {
    setLoading(true)
    const storeObj: any = {};
    [updTime, creTime, postedAt, createdAt, status, newBalance, oldBalance, level, amount, id,
      event,
    ].filter(item => item).forEach((item: any) => {
      storeObj[item] = 1
    })

    const formParams = formRef.current.getFieldsValue()
    Object.keys(formParams).forEach((key: any) => {
      !formParams[key] ? formParams[key] = undefined : ''
    })
    delete formParams.timer
    queryTXList({
      // pageNum: current,
      pageToken:pageToken,
      pageSize: queryPageSize,
      // forwardOrBackward,
      //@ts-ignore
      postedAtStart: timeValue?.length && timeValue[0]['$d']?.getTime() || undefined,
      //@ts-ignore
      postedAtEnd: timeValue?.length && timeValue[1]['$d']?.getTime() || undefined,
      id: cardEmbossedName,
      event: active == 'all' ? undefined : active,
      ...searchParams,
      ...storeObj,
      ...formParams
    }).then((res: any) => {
      if (res.code == 200) {
        setTotal(res.data.total)
        setData(res?.data?.list || [])
        setNextPageToken(res.data.nextPageToken|| '');
        setPrevPageToken(res.data.prevPageToken|| '');
      }
    }).catch(err => {
      console.log(err);
    }).finally(() => {
      setLoading(false)
      loadingRef.current = true
    })
  }


  const onClose = () => {
    setIsDetails(false)
  };

  const download = () => {
    close()
  }
  const close = () => {
    setDownloadShow(false)
  }


  const goDetails = (row: any) => {
    setIsDetails(true)
    setActiveData(row)
  }

  const handleTableChange = (__: any, _: any, sorter: any) => {
    setId(undefined);
    setUpdTime(undefined);
    setCreTime(undefined);
    setPostedAt(undefined);
    setCreatedAt(undefined);
    setStatus(undefined);
    setNewBalance(undefined);
    setOldBalance(undefined);
    setLevel(undefined);
    setAmount(undefined);
    setEvent(undefined);
    if (sorter) {
      if (Array.isArray(sorter)) {
        return sorter.forEach((item: any) => {
          setValueFn(item)
        })
      }
      setValueFn(sorter)
    }
  }

  const setValueFn = (sorter: any) => {
    setCurrent(1)
    setCardEmbossedName(undefined)
    setForwardOrBackward(1)
    switch (sorter.field) {
      case 'id':
        setId(sorter.order && (sorter.order == 'ascend' ? 'idAsc' : 'idDesc') || undefined); break;
      case 'updTime':
        setUpdTime(sorter.order && (sorter.order == 'ascend' ? 'updTimeAsc' : 'updTimeDesc') || undefined); break;
      case 'creTime':
        setCreTime(sorter.order && (sorter.order == 'ascend' ? 'creTimeAsc' : 'creTimeDesc') || undefined); break;
      case 'postedAt':
        setPostedAt(sorter.order && (sorter.order == 'ascend' ? 'postedAtAsc' : 'postedAtDesc') || undefined); break;
      case 'createdAt':
        setCreatedAt(sorter.order && (sorter.order == 'ascend' ? 'createdAtAsc' : 'createdAtDesc') || undefined); break;
      case 'status':
        setStatus(sorter.order && (sorter.order == 'ascend' ? 'statusAsc' : 'statusDesc') || undefined); break;
      case 'newBalance':
        setNewBalance(sorter.order && (sorter.order == 'ascend' ? 'newBalanceAsc' : 'newBalanceDesc') || undefined); break;
      case 'oldBalance':
        setOldBalance(sorter.order && (sorter.order == 'ascend' ? 'oldBalanceAsc' : 'oldBalanceDesc') || undefined); break;
      case 'level':
        setLevel(sorter.order && (sorter.order == 'ascend' ? 'levelAsc' : 'levelDesc') || undefined); break;
      case 'amount':
        setAmount(sorter.order && (sorter.order == 'ascend' ? 'amountAsc' : 'amountDesc') || undefined); break;
      case 'event':
        setEvent(sorter.order && (sorter.order == 'ascend' ? 'eventAsc' : 'eventDesc') || undefined); break;
      default: break;
    }
  }

  const SegmeChange = (item: any) => {
    setCurrent(1)
    setCardEmbossedName(undefined)
    setForwardOrBackward(1)
    setActive(item)
  }

  const querySearch = () => {
    setPageToken(undefined)
    current == 1 ? query() : setCurrent(1)
  }

  return (
    <div >
      <PageHeader
        title={t('我的交易记录')}
        breadcrumbs={[]}
      />

      <Card className=' '>
        <Flex justify='space-between' align='center'>
          <ConfigProvider theme={{
            token: {
              borderRadius: 20,
              borderRadiusLG: 20,
              borderRadiusSM: 20,
              borderRadiusXS: 20,
            },
            components: {
              Segmented: {
                itemSelectedBg: '#274673',
                itemSelectedColor: '#fff',
                itemColor: '#fff',
              }
            }
          }}>

            <Segmented options={TRANSACTION_ENUM()}
              value={active}
              size='small'
              onChange={SegmeChange}
              className='h-[50px]
              p-[8px] rounded-[50px] bg-[#efefef] _Segmented'
            />
          </ConfigProvider>

          <Button onClick={() => setDownloadShow(true)}
            shape="round"
            disabled
            style={{ backgroundImage: 'linear-gradient(90deg, rgb(243, 227, 180), rgb(227, 179, 84))' }}
            className=' '> {t('下载交易报告')}</Button>
        </Flex>
        <Form
          className='mt-[20px]'
          ref={formRef}
          name="sign-up-form"
          layout="horizontal"
          autoComplete="off"
          // labelCol={{ flex: '120px' }}
          labelWrap
        >
          <Row gutter={20} >
            <Col xs={{ span: 8 }}
              sm={{ span: 8 }}
              md={{ span: 8 }}
              lg={{ span: 6 }}
              xl={{ span: 6 }}
            >
              <Form.Item
                label={t('交易时间')}
                name="timer">
                <RangePicker
                  showTime
                  allowClear
                  onChange={(e: any) => { setTimeValue(e) }}
                />
              </Form.Item>

            </Col>
            <Col xs={{ span: 8 }}
              sm={{ span: 8 }}
              md={{ span: 8 }}
              lg={{ span: 6 }}
              xl={{ span: 6 }}>
              <Form.Item
                label={t('账户ID')}
                name="accAccountId" >

                <Input allowClear placeholder={t('账户ID')} />
              </Form.Item>
            </Col>
            <Col xs={{ span: 8 }}
              sm={{ span: 8 }}
              md={{ span: 8 }}
              lg={{ span: 6 }}
              xl={{ span: 6 }}>
              <Form.Item
                label={t('卡账户ID')}
                name="accCardAccountId" >
                <Input allowClear placeholder={t('卡账户ID')} />
              </Form.Item>
            </Col>
            <Col xs={{ span: 8 }}
              sm={{ span: 8 }}
              md={{ span: 8 }}
              lg={{ span: 6 }}
              xl={{ span: 6 }}>
              <Form.Item
                label={t('卡ID')}
                name="accCardId" >
                <Input allowClear placeholder={t('卡ID')} />
              </Form.Item>
            </Col>
            <Col xs={{ span: 8 }}
              sm={{ span: 8 }}
              md={{ span: 8 }}
              lg={{ span: 6 }}
              xl={{ span: 6 }}>
              <Form.Item
                label={t('发起人的层级')}
                name="level" >
                <Select className='w-[300px]'
                  placeholder={t('发起人的层级')}
                  allowClear
                  options={Object.keys(LEVE_ENUM()).map((v: any) => {
                    return { label: LEVE_ENUM()[v], value: v }
                  })}
                />
              </Form.Item>
            </Col>
            <Col xs={{ span: 8 }}
              sm={{ span: 8 }}
              md={{ span: 8 }}
              lg={{ span: 6 }}
              xl={{ span: 6 }}>
              <Form.Item
                label={t('交易状态')}
                name="status" >
                <Select className='w-[300px]'
                  placeholder={t('交易状态')}
                  allowClear
                  options={Object.keys(TRANSACTION_STATUS_ENUM()).map((v: any) => {
                    return { label: TRANSACTION_STATUS_ENUM()[v], value: v }
                  })}
                />
              </Form.Item>
            </Col>
            <Col xs={{ span: 8 }}
              sm={{ span: 8 }}
              md={{ span: 8 }}
              lg={{ span: 6 }}
              xl={{ span: 6 }}>
              <Form.Item
                label={t('组织')}
                name="appid"
              >
                <OrganizationSelect
                  placeholder={t("请选择组织")}
                  onChange={(value: string | undefined) => {
                    setSearchParams(prev => ({ 
                      ...prev, 
                      appid: value || undefined 
                    }));
                  }}
                />
              </Form.Item>
            </Col>
            <Col xs={{ span: 8 }}
              sm={{ span: 8 }}
              md={{ span: 8 }}
              lg={{ span: 6 }}
              xl={{ span: 6 }}>
              <Form.Item
                label={t('货币')}
                name="currency" >
                {/* <Select
                  allowClear
                  placeholder={t("请选择货币")}
                  onChange={(value) => {
                    setSearchParams(prev => ({ ...prev, currency: value }));
                  }}
                  options={[
                    { label: 'USDT', value: 'USDT' }
                  ]}
                /> */}
                <Input allowClear placeholder={t('货币')} />
              </Form.Item>
            </Col>
            <Col>
              <Button onClick={querySearch} className='ml-[10px]'>{t('搜索')}</Button>
            </Col>
          </Row>
        </Form>



        <Table
          loading={loading}
          className='mt-[20px] _store_table'
          rowKey={(row: any) => row?.id}
          scroll={{ x: 1000 }}
          onChange={handleTableChange}
          columns={[
            {
              title: t('ID'),
              dataIndex: 'id',
              key: 'id',
              width: 100,
              sorter: { multiple: 1 },
              filteredValue: checkedList,
              filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
                <div className='p-[20px] bg-[#fff] w-[600px]'>
                  <div>{t("请选择需要隐藏的字段")}</div>
                  <Checkbox.Group
                    options={[
                      { label: t('数据id'), value: 'dataId' },
                      { label: t('唯一key'), value: 'idempotencyKey' },
                      { label: t('账户id'), value: 'accAccountId' },
                      { label: t('卡账户ID'), value: 'cardAccountId' },
                      { label: t('卡id'), value: 'accCardId' },
                      { label: t('事件ID'), value: 'eventId' },
                      { label: t('事件'), value: 'event' },
                      { label: t('链'), value: 'chain' },
                      { label: t('块高'), value: 'blockNumber' },
                      { label: t('交易货币'), value: 'currency' },
                      { label: t('交易金额'), value: 'amount' },
                      { label: t('发起人的层级'), value: 'level' },
                      { label: t('旧余额'), value: 'oldBalance' },
                      { label: t('新余额'), value: 'newBalance' },
                      { label: t('卡帐户交易状态'), value: 'status' },
                      { label: t('入账日期'), value: 'createdAt' },
                      { label: t('到账日期'), value: 'postedAt' },
                      { label: t('创建时间'), value: 'creTime' },
                      { label: t('更新时间'), value: 'updTime' },
                    ]}
                    value={selectedKeys}
                    onChange={(e) => {
                      setSelectedKeys(e)
                    }}
                  />
                  <div style={{ marginTop: 8 }}>
                    <Button
                      type="primary"
                      className='mr-[10px]'
                      onClick={() => {
                        confirm();
                        setCheckedList(selectedKeys)
                      }}
                      size="small"
                      style={{ width: 90 }}
                    >
                      {t('确定')}
                    </Button>
                    <Button
                      onClick={() => {
                        clearFilters && clearFilters()
                        setSelectedKeys([])
                      }}
                      size="small"
                      style={{ width: 90 }}
                    >
                      {t('重置')}
                    </Button>
                  </div>
                </div>)
            },
            {
              title: t('数据ID'),
              dataIndex: 'dataId',
              width: 130,
              key: 'dataId',
              ellipsis: true,
              hidden: checkedList.includes('dataId'),
            },
            {
              title: t('唯一KEY'),
              dataIndex: 'idempotencyKey',
              key: 'idempotencyKey',
              width: 130,
              ellipsis: true,
              hidden: checkedList.includes('idempotencyKey'),
            },
            {
              title: t('账户ID'),
              dataIndex: 'accAccountId',
              width: 130,
              ellipsis: true,
              key: 'accAccountId',
              hidden: checkedList.includes('accAccountId'),
            },
            {
              title: t('卡账户ID'),
              width: 130,
              dataIndex: 'accCardAccountId',
              key: 'accCardAccountId',
              ellipsis: true,
              hidden: checkedList.includes('accCardAccountId'),
            },
            {
              title: t('卡ID'),
              dataIndex: 'accCardId',
              ellipsis: true,
              width: 130,
              key: 'accCardId',
              hidden: checkedList.includes('accCardId'),
            },
            {
              title: t('事件ID'),
              dataIndex: 'eventId',
              width: 130,
              key: 'eventId',
              ellipsis: true,
              hidden: checkedList.includes('eventId'),
            },
            {
              title: t('事件'),
              width: 130,
              dataIndex: 'event',
              key: 'event',
              ellipsis: true,
              sorter: { multiple: 10 }, //eventAsc  eventDesc
              hidden: checkedList.includes('event'),
              render: (text: any) => EVENT_FEEENUM()[text] || '-'
            },
            // {
            //   title: t('链'),
            //   width: 130,
            //   dataIndex: 'chain',
            //   key: 'chain',
            //   ellipsis: true,
            //   hidden: checkedList.includes('chain'),
            // },
            // {
            //   title: t('块高'),
            //   width: 130,
            //   dataIndex: 'blockNumber',
            //   key: 'blockNumber',
            //   ellipsis: true,
            //   sorter: { multiple: 10 }, //blockNumberAsc  blockNumberDesc
            //   hidden: checkedList.includes('blockNumber'),
            // },
            {
              title: t('交易货币'),
              width: 130,
              dataIndex: 'currency',
              key: 'currency',
              ellipsis: true,
              hidden: checkedList.includes('currency'),
            },
            {
              title: t('交易金额'),
              width: 130,
              dataIndex: 'amount',
              key: 'amount',
              ellipsis: true,
              hidden: checkedList.includes('amount'),
              sorter: { multiple: 2 }, //amountAsc  amountDesc
            },
            {
              title: t('旧余额'),
              width: 130,
              dataIndex: 'oldBalance',
              key: 'oldBalance',
              ellipsis: true,
              sorter: { multiple: 4 }, //oldBalanceAsc  oldBalanceDesc
            },
            {
              title: t('新余额'),
              dataIndex: 'newBalance',
              width: 130,
              key: 'newBalance',
              ellipsis: true,
              hidden: checkedList.includes('newBalance'),
              sorter: { multiple: 5 }, //newBalanceAsc  newBalanceDesc
            },

            {
              title: t('交易状态'),//待处理、已过账、拒绝、无效
              dataIndex: 'status',
              width: 130,
              ellipsis: true,
              key: 'status',
              hidden: checkedList.includes('status'),
              sorter: { multiple: 6 }, //statusAsc  statusDesc
              render: (text: any) => TRANSACTION_STATUS_ENUM()[text] || '-'
            },
            {
              title: t('发起人的层级'),
              width: 130,
              dataIndex: 'level',
              key: 'level',
              ellipsis: true,
              hidden: checkedList.includes('level'),
              sorter: { multiple: 3 }, //levelAsc  levelDesc
            },

            {
              title: t('入账日期'),
              dataIndex: 'createdAt',
              width: 190,
              ellipsis: true,
              key: 'createdAt',
              hidden: checkedList.includes('createdAt'),
              sorter: { multiple: 7 }, //postedAtAsc  postedAtDesc
              render: (text: any) => text ? new Date(+text).toLocaleString() : '-'
            },
            {
              title: t('到账日期'),
              dataIndex: 'postedAt',
              width: 190,
              key: 'postedAt',
              ellipsis: true,
              hidden: checkedList.includes('postedAt'),
              sorter: { multiple: 8 }, //createdAtAsc  createdAtDesc
              render: (text: any) => text ? new Date(+text).toLocaleString() : '-'
            },
            {
              title: t('创建时间'),
              dataIndex: 'creTime',
              width: 190,
              key: 'creTime',
              ellipsis: true,
              hidden: checkedList.includes('creTime'),
              sorter: { multiple: 9 }, //creTimeAsc  creTimeDesc
            },
            {
              title: t('更新时间'),
              dataIndex: 'updTime',
              width: 190,
              ellipsis: true,
              key: 'updTime',
              hidden: checkedList.includes('updTime'),
              sorter: { multiple: 10 }, //updTimeAsc  updTimeDesc
            },
            {
              title: t('操作'),
              key: 'action',
              fixed: 'right',
              width: 130,
              render: (_: any, record: any) => (
                <Space size="middle">
                  <Button
                    size='small'
                    onClick={goDetails.bind(this, record)} type="text" className='_primary'>{t('详情')} </Button>
                </Space>
              ),
            },
          ]}
          dataSource={data}
          pagination={{
            hideOnSinglePage: true,
            pageSize: queryPageSize,
            current,
            total,
            pageSizeOptions: [10,20,50,100],
            simple: { readOnly: true },
            onChange: pageChange,
            position: ['bottomRight']
          }}
        />
      </Card>
      <Modal
        title={t("下载交易报告")}
        open={downloadShow}
        className='top-[30vh]'
        width={400} 
        onCancel={close}
        destroyOnClose={true}
        footer={null}
      >
        <div className='text-[14px] font-bold mb-[10px]'>
          {t('您可以选择日期范围或下载所有日期范围')}
        </div>
        <Form
          layout="vertical"
        >
          <Form.Item
            label={<span className='text-[12px]'>{t('账户')}</span>}
            name="coin" >
            <Select
              placeholder={t('选择账户')}
              options={[
                { label: 'USDT', value: 'USDT' },
                { label: 'USD', value: 'USD' }
              ]} />
          </Form.Item>
          <Form.Item
            label={<span className='text-[12px]'>{t('日期范围')}</span>}
            name="account"
          >
            <RangePicker className='w-full' />
          </Form.Item>
        </Form>
        <Button className='w-full' shape='round'
          onClick={download}
          type='primary'>{t('下载')}</Button>
      </Modal>
      <Drawer
        size='large'
        title={t('交易详情')}
        placement="left"
        closable={false}
        onClose={onClose}
        open={isDetails}
      >
        {
          [
            { label: '事件', value: EVENT_FEEENUM()[activeData?.event] || '-' },
            { label: '创建时间', value: activeData?.creTime || '-' },
            { label: '更新时间', value: activeData?.updTime || '-' },
            { label: '入账日期', value: activeData?.createdAt && new Date(+activeData?.createdAt).toLocaleString() || '-' },
            { label: '到账日期', value: activeData?.postedAt && new Date(+activeData?.postedAt).toLocaleString() || '-' },
            { label: '交易状态', value: TRANSACTION_STATUS_ENUM()[activeData?.status] || '-' },
            { label: '交易金额', value: activeData?.amount || '-' },
            { label: '新余额', value: activeData?.newBalance || '-' },
            { label: '旧余额', value: activeData?.oldBalance || '-' },
            { label: '发起人的层级', value: activeData?.level || '-' },
            // { label: '层级1手续费', value: activeData?.level1Fee || '-' },
            // { label: '层级2手续费', value: activeData?.level2Fee || '-' },
            // { label: '层级3手续费', value: activeData?.level3Fee || '-' },
            // { label: '层级4手续费', value: activeData?.level4Fee || '-' },
            // { label: '层级5手续费', value: activeData?.level5Fee || '-' },
            // { label: '层级2利润', value: activeData?.level21Profit || '-' },
            // { label: '层级3利润', value: activeData?.level31Profit || '-' },
            // { label: '层级4利润', value: activeData?.level41Profit || '-' },
            // { label: '层级5利润', value: activeData?.level51Profit || '-' },
            { label: '费用类型', value: FEETYPE_ENUM[activeData?.feeType] || '-' },
            { label: '交易货币', value: activeData?.currency || '-' },
            { label: '链', value: activeData?.chain || '-' },
            { label: '块高', value: activeData?.blockNumber || '-' },
            { label: '事件ID', value: activeData?.eventId || '-' },
            { label: '卡id', value: activeData?.accCardId || '-' },
            { label: '卡账户ID', value: activeData?.cardAccountId || '-' },
            { label: '账户id', value: activeData?.accAccountId || '-' },
            { label: '唯一key', value: activeData?.idempotencyKey || '-' },
            { label: '数据id', value: activeData?.dataId || '-' },
            { label: 'ID', value: activeData?.id || '-' },

          ].map((row, i) => (
            <Flex key={`drawer-row-${i}`}>
              <div className='w-[30%] px-[10px] py-[5px] bg-[#fafafa]' style={{ border: '1px solid #e8e8e8', borderTop: i == 0 ? '1px solid #e8e8e8' : 'none' }} >{t(row.label)}</div>
              <div className='px-[10px] py-[5px] flex-1 text-[#000000a6] text-[12px]' style={{ border: '1px solid #e8e8e8', borderTop: i == 0 ? '1px solid #e8e8e8' : 'none', borderLeft: 'none' }} >{row.value}</div>
            </Flex>
          ))
        }
      </Drawer>
    </div>
  );
};
