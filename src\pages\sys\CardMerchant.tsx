import {
    Table, Input,
    Modal, Form, message, Card, Flex
} from 'antd';
import {
    HomeOutlined,
    // RocketOutlined,
    SearchOutlined
} from '@ant-design/icons';
import { PageHeader } from '@/components';
import { useTranslation } from 'react-i18next';
import { useEffect, useRef, useState } from 'react';
import {
    getMerchantCardList,
    postAddOrganiz,
    postUpdateOrganiz
} from '@/api/sys'
// import { useNavigate } from 'react-router-dom';

export const CardMerchantPage = () => {
    // const context = useStylesContext();
    const { t } = useTranslation()
    const [current, setCurrent] = useState<number>(1)
    const [total, setTotal] = useState<number>(0)
    const loadingRef = useRef(true)
    const [loading, setLoading] = useState(false)
    const [open, setOpen] = useState(false);
    const [data, setData] = useState([])
    const formRef: any = useRef()
    const [activeData, setActive] = useState<any>()
    // const navigate = useNavigate();

    useEffect(() => {
        if (loadingRef.current) {
            loadingRef.current = false
            query()
        }
    }, [])
    const pageChange = (page: number) => {
        setCurrent(page)
    }

    const query = (value?: any, type?: string) => {
        if (!loading) {
            getMerchantCardList({
                pageNum: 1,
                pageSize: 20,
                companyName: type == 'search' ? value : undefined
            }).then((res: any) => {
                if (res.code == 200) {
                    setTotal(res.data?.total)
                    setData(res.data?.list || [])
                }
            }).catch(err => {
                console.log(err);
            }).finally(() => {
                setLoading(false)
            })
        }
    }

    const handelGenTenantId = () => {
        return "TN-" + Math.floor(Math.random() * 999999 + 100000);
    }
    const cancel = () => {
        setOpen(false);
        setActive({})
    }
    const addAccount = () => {
        // cancel()
        let api = postAddOrganiz
        if (activeData?.id) api = postUpdateOrganiz
        formRef.current?.validateFields().then((values: any) => {
            api({
                // roleType: "9",
                merchantId: handelGenTenantId(),
                ...values,
                id: activeData?.id || undefined
            }).then((res: any) => {
                const { code } = res
                if (code == 200) {
                    message.open({
                        type: 'success',
                        content: activeData?.id ? t('修改成功') : t('添加成功')
                    })
                    cancel()
                    query()
                    return
                }
                message.open({
                    type: 'error',
                    content: t(res.message)
                })
            })
        })

    }
    // const add = () => {
    //     setOpen(true);
    // }

    // const toUrl = (row: any) => {
    //     navigate('/sys/organiz/apiConfig', { state: { id: row?.appid } });
    // }
    // const toUrl2 = (row: any) => {
    //     navigate('/sys/organiz/cardConfig', { state: { id: row?.appid } });
    // }

    return (
        <div>
            <PageHeader
                title=""
                breadcrumbs={[
                    {
                        title: (
                            <>
                                <HomeOutlined />
                                <span>{t('卡商管理')}</span>
                            </>
                        ),

                    },
                ]}
            />
            <Card className='mt-[20px]'>
                <Flex justify='space-between'>
                    <Input
                        prefix={<SearchOutlined />}
                        allowClear
                        placeholder={t('请输入关键字进行搜索')}
                        className='w-[30%] max-w-[300px]'
                        onBlur={(e: any) => query(e.target.value, 'search')}
                    />
                    {/* <Button onClick={add} type='primary'> {t('新增组织')}</Button> */}
                </Flex>
                <Table
                    loading={loading}
                    className='mt-[20px]'
                    rowKey={(row: any) => row.id}
                    columns={[
                        {
                            title: t('名称'),
                            dataIndex: 'cardMerchantName',
                            key: 'cardMerchantName',
                        },
                        {
                            title: t('appKey'),
                            dataIndex: 'cardMerchantAppkey',
                            key: 'cardMerchantAppkey',
                        },
                        {
                            title: t('Host'),
                            dataIndex: 'cardMerchantBaseapi',
                            key: 'cardMerchantBaseapi',
                        },
                        // {
                        //     title: t('操作'),
                        //     key: 'action',
                        //     render: (_, row: any) => (
                        //         <>
                        //             <Button type='text' className=' cursor-pointer ' onClick={() => {
                        //                 setActive(row)
                        //                 setOpen(true)
                        //                 setTimeout(() => {
                        //                     formRef.current?.setFieldsValue({ ...row })
                        //                 }, 10)
                        //             }}>{t('修改')}</Button>
                        //             <Button type='text' className=' cursor-pointer ' onClick={() => {
                        //                 toUrl(row)
                        //             }}>{t('API配置')}</Button>
                        //             <Button type='text' className=' cursor-pointer ' onClick={() => {
                        //                 toUrl2(row)
                        //             }}>{t('卡配置')}</Button>
                        //         </>
                        //     ),
                        // },
                    ]}
                    dataSource={data}
                    pagination={{
                        hideOnSinglePage: true,
                        pageSize: 10,
                        current,
                        total,
                        onChange: pageChange,
                        position: ['bottomRight']
                    }}
                />
            </Card>
            <Modal
                maskClosable={false}
                keyboard={false}
                title={activeData?.id ? t("修改组织信息") : t("新增组织")}
                open={open}
                onOk={addAccount}
                onCancel={cancel} 
                okText={t('确认')}
                destroyOnClose={true}
                cancelText={t("取消")}
            >
                <Form
                    ref={formRef}
                    name="sign-up-form"
                    layout="vertical"
                    labelCol={{ span: 24 }}
                    wrapperCol={{ span: 24 }}
                    autoComplete="off"
                    defaultValue={{ ...activeData }}
                    requiredMark={false}
                >
                    <Form.Item
                        label={t('组织名称')}
                        name="companyName"
                        rules={[
                            { required: true, message: t('请输入组织名称') },
                        ]}
                    >
                        <Input />
                    </Form.Item>
                    <Form.Item
                        label={t('地址')}
                        name="companyAddress"
                        rules={[
                            { required: true, message: t('请输入地址') },
                        ]}
                    >
                        <Input />
                    </Form.Item>
                    <Form.Item
                        label={t('组织电话')}
                        name="companyPhone"
                        rules={[
                            { required: true, message: t('请输入电话') },
                        ]}
                    >
                        <Input />
                    </Form.Item>

                </Form>
            </Modal>
        </div>
    );
};
