import { Modal, Form, Input, Button, message } from 'antd';
import { useState } from 'react';
import { accountLimitQuery, accountLimitUpdate } from '../../api/accountlimit';

interface AccountLimitModalProps {
  visible: boolean;
  onCancel: () => void;
  accountId: string;
}

interface LimitFormValues {
  limitAmount: string;
  limitCount: string;
}

const AccountLimitModal = ({ visible, onCancel, accountId }: AccountLimitModalProps) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const fetchLimitData = async () => {
    try {
      setLoading(true);
      const res = await accountLimitQuery({ 
        accountId,
        limitAmount: '0', // Default values for query
        limitCount: '0'
      });
      
      if (res.code === 200 && res.data) {
        form.setFieldsValue(res.data);
      } else {
        form.resetFields();
        if (res.code !== 200) {
          // message.error(res.message || '获取账户限额数据失败');
        }
      }
    }
    // catch (error) {
    //   message.error(error.message || '获取账户限额数据失败');
    // }
    finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (values: LimitFormValues) => {
    try {
      setLoading(true);
      const data = {
        accountId,
        ...values
      };

      const res = await accountLimitUpdate(data);
      
      if (res.code === 200) {
        message.success(res.message || '账户限额更新成功');
        onCancel();
      } else {
        // message.error(res.message || '操作失败');
      }
    }
    // catch (error) {
    //   message.error(error.message || '操作失败');
    // }
    finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title="设置USAFE账户限额"
      visible={visible}
      onCancel={onCancel}
      footer={null}
      destroyOnClose
      afterOpenChange={(open) => open && fetchLimitData()}
    >
      <Form form={form} onFinish={handleSubmit} layout="vertical">
        <Form.Item
          label="账户ID"
        >
          <Input value={accountId} disabled />
        </Form.Item>
        <Form.Item
          label="限额金额"
          name="limitAmount"
          rules={[
            { required: true, message: '请输入限额金额' },
            { 
              validator: (_, value) => 
                parseFloat(value) >= 0 ? Promise.resolve() : Promise.reject('不能小于0')
            }
          ]}
        >
          <Input type="number" />
        </Form.Item>

        <Form.Item
          label="限额次数"
          name="limitCount"
          rules={[
            { required: true, message: '请输入限额次数' },
            { 
              validator: (_, value) => 
                parseFloat(value) >= 0 ? Promise.resolve() : Promise.reject('不能小于0')
            }
          ]}
        >
          <Input type="number" />
        </Form.Item>

        <Form.Item>
          <div style={{ display: 'flex', justifyContent: 'flex-end', gap: 8 }}>
            <Button onClick={onCancel}>取消</Button>
            <Button type="primary" htmlType="submit" loading={loading}>
              确认
            </Button>
          </div>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default AccountLimitModal;
