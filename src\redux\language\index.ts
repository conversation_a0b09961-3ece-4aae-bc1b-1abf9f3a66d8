import { createSlice } from '@reduxjs/toolkit';

export interface LanguageState {
  language: string;
}

const initialState: LanguageState = {
  language: 'zh',
};

const languageSlice = createSlice({
  name: 'language',
  initialState,
  reducers: {
    setLanguageSlice: (state: LanguageState, action) => {
      state.language = action.payload
    },
  },
});

export const { setLanguageSlice } = languageSlice.actions;

export default languageSlice.reducer;
