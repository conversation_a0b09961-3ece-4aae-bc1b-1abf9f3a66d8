import request from '../request.ts'

// 获取组织
export function getOrganiz(params: any) {
    return request({
        url: '/tenant/listpage',
        method: 'get',
        params
    })
}

// 获取组织不分页
export function getOrganizList(params: any) {
    return request({
        url: '/tenant/list',
        method: 'get',
        params
    })
}
//添加组织
export function postAddOrganiz(data: any) {
    return request({
        url: "/tenant/add",
        method: "post",
        data,
    });
}

//更新组织
export function postUpdateOrganiz(data: any) {
    return request({
        url: "/tenant/update",
        method: "post",
        data,
    });
}

//系统用户
export function getSysUserList(params: any) {
    return request({
        url: "/system/tenantUser/listpage",
        method: "get",
        params,
    });
}
//系统用户 添加
export function postSysUserAdd(data: any) {
    return request({
        url: "/system/tenantUser/add",
        method: "post",
        data,
    });
}
//系统用户 修改
export function postSysUserUpdate(data: any) {
    return request({
        url: "/system/tenantUser/update",
        method: "post",
        data,
    });
}


//系统组织 api 参数列表
export function postOrganizConfigList(params: any) {
    return request({
        url: "/tenant/secretAdmin/listpage",
        method: "get",
        params,
    });
}
//系统组织  参数 更新
export function postOrganizConfigUpload(data: any) {
    return request({
        url: "/tenant/secretAdmin/update",
        method: "post",
        data,
    });
}

//系统组织  参数 添加
export function postOrganizConfigAdd(data: any) {
    return request({
        url: "/tenant/secretAdmin/add",
        method: "post",
        data,
    });
}


//获取卡商
export function getMerchantCard(params: any) {
    return request({
        url: "/admin/creditCard/merchant/list/options",
        method: "get",
        params
    });
}

//获取组织配置的卡片类型列表
export function getMerchantCardTypeList(params: any) {
    return request({
        url: "/admin/creditCard/type/listpage",
        method: "get",
        params
    });
}

//给组织添加卡
export function getMerchantCardAdd(data: any) {
    return request({
        url: "/admin/creditCard/type/add",
        method: "post",
        data,
    });
}
//组织修改卡
export function getMerchantCardUpload(data: any) {
    return request({
        url: "/admin/creditCard/type/update",
        method: "post",
        data,
    });
}

//卡商管理
export function getMerchantCardList(params: any) {
    return request({
        url: "/admin/creditCard/merchant/listpage",
        method: "get",
        params,
    });
}

//货币管理
export function getCurrencyList(params: any) {
    return request({
        url: "/admin/currency/list",
        method: "get",
        params,
    });
}
//货币 更新
export function postCurrencyUpdate(data: any) {
    return request({
        url: "/admin/currency/save",
        method: "post",
        data,
    });
}


//货币 添加
export function postCurrencyAdd(data: any) {
    return request({
        url: "/admin/currency/add",
        method: "post",
        data,
    });
}
//获取组织的费率
export function postQueryTenantFee(data: any) {
    return request({
        url: "/tenant/fee/getTenantFeeSetting",
        method: "post",
        data,
    });
}

//修改组织的费率
export function postUpdTenantFee(data: any) {
    return request({
        url: "/tenant/fee/updTenantFeeSetting",
        method: "post",
        data,
    });
}

//修正组织的费率
export function repairAccountTenantFeeSetting(data: any) {
    return request({
        url: "/tenant/fee/repairAccountTenantFeeSetting",
        method: "post",
        data,
    });
}


//获取组织的 配置卡费率
export function postQueryTenantCardFee(data: any) {
    return request({
        url: "/admin/creditCard/type/fee/getCardFeeSetting",
        method: "post",
        data,
    });
}

//修改组织的配置卡费率
export function postUpdTenantCardFee(data: any) {
    return request({
        url: "/admin/creditCard/type/fee/updCardFeeSetting",
        method: "post",
        data,
    });
}



//获取库存列表
export function getInventoryList(params: any) {
    return request({
        url: "/admin/creditCard/repo/listpage",
        method: "get",
        params,
    });
}


//导入库存列表
export function postImportList(data: any) {
    return request({
        url: "/admin/creditCard/repo/import",
        method: "post",
        data,
    });
}

//获取卡CVC 信息
export function postQueryCardCVC(data: any) {
    return request({
        url: "/admin/creditCard/repo/query/cvc",
        method: "post",
        data,
    });
}

//删除库存卡
export function postDelInventorCard(data: any) {
    return request({
        url: "/admin/creditCard/repo/remove",
        method: "post",
        data,
    });
}

//查询 api 权限接口
export function treeList(params: any) {
    return request({
        url: "/user/menuTree/",
        method: "get",
        params
    });
}

//通过uid获取白名单信息
export function getIpFilterByUid(uid: any) {
    return request({
        url: "/system/tenantUserIpFilter/getIpFilterByUid?uid=" + uid,
        method: "get",
    });
}

//设置白名单ip
export function addIpFilter(data: any) {
    return request({
        url: "/system/tenantUserIpFilter/addIpFilter",
        method: "post",
        data,
    });
}
