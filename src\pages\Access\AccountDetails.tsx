import {
  Flex, Row, Col, Input, Button,
  Card, Table, DatePicker, Modal,
  Form, Select, Checkbox, message,
  Segmented, ConfigProvider,
} from 'antd';
// import { useStylesContext } from '@/context';
import {
  HomeOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  DollarOutlined,
  CreditCardOutlined,
  // SyncOutlined/
} from '@ant-design/icons';
import { PageHeader } from '@/components';
// import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useEffect, useRef, useState } from 'react';
import CountUp from 'react-countup';
import { useNavigate, useLocation } from 'react-router-dom';
import { getAccountDetails, postAccountToAccount } from '@/api/account'
import { queryTXList } from '@/api/transactions'
import { TRANSACTION_ENUM, EVENT_FEEENUM, TRANSACTION_STATUS_ENUM } from '@/utils/enum'

const { RangePicker } = DatePicker;
export const AccountDetailsPage = () => {
  // const context = useStylesContext();
  let defalutCheckedList = []
  localStorage.getItem('checkedList-account') && (defalutCheckedList = (JSON.parse(localStorage.getItem('checkedList-account') || '[]')))
  const { t } = useTranslation()
  const [current, setCurrent] = useState<number>(1)
  const [total, setTotal] = useState<number>(0)
  const loadingRef = useRef(true)
  const [loading, setLoading] = useState(false)
  const [open, setOpen] = useState(false);
  const [isEye, setEye] = useState(true)
  const [data, setData] = useState()
  const navigate = useNavigate();
  const location = useLocation();
  const [activeData, setActiveData]: any = useState({})
  const [checkedList, setCheckedList]: any = useState(defalutCheckedList)
  const [TXAccountShow, setTXAccountShow] = useState(false)
  const topupRef = useRef<any>()
  const [submitIng, setSubmitIng] = useState(false)
  const [active, setActive] = useState('account_transaction')
  const [timeValue, setTimeValue]: any = useState([])


  useEffect(() => {
    if (loadingRef.current) {
      loadingRef.current = false
      query()
    }
  }, [])
  useEffect(() => { queryTransactionRecords() }, [active])


  const pageChange = (page: number,) => {//pageSize: number
    setCurrent(page)
  }
  const SegmeChange = (item: any) => {
    setActive(item)
  }

  const query = () => {
    if (!loading) {
      setLoading(true)
      const accountId = location.state?.id || ''
      getAccountDetails({ accountId }).then((res: any) => {
        const { code } = res
        if (code == 200) {
          res.data.forEach((v: any) => {
            if (v.asset == 'USD') {
              setActiveData(v)
            }
          });
          return
        }
      }).catch((err) => console.log(err))
        .finally(() => {
          setLoading(false)
          queryTransactionRecords()
        })
    }
  }

  const queryTransactionRecords = (value?: any, type?: string) => {
    const accountId = location.state?.id || ''
    queryTXList({
      accAccountId: accountId,
      pageNum: 1,
      pageSize: 10,
      event: active,
      postedAtStart: timeValue?.length && timeValue[0]['$d']?.getTime() || undefined,
      //@ts-ignore
      postedAtEnd: timeValue?.length && timeValue[1]['$d']?.getTime() || undefined,
      keyWord: type == 'search' ? value : undefined
    }).then((res: any) => {
      const { code } = res
      if (code == 200) {
        setData(res.data.list)
        setTotal(res.data.total)
        return
      }
    }).catch((err) => console.log(err))
      .finally(() => {
        setLoading(false)
      })

  }

  const add = () => {
    setOpen(true);
  }

  const download = () => {

  }

  const close = () => {
    setOpen(false);
  }

  const TXAccount = () => {
    const accountId = location.state?.id || ''
    topupRef.current.validateFields().then((values: any) => {
      setSubmitIng(true)
      postAccountToAccount({
        sourceAccountId: accountId,
        destinationAccountId: values.destinationAccountId,
        amount: values.amount,
        "feeType": 1,
        "asset": "USD",
      }).then((res: any) => {
        const { code } = res
        if (code == 200) {
          setTXAccountShow(false)
          queryTransactionRecords()
          return
        }
        message.open({ type: 'error', content: t(res.message) })
      }).catch((err) => console.log(err))
        .finally(() => {
          query()
          setSubmitIng(false)
        })
    })
  }

  const back = () => {
    navigate('/acccess/account-management')
  }

  const cancel = () => {
    setTXAccountShow(false)
  }


  return (
    <div>
      <PageHeader
        title={t('账户详情')}
        breadcrumbs={[
          {
            title: (
              <span className=' cursor-pointer' onClick={back}>
                <HomeOutlined />
                <span className='ml-[10px]'>{t('账户管理')}</span>
              </span>
            ),
          },
          {
            title: <span>{t('账户详情')}</span>,

          },
        ]}
      />
      <Card className='mt-[20px]' title={t("总览")}>
        <Flex justify='space-between'>
          <div>
            <Flex>
              <div className='text-[#666666] text-[16px] mr-[10px]'>{t('资产净值')}</div>
              {
                isEye ?
                  <EyeOutlined onClick={() => setEye(false)} /> :
                  <EyeInvisibleOutlined onClick={() => setEye(true)} />
              }
            </Flex>
            <Flex className='text-[#305992] text-[32px] font-bold'>
              {
                isEye ?
                  <CountUp end={activeData?.balanceAmount || 0}
                    duration={0.5}
                    decimals={2}
                    suffix=" USD"
                  /> :
                  <span>******</span>
              }
            </Flex>
          </div>
          <Flex >
            <Button shape='round' type='primary' className='w-[120px]' onClick={() => setTXAccountShow(true)} >{t('转账')}</Button>
          </Flex>
        </Flex>

        <Row className='mt-[20px]' >
          <Col xs={24} md={12} lg={6}>
            <Flex>
              <DollarOutlined className='text-[25px]' />
              <div className='text-[#000] text-[20px] font-bold ml-[10px]'>
                <div className='text-[#666666] text-[14px] mr-[10px]'>{t('资产')}</div>
                {
                  isEye ?
                    <CountUp end={activeData?.balanceAmount || 0} /> :
                    <span>******</span>
                }
              </div>
            </Flex>
          </Col>
          <Col xs={24} md={12} lg={6}>
            <Flex>
              <CreditCardOutlined className='text-[25px]' />
              <div className='text-[#000] text-[20px] font-bold ml-[10px]'>
                <div className='text-[#666666] text-[14px] mr-[10px]'>{t('卡片账户余额')}</div>
                {
                  isEye ?
                    <CountUp end={0} /> :
                    <span>******</span>
                }
              </div>
            </Flex>
          </Col>
        </Row>
      </Card>
      <Card className='mt-[20px]' title={t("交易记录")}
        extra={<Button onClick={add} disabled type='primary'> {t('下载交易报告')}</Button>}
      >
        <ConfigProvider theme={{
          token: {
            borderRadius: 20,
            borderRadiusLG: 20,
            borderRadiusSM: 20,
            borderRadiusXS: 20,
          },
          components: {
            Segmented: {
              itemSelectedBg: '#274673',
              itemSelectedColor: '#fff',
              itemColor: '#fff',
            }
          }
        }}>
          {/* 返回 2-5 */}
          {/* {JSON.stringify(TRANSACTION_ENUM.slice(1, 5))} */}
          <Segmented options={TRANSACTION_ENUM()?.slice(1, 5)}
            value={active}
            size='small'
            onChange={SegmeChange}
            className='h-[50px] mb-[20px]
              p-[8px] rounded-[50px] bg-[#efefef] _Segmented'
          />
        </ConfigProvider>
        <Flex justify='space-between'>
          <Flex className='mt-[20px]'>
            <RangePicker
              showTime
              onChange={(e: any) => { setTimeValue(e) }}
              className='w-[300px]' />
            <Button onClick={queryTransactionRecords} className='ml-[10px]'>{t('搜索')}</Button>
          </Flex>
        </Flex>
        <Table
          loading={loading}
          className='mt-[20px] _store_table'
          columns={[
            {
              title: t('ID'),
              dataIndex: 'id',
              key: 'id',
              width: 100,
              sorter: { multiple: 1 },
              filteredValue: checkedList,
              filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
                <div className='p-[20px] bg-[#fff] w-[600px]'>
                  <div>{t("请选择需要隐藏的字段")}</div>
                  <Checkbox.Group
                    options={[
                      { label: t('数据id'), value: 'dataId' },
                      { label: t('唯一key'), value: 'idempotencyKey' },
                      { label: t('账户id'), value: 'accAccountId' },
                      { label: t('卡账户ID'), value: 'cardAccountId' },
                      { label: t('卡id'), value: 'accCardId' },
                      { label: t('事件ID'), value: 'eventId' },
                      { label: t('事件'), value: 'event' },
                      { label: t('链'), value: 'chain' },
                      { label: t('块高'), value: 'blockNumber' },
                      { label: t('交易货币'), value: 'currency' },
                      { label: t('交易金额'), value: 'amount' },
                      { label: t('发起人的层级'), value: 'level' },
                      { label: t('旧余额'), value: 'oldBalance' },
                      { label: t('新余额'), value: 'newBalance' },
                      { label: t('卡帐户交易状态'), value: 'status' },
                      { label: t('入账日期'), value: 'createdAt' },
                      { label: t('到账日期'), value: 'postedAt' },
                      { label: t('创建时间'), value: 'creTime' },
                      { label: t('更新时间'), value: 'updTime' },
                    ]}
                    value={selectedKeys}
                    onChange={(e) => {
                      setSelectedKeys(e)
                    }}
                  />
                  <div style={{ marginTop: 8 }}>
                    <Button
                      type="primary"
                      className='mr-[10px]'
                      onClick={() => {
                        confirm();
                        setCheckedList(selectedKeys)
                      }}
                      size="small"
                      style={{ width: 90 }}
                    >
                      {t('确定')}
                    </Button>
                    <Button
                      onClick={() => {
                        clearFilters && clearFilters()
                        setSelectedKeys([])
                      }}
                      size="small"
                      style={{ width: 90 }}
                    >
                      {t('重置')}
                    </Button>
                  </div>
                </div>)
            },
            {
              title: t('数据ID'),
              dataIndex: 'dataId',
              width: 130,
              key: 'dataId',
              ellipsis: true,
              hidden: checkedList.includes('dataId'),
            },
            {
              title: t('唯一KEY'),
              dataIndex: 'idempotencyKey',
              key: 'idempotencyKey',
              width: 130,
              ellipsis: true,
              hidden: checkedList.includes('idempotencyKey'),
            },
            {
              title: t('账户ID'),
              dataIndex: 'accAccountId',
              width: 130,
              ellipsis: true,
              key: 'accAccountId',
              hidden: checkedList.includes('accAccountId'),
            },
            {
              title: t('卡账户ID'),
              width: 130,
              dataIndex: 'accCardAccountId',
              key: 'accCardAccountId',
              ellipsis: true,
              hidden: checkedList.includes('accCardAccountId'),
            },
            {
              title: t('卡ID'),
              dataIndex: 'accCardId',
              ellipsis: true,
              width: 130,
              key: 'accCardId',
              hidden: checkedList.includes('accCardId'),
            },
            {
              title: t('事件ID'),
              dataIndex: 'eventId',
              width: 130,
              key: 'eventId',
              ellipsis: true,
              hidden: checkedList.includes('eventId'),
            },
            {
              title: t('事件'),
              width: 130,
              dataIndex: 'event',
              key: 'event',
              ellipsis: true,
              sorter: { multiple: 10 }, //eventAsc  eventDesc
              hidden: checkedList.includes('event'),
              render: (text: any) => EVENT_FEEENUM()[text] || '-'
            },
            // {
            //   title: t('链'),
            //   width: 130,
            //   dataIndex: 'chain',
            //   key: 'chain',
            //   ellipsis: true,
            //   hidden: checkedList.includes('chain'),
            // },
            // {
            //   title: t('块高'),
            //   width: 130,
            //   dataIndex: 'blockNumber',
            //   key: 'blockNumber',
            //   ellipsis: true,
            //   sorter: { multiple: 10 }, //blockNumberAsc  blockNumberDesc
            //   hidden: checkedList.includes('blockNumber'),
            // },
            {
              title: t('交易货币'),
              width: 130,
              dataIndex: 'currency',
              key: 'currency',
              ellipsis: true,
              hidden: checkedList.includes('currency'),
            },
            {
              title: t('交易金额'),
              width: 130,
              dataIndex: 'amount',
              key: 'amount',
              ellipsis: true,
              hidden: checkedList.includes('amount'),
              sorter: { multiple: 2 }, //amountAsc  amountDesc
            },
            {
              title: t('旧余额'),
              width: 130,
              dataIndex: 'oldBalance',
              key: 'oldBalance',
              ellipsis: true,
              sorter: { multiple: 4 }, //oldBalanceAsc  oldBalanceDesc
            },
            {
              title: t('新余额'),
              dataIndex: 'newBalance',
              width: 130,
              key: 'newBalance',
              ellipsis: true,
              hidden: checkedList.includes('newBalance'),
              sorter: { multiple: 5 }, //newBalanceAsc  newBalanceDesc
            },

            {
              title: t('交易状态'),//待处理、已过账、拒绝、无效
              dataIndex: 'status',
              width: 130,
              ellipsis: true,
              key: 'status',
              hidden: checkedList.includes('status'),
              sorter: { multiple: 6 }, //statusAsc  statusDesc
              render: (text: any) => TRANSACTION_STATUS_ENUM()[text] || '-'
            },
            {
              title: t('发起人的层级'),
              width: 130,
              dataIndex: 'level',
              key: 'level',
              ellipsis: true,
              hidden: checkedList.includes('level'),
              sorter: { multiple: 3 }, //levelAsc  levelDesc
            },

            {
              title: t('入账日期'),
              dataIndex: 'createdAt',
              width: 190,
              ellipsis: true,
              key: 'createdAt',
              hidden: checkedList.includes('createdAt'),
              sorter: { multiple: 7 }, //postedAtAsc  postedAtDesc
              render: (text: any) => text ? new Date(+text).toLocaleString() : '-'
            },
            {
              title: t('到账日期'),
              dataIndex: 'postedAt',
              width: 190,
              key: 'postedAt',
              ellipsis: true,
              hidden: checkedList.includes('postedAt'),
              sorter: { multiple: 8 }, //createdAtAsc  createdAtDesc
              render: (text: any) => text ? new Date(+text).toLocaleString() : '-'
            },
            {
              title: t('创建时间'),
              dataIndex: 'creTime',
              width: 190,
              key: 'creTime',
              ellipsis: true,
              hidden: checkedList.includes('creTime'),
              sorter: { multiple: 9 }, //creTimeAsc  creTimeDesc
            },
            {
              title: t('更新时间'),
              dataIndex: 'updTime',
              width: 190,
              ellipsis: true,
              key: 'updTime',
              hidden: checkedList.includes('updTime'),
              sorter: { multiple: 10 }, //updTimeAsc  updTimeDesc
            },
          ]}
          dataSource={data}
          scroll={{ x: 1000 }}
          pagination={{
            hideOnSinglePage: true,
            pageSize: 10,
            current,
            total,
            simple: { readOnly: true },
            onChange: pageChange,
            position: ['bottomRight']
          }}
        />
      </Card>
      <Modal
        title={t("下载交易报告")}
        open={open}
        className='top-[30vh]'
        width={400}
        onCancel={close}
        destroyOnClose={true}
        footer={null}
      >
        <div className='text-[14px] font-bold mb-[10px]'>
          {t('您可以选择日期范围或下载所有日期范围')}
        </div>
        <Form
          layout="vertical"
        >
          <Form.Item
            label={<span className='text-[12px]'>{t('账户')}</span>}
            name="coin" >
            <Select
              placeholder={t('选择账户')}
              options={[
                { label: 'USDT', value: 'USDT' },
                { label: 'USD', value: 'USD' }
              ]} />
          </Form.Item>
          <Form.Item
            label={<span className='text-[12px]'>{t('日期范围')}</span>}
            name="account"
          >
            <RangePicker className='w-full' />
          </Form.Item>
        </Form>
        <Button className='w-full' shape='round'
          onClick={download}
          type='primary'>{t('下载')}</Button>
      </Modal>
      {/* 账户转账 */}
      <Modal
        title={t("账户转账")}
        open={TXAccountShow}
        centered
        width={400}
        onCancel={cancel}
        destroyOnClose={true}
        footer={null}
      >
        <Form
          ref={topupRef}
          layout="vertical"
        >
          {/* <Form.Item
            label={<span className='text-[12px]'>{t('账户')}</span>}
            name="accountId"
            rules={[
              { required: true, message: t('账户') },
            ]}
          >

            <DebounceSelect
              showSearch
              value={tlementAccountValue}
              placeholder=""
              defaultList={defaultList}
              fetchOptions={fetchUserList}
              onChange={(newValue) => {
                setTlementAccountValue(newValue);
              }}
              style={{ width: '100%' }}
            />
          </Form.Item> */}
          <Form.Item
            label={<span className='text-[12px]'>{t('目标账户')}</span>}
            name="destinationAccountId"
            rules={[
              { required: true, message: t('目标账户') },
            ]}
          >
            <Input className='w-full' />
          </Form.Item>

          <Form.Item
            label={<span className='text-[12px]'>{t('金额')}</span>}
            name="amount"
            rules={[
              { required: true, message: t('金额') },
            ]}
          >
            <Input className='w-full' />
          </Form.Item>

        </Form>
        <Flex>
          <Button className='w-full mr-[5px]' shape='round' onClick={cancel}>{t('取消')}</Button>
          <Button className='w-full ml-[5px]' shape='round' type='primary' loading={submitIng} onClick={TXAccount}>{t('转账')}</Button>
        </Flex>
      </Modal>

    </div>
  );
};
