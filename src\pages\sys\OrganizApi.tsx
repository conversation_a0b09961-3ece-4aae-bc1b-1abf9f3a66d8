import {
    Table, Input, DatePicker, Switch,
    Modal, Form, message, Card, Flex, Button
} from 'antd';
import {
    HomeOutlined,
} from '@ant-design/icons';
import { PageHeader } from '@/components';
import { useTranslation } from 'react-i18next';
import { useEffect, useRef, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom'
import {
    postOrganizConfigList,
    postOrganizConfigUpload,
    postOrganizConfigAdd
} from '@/api/sys'


export const OrganizApiPage = () => {
    const navigate = useNavigate();
    const { t } = useTranslation()
    const [current, setCurrent] = useState<number>(1)
    const [total, setTotal] = useState<number>(0)
    const loadingRef = useRef(true)
    const [loading, setLoading] = useState(false)
    const [open, setOpen] = useState(false);
    const [data, setData] = useState([])
    const formRef: any = useRef()
    const [activeData, setActive] = useState<any>()
    const location = useLocation()
    const [expireTime, setExpireTime] = useState('')
    const [isvalue, setIsvalue] = useState('')
    // const [tiem, setTime] = useState<any>()

    useEffect(() => {
        if (loadingRef.current) {
            loadingRef.current = false
            query()
        }
    }, [])
    const pageChange = (page: number) => {
        setCurrent(page)
    }

    const query = (value?: any, type?: string) => {
        if (!loading) {
            postOrganizConfigList({
                pageNum: 1,
                pageSize: 20,
                appid: location?.state.id,
                name: type == 'search' ? value : undefined
            }).then((res: any) => {
                if (res.code == 200) {
                    setTotal(res.data?.total)
                    setData(res.data?.list || [])
                }
            }).catch(err => {
                console.log(err);
            }).finally(() => {
                setLoading(false)
            })
        }
    }

    const cancel = () => {
        setOpen(false);
        setActive({})
        setIsvalue('')
    }
    const addAccount = () => {
        // cancel()
        let api = postOrganizConfigAdd
        if (activeData?.id) api = postOrganizConfigUpload
        formRef.current?.validateFields().then((values: any) => {
            api({
                // ...values,
                ip: values.ip,
                status: values.status ? 1 : 0,
                appSecret: values.appSecret,
                expireTime: new Date(expireTime).getTime(),
                appid: location.state?.id || undefined,
                id: activeData?.id || undefined
            }).then((res: any) => {
                const { code } = res
                if (code == 200) {
                    message.open({
                        type: 'success',
                        content: activeData?.id ? t('修改成功') : t('添加成功')
                    })
                    cancel()
                    query()
                    return
                }
                message.open({
                    type: 'error',
                    content: t(res.message)
                })
            })
        })

    }
    const add = () => {
        setOpen(true);
    }

    const goBack = () => {
        navigate('/sys/organiz')
    }
    const dateChange: any = (_: any, dataString: string) => {
        setExpireTime(dataString)

    }

    const ENUM: any = { 1: t('可用'), 0: t('停止') }
    return (
        <div>
            <PageHeader
                title=""
                breadcrumbs={[
                    {
                        title: (
                            <span onClick={goBack} className=' cursor-pointer'>
                                <HomeOutlined />
                                <span>{t('组织管理')}</span>
                            </span>
                        ),

                    },
                    {
                        title: (
                            <>
                                <span>{t('API配置')}</span>
                            </>
                        ),

                    },
                ]}
            />
            <Card className='mt-[20px]'>
                <Flex justify='space-between'>
                    {/* <Input
                        prefix={<SearchOutlined />}
                        allowClear
                        placeholder={t('请输入关键字进行搜索')}
                        className='w-[30%] max-w-[300px]'
                        onBlur={(e: any) => query(e.target.value, 'search')}
                    /> */}
                    <></>
                    <Button onClick={add} type='primary'> {t('新增配置')}</Button>
                </Flex>
                <Table
                    loading={loading}
                    className='mt-[20px]'
                    scroll={{ x: '1000px' }}
                    rowKey={(row: any) => row.id}
                    columns={[
                        {
                            title: t('appKey'),
                            dataIndex: 'appKey',
                            key: 'appKey',
                        },
                        {
                            title: t('公钥'),
                            dataIndex: 'appSecret',
                            key: 'appSecret',
                            ellipsis: true
                        },
                        {
                            title: t('创建人'),
                            dataIndex: 'createUserName',
                            key: 'createUserName',
                        },
                        {
                            title: t('状态'),
                            dataIndex: 'status',
                            key: 'status',
                            render: (_, row: any) => {
                                return ENUM[row.status]
                            }
                        },
                        {
                            title: t('时间'),
                            dataIndex: 'createTime',
                            key: 'createTime',
                            render: (_, row: any) => {
                                return row.createTime && new Date(row.createTime).toLocaleString() || '-'
                            }
                        },
                        {
                            title: t('操作'),
                            key: 'action',
                            render: (_, row: any) => (//t('操作')
                                <Button className=' cursor-pointer  _primary' onClick={() => {
                                    setActive(row)
                                    setOpen(true)
                                    setIsvalue(row.appSecret)
                                    setTimeout(() => {
                                        formRef.current?.setFieldsValue({
                                            appSecret: row.appSecret,
                                            status: !!row.status
                                        })
                                    }, 10)
                                }}
                                    type='text'
                                >{t('修改')}</Button>
                            ),
                        },
                    ]}
                    dataSource={data}
                    pagination={{
                        hideOnSinglePage: true,
                        pageSize: 10,
                        current,
                        total,
                        onChange: pageChange,
                        position: ['bottomRight']
                    }}
                />
            </Card>
            <Modal
                maskClosable={false}
                keyboard={false}
                title={activeData?.id ? t("修改配置") : t("新增配置")}
                open={open}
                centered
                onOk={addAccount}
                onCancel={cancel} 
                okText={t('确认')}
                width={isvalue ? 800 : undefined}
                destroyOnClose={true}
                cancelText={t("取消")}
            >
                <Form
                    ref={formRef}
                    name="sign-up-form"
                    labelWrap
                    wrapperCol={{ flex: 1 }}
                    labelCol={{ span: 4 }}
                    autoComplete="off"
                    requiredMark={false}
                >
                    <Form.Item
                        label={t('过期时间')}
                        name="expireTime"
                    >
                        <DatePicker
                            value={expireTime}
                            className='w-full'
                            onChange={dateChange}
                            format={'YYYY-MM-DD HH:mm:ss'}
                            showTime
                        />
                    </Form.Item>
                    <Form.Item
                        label={t('状态')}
                        name="status"
                    >
                        <Switch />
                    </Form.Item>
                    <Form.Item
                        label={t('IP')}
                        name="ip"
                        initialValue={"*"}
                        rules={[]}
                    >
                        <Input />
                    </Form.Item>
                    <Form.Item
                        label={t('公钥')}
                        name="appSecret"
                        rules={[
                            { required: true, message: t('公钥') },
                        ]}
                    >
                        <Input.TextArea autoSize onChange={(e: any) => {
                            setIsvalue(e.target.value)
                        }} />
                    </Form.Item>

                </Form>
            </Modal>
        </div>
    );
};
