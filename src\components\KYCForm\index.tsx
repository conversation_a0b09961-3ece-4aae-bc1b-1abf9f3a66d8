import React, { useEffect, useState, forwardRef, useImperativeHandle } from 'react';
import {
  Form,
  Input,
  Select,
  DatePicker,
  Upload,
  Image,
  Flex,
  Button,
} from 'antd';
import { LoadingOutlined, PlusOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import dayjs from 'dayjs';
import countryCodeList from '@/assets/json/country.json';
import { postCountries, postOccupations } from '@/api/card';
import { OrganizationSelect } from '@/components/OrganizationSelect';

interface KYCFormProps {
  initialData?: any;
  disabled?: boolean;
  showOrganization?: boolean;
  onDateOfBirthChange?: (dateString: string) => void;
  onExpiryDateChange?: (dateString: string) => void;
  onFrontImageChange?: (imageData: string) => void;
  onBackImageChange?: (imageData: string) => void;
}

export interface KYCFormRef {
  validateFields: () => Promise<any>;
  setFieldsValue: (values: any) => void;
  getFieldsValue: () => any;
  resetFields: () => void;
}

const beforeUpload = () => false;

export const KYCForm = forwardRef<KYCFormRef, KYCFormProps>(({
  initialData,
  disabled = false,
  showOrganization = true,
  onDateOfBirthChange,
  onExpiryDateChange,
  onFrontImageChange,
  onBackImageChange,
}, ref) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [countryList, setCountryList] = useState<any>([]);
  const [occupationList, setOccupationList] = useState<any>([]);
  const [front, setFront] = useState<string>();
  const [back, setBack] = useState<string>();
  const [loading, setLoading] = useState(false);

  useImperativeHandle(ref, () => ({
    validateFields: () => form.validateFields(),
    setFieldsValue: (values: any) => form.setFieldsValue(values),
    getFieldsValue: () => form.getFieldsValue(),
    resetFields: () => form.resetFields(),
  }));

  useEffect(() => {
    queryCountryAndOccupation();
    // 设置固定的身份类型和账户类型字段
    form.setFieldsValue({
      identityType: "individual",
      accountType: "prepaid"
    });
  }, [form]);

  useEffect(() => {
    if (initialData) {
      setFront(initialData.frontImage);
      setBack(initialData.backImage);

      setTimeout(() => {
        form.setFieldsValue({
          ...initialData,
          num1: `+${initialData.countryCode}`,
          num2: initialData.mobileNumber,
          cardNumber: initialData.number,
          dateOfBirth: initialData.dateOfBirth ? dayjs(initialData.dateOfBirth) : null,
          expiryDate: initialData.expiryDate ? dayjs(initialData.expiryDate) : null,
          front: initialData.frontImage, // 设置表单字段值
          back: initialData.backImage,   // 设置表单字段值
          identityType: "individual",    // 设置固定的身份类型
          accountType: "prepaid",        // 设置固定的账户类型
        });
      }, 100);
    }
  }, [initialData, form]);

  const queryCountryAndOccupation = () => {
    postCountries({}).then((res: any) => {
      if (res.code === 200) {
        setCountryList(
          res.data?.map((v: any) => ({
            value: v.code,
            label: v.name,
          })) || []
        );
      }
    });

    postOccupations({}).then((res: any) => {
      if (res.code === 200) {
        setOccupationList(
          res.data?.map((v: any) => ({
            value: v.name,
            label: v.name,
          })) || []
        );
      }
    });
  };

  const getBase64 = (img: any, callback: (url: string) => void) => {
    const reader = new FileReader();
    reader.addEventListener('load', () => callback(reader.result as string));
    reader.readAsDataURL(img);
  };

  const handleFrontChange = (info: any) => {
    getBase64(info.file, (url: string) => {
      setFront(url);
      form.setFieldsValue({ front: url }); // 同时设置表单字段值
      onFrontImageChange?.(url);
    });
  };

  const handleBackChange = (info: any) => {
    getBase64(info.file, (url: string) => {
      setBack(url);
      form.setFieldsValue({ back: url }); // 同时设置表单字段值
      onBackImageChange?.(url);
    });
  };

  const onDateChange = (_: any, dateString: string) => {
    onDateOfBirthChange?.(dateString);
  };

  const onDateChange2 = (_: any, dateString: string) => {
    onExpiryDateChange?.(dateString);
  };

  const uploadButton = (
    <button style={{ border: 0, background: 'none' }} type="button">
      {loading ? <LoadingOutlined /> : <PlusOutlined />}
      <div style={{ marginTop: 8 }}>Upload</div>
    </button>
  );

  return (
    <Form
      form={form}
      autoComplete="off"
      disabled={disabled}
      requiredMark={false}
      layout="vertical"
    >
      <Flex>
        <div>
          <Flex>
            <Form.Item
              label={t("名")}
              name="firstName"
              className="w-[50%] mr-[10px]"
              rules={[{ required: true, message: t("请输入名") }]}
            >
              <Input />
            </Form.Item>
            <Form.Item
              name="lastName"
              label={t("姓氏")}
              className="w-[50%]"
              rules={[{ required: true, message: t("请输入姓氏") }]}
            >
              <Input />
            </Form.Item>
          </Flex>

          <Flex>
            <Form.Item
              label={t("名(Local)")}
              name="firstNameLocal"
              className="w-[50%] mr-[10px]"
            >
              <Input />
            </Form.Item>
            <Form.Item
              name="lastNameLocal"
              label={t("姓氏(Local)")}
              className="w-[50%]"
            >
              <Input />
            </Form.Item>
          </Flex>

          <Form.Item className="mb-[0] w-full" label={t("手机号码")}>
            <Flex>
              <Form.Item
                name="num1"
                className="w-[100px] mr-[10px]"
                rules={[{ required: true, message: t("请选择国家代码") }]}
              >
                <Select
                  className="w-[100px] mr-[10px]"
                  showSearch
                  options={countryCodeList.map((v) => ({
                    value: v.code,
                    label: v.code,
                  }))}
                />
              </Form.Item>
              <Form.Item
                name="num2"
                className="w-full"
                rules={[{ required: true, message: t("请输入手机号码") }]}
              >
                <Input />
              </Form.Item>
            </Flex>
          </Form.Item>

          <Form.Item
            label="E-Mail"
            name="email"
            className="w-full"
            rules={[{ required: true, message: t("请输入邮箱") }]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            label={t("身份类型")}
            name="identityType"
            className="w-full"
          >
            <Input value="individual" disabled />
          </Form.Item>

          <Form.Item
            label={t("账户类型")}
            name="accountType"
            className="w-full"
          >
            <Input value="prepaid" disabled />
          </Form.Item>

          <Flex>
            <Form.Item
              label={t("证件类型")}
              name="type"
              className="w-[40%] mr-[10px]"
              rules={[{ required: true, message: t("请选择证件类型") }]}
            >
              <Select
                placeholder={t("证件类型")}
                options={[
                  { value: "passport", label: t("护照") },
                  { value: "drivers-license", label: t("驾照") },
                  { value: "national-id", label: t("国民身份证") },
                ]}
              />
            </Form.Item>

            <Form.Item
              label={t("证件号码")}
              name="cardNumber"
              className="w-full"
              rules={[{ required: true, message: t("请输入证件号码") }]}
            >
              <Input />
            </Form.Item>
          </Flex>

          <Flex>
            <Form.Item
              label={t("出生日期")}
              name="dateOfBirth"
              className="w-full"
              rules={[{ required: true, message: t("请选择出生日期") }]}
            >
              <DatePicker onChange={onDateChange} />
            </Form.Item>
            <Form.Item
              label={t("证件有效期")}
              name="expiryDate"
              className="w-full"
              rules={[{ required: true, message: t("请选择证件有效期") }]}
            >
              <DatePicker onChange={onDateChange2} />
            </Form.Item>
          </Flex>

          <Flex>
            <Form.Item
              label={t("证件照正面")}
              name="front"
              className="w-full"
              rules={[
                {
                  validator: (_, value) => {
                    if (!value && !front) {
                      return Promise.reject(new Error(t("请上传证件照正面")));
                    }
                    return Promise.resolve();
                  }
                }
              ]}
            >
              <Flex align="center">
                {front && (
                  <Image
                    className="w-full h-auto max-w-[100px] max-h-[100px]"
                    src={front}
                  />
                )}
                {!disabled && (
                  <Upload
                    name="front"
                    listType="picture-card"
                    className="avatar-uploader ml-[20px]"
                    showUploadList={false}
                    accept="image/png,image/jpg,image/jpeg"
                    beforeUpload={beforeUpload}
                    onChange={handleFrontChange}
                  >
                    {uploadButton}
                  </Upload>
                )}
              </Flex>
            </Form.Item>

            <Form.Item
              label={t("证件照反面")}
              name="back"
              className="w-full"
              rules={[
                {
                  validator: (_, value) => {
                    if (!value && !back) {
                      return Promise.reject(new Error(t("请上传证件照反面")));
                    }
                    return Promise.resolve();
                  }
                }
              ]}
            >
              <Flex align="center">
                {back && (
                  <Image
                    className="w-full ml-[10px] h-auto max-w-[100px] max-h-[100px]"
                    src={back}
                  />
                )}
                {!disabled && (
                  <Upload
                    name="back"
                    listType="picture-card"
                    className="avatar-uploader ml-[20px]"
                    showUploadList={false}
                    accept="image/png,image/jpg,image/jpeg"
                    beforeUpload={beforeUpload}
                    onChange={handleBackChange}
                  >
                    {uploadButton}
                  </Upload>
                )}
              </Flex>
            </Form.Item>
          </Flex>
        </div>

        <div className="w-[50%] ml-[20px]">
          <Form.Item
            label={t("年收入")}
            name="annualIncome"
            className="w-full"
            rules={[{ required: true, message: t("请输入年收入") }]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            label={t("职业")}
            name="occupation"
            className="w-full"
            rules={[{ required: true, message: t("请选择职业") }]}
          >
            <Select
              showSearch
              placeholder={t("职业")}
              options={occupationList}
            />
          </Form.Item>

          <Form.Item
            label={t("职位")}
            name="position"
            className="w-full"
            rules={[{ required: true, message: t("请输入职位") }]}
          >
            <Input />
          </Form.Item>

          <Flex>
            <Form.Item
              label={t("国家")}
              name="country"
              className="w-[30%] mr-[10px]"
              rules={[{ required: true, message: t("请选择国家") }]}
            >
              <Select
                showSearch
                placeholder={t("国家")}
                options={countryList}
              />
            </Form.Item>
            <Form.Item 
              label={t("地址")} 
              className="w-[60%]" 
              name="address"
            >
              <Input />
            </Form.Item>
          </Flex>

          {showOrganization && (
            <Form.Item
              label={t("组织")}
              name="appid"
              rules={[{ required: true, message: t("请选择组织") }]}
            >
              <OrganizationSelect
                allowClear
                placeholder={t("请选择组织")}
                autoSelectFirst={true}
              />
            </Form.Item>
          )}
        </div>
      </Flex>
    </Form>
  );
});

KYCForm.displayName = 'KYCForm';
