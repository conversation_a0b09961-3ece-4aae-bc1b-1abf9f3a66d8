// import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';

import { EmployeeCard } from './EmployeeCard.tsx';

const DATA = {
  employee_id: '24e4e64c-bf09-459f-8cea-f9d2de99d15b',
  title: 'Mrs',
  first_name: '<PERSON><PERSON>',
  middle_name: '<PERSON><PERSON><PERSON>',
  last_name: '<PERSON><PERSON><PERSON>',
  avatar:
    'https://images.unsplash.com/photo-1633332755192-727a05c4013d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8dXNlcnxlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=400&q=60',
  role: 'Operator',
  age: 28,
  email: '<EMAIL>',
  country: 'Indonesia',
  favorite_color: 'gray',
  hire_date: '4/9/2017',
  salary: 92877.67,
};

const meta = {
  title: 'Components/Employee',
  component: EmployeeCard,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
}

export default meta;



export const Default: any = {
  args: {
    data: DATA,
    style: { width: 400 },
  },
};

export const Expanded: any = {
  args: {
    data: DATA,
    showInfo: true,
    style: { width: 400 },
  },
};
