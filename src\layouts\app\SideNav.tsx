import React, { useEffect, useRef, useState } from 'react';
import {
  ConfigProvider, Layout, Menu, MenuProps,
  Flex,
  SiderProps, Popover, Segmented
} from 'antd';
import {
  IdcardOutlined,
  TeamOutlined,
  PieChartOutlined,
  BorderInnerOutlined,
  TransactionOutlined,
  CreditCardOutlined,
  AuditOutlined,
  UnlockOutlined,
  PartitionOutlined,
  RobotOutlined,
  ClusterOutlined,
  // BarsOutlined,
  UserSwitchOutlined,
  DeleteColumnOutlined,
  ReconciliationOutlined,
  MergeCellsOutlined,
  SwapOutlined,
} from '@ant-design/icons';
import { Logo } from '../../components';
import { Link, useLocation } from 'react-router-dom';
import { useTranslation, } from 'react-i18next';
import { COLOR } from '../../App.tsx';
import { SVGCard10 } from '@/components/SVG_Components/SVG_Card10.tsx'
import { useSelector } from 'react-redux';

const { Sider } = Layout;
type MenuItem = Required<MenuProps>['items'][number];

const getItem = (
  label: React.ReactNode,
  key: React.Key,
  icon?: React.ReactNode,
  children?: MenuItem[],
  type?: 'group'
): MenuItem => {
  return {
    key,
    icon,
    children,
    label,
    type,
  } as MenuItem;
};



const rootSubmenuKeys = ['assets', 'profile', 'convenient', 'sys'];

type SideNavProps = SiderProps;
const C_Popover = (props: any) => {
  const { t } = useTranslation();
  const [active, setActive] = useState('0');
  const userStore = useSelector((state: any) => { return state.user });

  const SYSENUM: any = {
    0: t('系统管理员'),
    1: t('代理'),
    2: t('APP'),
  }


  const SegmeChange = (item: any) => {
    setActive(item)
    props.change(item)
  }


  return <Flex justify='space-between'>
    {t('快捷方式')}
    {userStore?.user?.user?.roleType == "0" ?
      <Popover placement="right"
        content={<Segmented
          vertical
          value={active}
          block={true}
          onChange={SegmeChange}
          options={Object.keys(SYSENUM).map((v: any) => {
            return { value: `${v}`, label: SYSENUM[v] }
          })}
          className='
        p-[8px]  bg-[#efefef] '
        />}
      >
        <span className='text-[#000]'><SwapOutlined /></span>
      </Popover> : ''}
  </Flex >
}


const SideNav = ({ ...others }: SideNavProps) => {
  const { t } = useTranslation();
  const [items, setItems] = useState<MenuItem[]>([]);
  const nodeRef = useRef(null);
  const { pathname } = useLocation();
  const [openKeys, setOpenKeys] = useState(['']);
  const [current, setCurrent] = useState('');
  const userStore = useSelector((state: any) => { return state.user });
  const [active, setActive] = useState('0');
  const languageStore = useSelector((state: any) => { return state.language });


  const onOpenChange: MenuProps['onOpenChange'] = (keys) => {
    const latestOpenKey = keys.find((key) => openKeys.indexOf(key) === -1);
    if (latestOpenKey && rootSubmenuKeys.indexOf(latestOpenKey!) === -1) {
      setOpenKeys(keys);
    } else {
      setOpenKeys(latestOpenKey ? [latestOpenKey] : []);
    }
  };

  useEffect(() => {
    const paths = pathname.split('/');
    setOpenKeys(paths);
    if (paths.length >= 4) {
      setCurrent(paths[paths.length - 2]);
    } else {
      setCurrent(paths[paths.length - 1]);
    }

  }, [pathname, userStore, active]);


  useEffect(() => {
    if (userStore?.user) {
      handleItems()
    }
  }, [languageStore.language]);

  const _change = (v: any) => {
    setActive(`${v}`)
  }

  const handleItems = () => {
    let list: any = []
    if (active == '0') {
      list = [
        userStore?.user?.user?.roleType == "0" ? getItem(<C_Popover change={_change} />, '快捷方式', null, [
          getItem(<Link to={'/convenient/injection'}>{t('注入')}</Link>, 'injection', <BorderInnerOutlined />),
          getItem(<Link to={'/convenient/tenantRevenue'}>{t('代理主账户')}</Link>, 'tenantRevenue', <BorderInnerOutlined />),
        ], 'group') : null,

        getItem(t('BEUAccess'), 'acccess', <IdcardOutlined />, [
          getItem(<Link to={'/acccess/account-management'}>{t('账户管理')}</Link>, 'account-management', <PartitionOutlined />),
          getItem(<Link to={'/acccess/card-account'}>{t('卡账户')}</Link>, 'card-account', <SVGCard10 />),
          getItem(<Link to={'/acccess/Card'}>{t('Card')}</Link>, 'Card', <CreditCardOutlined />),
        ], 'group'),

        getItem(t('主菜单'), '主菜单', null, [], 'group'),

        getItem(<Link to={'/overview'}>{t('总览')}</Link>, 'overview', <PieChartOutlined />),
        getItem(<Link to={'/transactions'}>{t('指令记录')}</Link>, 'transactions', <AuditOutlined />),
        userStore?.user?.user?.roleType == "0" ? getItem(<Link to={'/webhook'}>{t('WEB HOOK')}</Link>, 'webhook', <DeleteColumnOutlined />) : null,
        userStore?.user?.user?.roleType == "0" ? getItem(<Link to={'/cardAccountSync'}>{t('卡账户同步')}</Link>, '卡账户同步', <DeleteColumnOutlined />) : null,
        userStore?.user?.user?.roleType == "0" ? getItem(<Link to={'/cardAccountTransactionLog'}>{t('未结算交易')}</Link>, '未结算交易', <DeleteColumnOutlined />) : null,
        getItem(<Link to={'/kyc'}>{t('KYC 管理')}</Link>, 'kyc', <ReconciliationOutlined />),
        getItem(<Link to={'/kyc/update'}>{t('KYC 更新')}</Link>, 'update', <ReconciliationOutlined />),


        getItem(t('账户'), 'profile', null, [
          getItem(<Link to={'/profile/info'}>{t('个人资料')}</Link>, 'info', <RobotOutlined />),
          getItem(<Link to={'/profile/security'}>{t('安全')}</Link>, 'security', <UnlockOutlined />),
        ], undefined),

        getItem(t('系统'), 'sys', null, [
          // getItem(<Link to={'/sys/menu'}>{t('菜单管理')}</Link>, 'menu', <BarsOutlined />),
          // getItem(<Link to={'/sys/role'}>{t('角色管理')}</Link>, 'role', <RobotOutlined />),
          userStore?.user?.user?.roleType == "0" ? getItem(<Link to={'/sys/cardMerchant'}>{t('卡商管理')}</Link>, 'cardMerchant', <UserSwitchOutlined />) : null,
          userStore?.user?.user?.roleType == "0" ? getItem(<Link to={'/sys/currency'}>{t('货币管理')}</Link>, 'currency', <TransactionOutlined />) : null,
          getItem(<Link to={'/sys/inventory'}>{t('库存管理')}</Link>, 'inventory', <MergeCellsOutlined />),
          getItem(<Link to={'/sys/organiz'}>{t('组织管理')}</Link>, 'organiz', <ClusterOutlined />),
          userStore?.user?.user?.roleType == "0" ? getItem(<Link to={'/sys/system-user'}>{t('用户管理')}</Link>, 'system-user', <TeamOutlined />) : null,
        ], undefined),
      ]
    }
    if (active == '1') {
      list = [
        getItem(<C_Popover change={_change} />, '快捷方式', null, [], 'group'),
        getItem(t('BEUAccess'), 'acccess', <IdcardOutlined />, [
          getItem(<Link to={'/acccess/account-management'}>{t('账户管理')}</Link>, 'account-management', <PartitionOutlined />),
          getItem(<Link to={'/acccess/card-account'}>{t('卡账户')}</Link>, 'card-account', <SVGCard10 />),
          getItem(<Link to={'/acccess/Card'}>{t('Card')}</Link>, 'Card', <CreditCardOutlined />),
        ], 'group'),

        getItem(t('主菜单'), '主菜单', null, [], 'group'),

        getItem(<Link to={'/overview'}>{t('总览')}</Link>, 'overview', <PieChartOutlined />),
        getItem(<Link to={'/transactions'}>{t('指令记录')}</Link>, 'transactions', <AuditOutlined />),
        getItem(<Link to={'/webhook'}>{t('WEB HOOK')}</Link>, 'webhook', <DeleteColumnOutlined />),
        getItem(<Link to={'/kyc'}>{t('KYC')}</Link>, 'kyc', <ReconciliationOutlined />),
        getItem(<Link to={'/kyc/update'}>{t('KYC 更新')}</Link>, 'update', <ReconciliationOutlined />),
      ]
    }
    if (active == '2') {
      list = [
        getItem(<C_Popover change={_change} />, '快捷方式', null, [], 'group'),
        getItem(t('BEUAccess'), 'acccess', <IdcardOutlined />, [
          getItem(<Link to={'/acccess/account-management'}>{t('账户管理')}</Link>, 'account-management', <PartitionOutlined />),
          getItem(<Link to={'/acccess/card-account'}>{t('卡账户')}</Link>, 'card-account', <SVGCard10 />),
        ], 'group'),

        getItem(t('主菜单'), '主菜单', null, [], 'group'),

        getItem(<Link to={'/overview'}>{t('总览')}</Link>, 'overview', <PieChartOutlined />),
        getItem(<Link to={'/transactions'}>{t('指令记录')}</Link>, 'transactions', <AuditOutlined />),
        getItem(<Link to={'/kyc'}>{t('KYC')}</Link>, 'kyc', <ReconciliationOutlined />),
        getItem(<Link to={'/kyc/update'}>{t('KYC 更新')}</Link>, 'update', <ReconciliationOutlined />),

        getItem(t('账户'), 'profile', null, [
          getItem(<Link to={'/profile/info'}>{t('个人资料')}</Link>, 'info', <RobotOutlined />),
          getItem(<Link to={'/profile/security'}>{t('安全')}</Link>, 'security', <UnlockOutlined />),
        ], undefined),
      ]
    }

    setItems([...list]);
  }

  return (
    <Sider ref={nodeRef}
      width={260}
      breakpoint="lg" collapsedWidth="0" {...others}>
      <Logo
        color="blue"
        asLink
        href={'/'}
        justify="center"
        gap="small"
        imgSize={{ h: 28, w: 28 }}
        style={{ padding: '1rem 0' }}
      />
      <ConfigProvider
        theme={{
          components: {
            Menu: {
              itemBg: 'none',
              itemSelectedBg: COLOR['100'],
              itemHoverBg: COLOR['50'],
              itemSelectedColor: COLOR['600'],
            },
          },
        }}
      >
        <Menu
          mode="inline"
          className='pl-[20px]'
          items={items}
          // onClick={onClick}
          openKeys={openKeys}
          onOpenChange={onOpenChange}
          selectedKeys={[current]}
          style={{ border: 'none' }}
        />
      </ConfigProvider>
    </Sider>
  );
};

export default SideNav;
