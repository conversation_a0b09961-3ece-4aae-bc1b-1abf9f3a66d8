import { configureStore, combineReducers } from '@reduxjs/toolkit';
import { persistReducer, persistStore } from 'redux-persist';
import storage from 'redux-persist/lib/storage';
import themeReducer, { ThemeState } from './theme/themeSlice.ts';
import languageReducer, { LanguageState } from './language/index.ts';
import userSliceReducer, { userState } from './user/index.ts';


// Define the state shape
interface RootState {
  theme: ThemeState;
  language: LanguageState,
  user: userState
}


// Configure store with persisted reducer
export const store = configureStore({
  reducer: persistReducer({
    key: 'root',
    storage,
    version: 1,
    whitelist: ['theme', 'language', 'user'], // 仅持久化 user 状态
  },
    combineReducers({
      theme: themeReducer,
      language: languageReducer,
      user: userSliceReducer,
    })
  ),
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
    }),
});

// Persistor
export const persistor = persistStore(store);

// Type for RootState
export type { RootState };
