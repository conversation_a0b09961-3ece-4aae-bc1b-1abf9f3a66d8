import {
  Card, Flex, Tag, Select, Input, Button, Modal,
  Avatar, Table, DatePicker, Tooltip, Dropdown,
  Form, Switch, InputNumber, Divider, Slider,
  Segmented, ConfigProvider,
  message, Upload, Checkbox,
} from 'antd';
import {
  LoadingOutlined, PlusOutlined, HomeOutlined,
  EyeOutlined, EyeInvisibleOutlined, SearchOutlined,
  UserOutlined, ExclamationOutlined, CheckOutlined
} from '@ant-design/icons';
import { PageHeader, AccountTopUpModal } from '@/components';
import { useTranslation } from 'react-i18next';
import { useEffect, useRef, useState } from 'react';
import CountUp from 'react-countup';
import { useNavigate, useLocation } from 'react-router-dom';
import { SVGCard1 } from '@/components/SVG_Components/SVG_Card'
import { SVGCard2 } from '@/components/SVG_Components/SVG_Card2'
import { SVGCard3 } from '@/components/SVG_Components/SVG_Card3'
import { SVGCard4 } from '@/components/SVG_Components/SVG_Card4'
import { SVGCard5 } from '@/components/SVG_Components/SVG_Card5'
import { SVGCard6 } from '@/components/SVG_Components/SVG_Card6'
import { SVGCard7 } from '@/components/SVG_Components/SVG_Card7'
import { SVG_Card8 } from '@/components/SVG_Components/SVG_Card8'
import { getAccountList } from '@/api/account' 
import countryCodeList from '@/assets/json/country.json'
import { useSelector } from 'react-redux'
import {
  getCardList, getCardConfigList, postCardLock,
  postCardTopup, postCardApply, postCardSpendingControls,
  postCardChangePin, postCardUpdatePhone, postCardSuspend,
  postCardCancel, postCardActivate, postCardUNSuspend,
  postCardUnlock, getCardAccountList
} from '@/api/card'
import { postCardAccountClientIdentityQuery } from '@/api/cardAccount'
import { postQueryCardCVC } from '@/api/sys'
import { postCountries, postOccupations } from '@/api/card'
import { queryTXList } from '@/api/transactions'
import { postAccountTocardAccount, postAccountWithdrawAccount } from '@/api/account'
import { TRANSACTION_ENUM, CARD_STATUS_ENUM, CARD_STATUS_COLOR_ENUM, TRANSACTION_STATUS_ENUM, EVENT_FEEENUM } from '@/utils/enum'

const { RangePicker } = DatePicker;
const beforeUpload = () => false


export const CardAccountDetailsPage = () => {
  // const context = useStylesContext();
  let defalutCheckedList = []
  localStorage.getItem('checkedList-card-account') && (defalutCheckedList = (JSON.parse(localStorage.getItem('checkedList-card-account') || '[]')))

  const navigate = useNavigate();
  const { t } = useTranslation()
  const [current, setCurrent] = useState<number>(1)
  const [total, setTotal] = useState<number>(0)
  const loadingRef = useRef(true)
  const [loading, setLoading] = useState(false)
  const [isEye, setEye] = useState(true)
  const [data, setData] = useState([])
  const [checked, setCecked] = useState(false)
  const [limitValue, setLimitValue]: any = useState(0)
  const location = useLocation()
  const [configData, setConfigData] = useState([])

  const [accountRechargeShow, setAccountRecharge] = useState(false);
  const [hairpinShow, setHairpinShow] = useState(false);
  const [downloadShow, setDownloadShow] = useState(false);
  const [lockingShow, setLockingShow] = useState(false);
  const [dayConsumptionLimitShow, setDayConsumptionLimitShow] = useState(false);
  const [dayATMLimitShow, setDayATMLimitShow] = useState(false);
  const [dayLimitShow, setDayLimitShow] = useState(false);
  const [singleLimitShow, setSingleLimitShow] = useState(false);
  const [pinShow, setPinShow] = useState(false);
  const [cardInfoShow, setCardInfoShow] = useState(false);
  const [updataPhoneShow, setUpdataPhoneShow] = useState(false)
  const [cancelCardShow, setCancelCardShow] = useState(false)
  const [pauseShow, setPauseShow] = useState(false)
  const [unpauseShow, setunPauseShow] = useState(false)
  const [unlockingShow, setUnLockingShow] = useState(false);
  const [balance, setBalance] = useState(0)

  const defaultPinCode = { 1: '', 2: '', 3: '', 4: '', 5: '', 6: '' }
  const [pinCode, setPinCode]: any = useState({ ...defaultPinCode })
  const [repeatPinCode, setRepeatPinCode]: any = useState({ ...defaultPinCode })
  const pinCodeRef: any = Object.keys({ ...defaultPinCode, 7: '' }).map(v => useRef(v))
  const repeatPinCodeRef: any = Object.keys({ ...defaultPinCode, 7: '' }).map(v => useRef(v))
  const [isIdentical, setIsIdentical] = useState(false)
  const [continuity, setContinuity] = useState(false)
  const updataPhoneFormRef: any = useRef()
  const [activeData, setActiveData] = useState<any>({})
  const [submitIng, setSubmint] = useState(false)
  const topupRef = useRef<any>()
  const applyRef = useRef<any>()
  const [reason, setReason] = useState()

  const [defaultList, setDefaultList] = useState([])
  const [front, setFront] = useState()
  const [back, setBack] = useState()
  const [activeCardShow, setActiveCardShow] = useState(false)
  const [tlementAccountValue, setTlementAccountValue] = useState<any>()
  const [cardActive, setCardActive] = useState<any>({})
  const [cardActiveCVC, setCardActiveCVC] = useState<any>({})

  const [countryList, setCountryList] = useState<any>([])
  const [occupationList, setOccupationList] = useState<any>([])
  const [active, setActive] = useState('card_account_transaction')
  const [TXData, setTXData] = useState<any>([])
  const [checkedList, setCheckedList]: any = useState(defalutCheckedList)

  const [TXAccountShow, setTXAccountShow] = useState(false)
  const [TXAccountShow2, setTXAccountShow2] = useState(false)
  const [clientIdentityModalOpen, setClientIdentityModalOpen] = useState(false)
  const [clientIdentityData, setClientIdentityData] = useState<any>(null)
  const topupRef2 = useRef<any>()
  const [timeValue, setTimeValue]: any = useState([])
  const userStore = useSelector((state: any) => { return state.user });


  // const ENUM: any = {
  //   "pending-activation": t("待激活"),
  //   "locked": t('已锁定'),
  //   "active": t('已激活'),
  //   "suspended": t('已暂停'),
  //   "cancelled": t('已取消'),
  // }

  // const colorENUM: any = {
  //   "pending-activation": '#FFA500',
  //   "locked": '#FF0000',
  //   "active": '#32CD32',
  //   "suspended": '#FF0000',
  //   "cancelled": '#A9A9A9',
  // }

  useEffect(() => {
    if (loadingRef.current) {
      loadingRef.current = false
      query() //卡列表
      queryDetails() //余额
      qyeryDefaultList()//  请求卡账户列表
      queryCardConfig() //配置列表
    }
  }, [])

  useEffect(() => {
    comparePins()
  }, [pinCode, repeatPinCode])

  useEffect(() => { queryCardAccountTXList() }, [active])


  useEffect(() => {
    if (dayConsumptionLimitShow || dayATMLimitShow || dayLimitShow || singleLimitShow) {
      const cardItem = activeData?.accCreditCardTypeId
      const ad: any = configData.find((v: any) => v?.id == cardItem)
      if (dayConsumptionLimitShow) {
        ad.limitAccountBanlace = ad.dailyPurchaseLimit
      }
      if (dayATMLimitShow) {
        ad.limitAccountBanlace = ad.dailyAtmLimit
      }
      if (dayLimitShow) {
        ad.limitAccountBanlace = ad.dailyLimit
      }
      if (singleLimitShow) {
        ad.limitAccountBanlace = ad.singleTransactionLimit
      }
      setCardActive(ad)
    }
  }, [dayConsumptionLimitShow, dayATMLimitShow, dayLimitShow, singleLimitShow, activeData])


  const pageChange = (page: number) => {
    setCurrent(page)
  }

  const query = (value?: any, type?: string) => {
    if (!loading) {
      setLoading(true)
      getCardList({
        cardAccountId: location?.state?.id,
        pageNum: current,
        pageSize: 100,
        name: type == 'search' ? value : undefined
      }).then((res: any) => {
        if (res.code == 200) {
          setTotal(res.data?.total)
          setData(res.data?.list || [])
        }
      }).catch(err => {
        console.log(err);
      }).finally(() => {
        setLoading(false)
      })
    }
  }


  const queryCardConfig = () => {
    getCardConfigList({
      pageNum: current,
      pageSize: 10,
      appid: location?.state?.appid
    }).then((res: any) => {
      if (res.code == 200) {
        setConfigData(res.data || [])
      }
    }).catch(err => {
      console.log(err);
    })
  }

// 查询 client-identity
  const getCardAccountClientIdentity = (id: any) => {
    postCardAccountClientIdentityQuery({
      cardAccountId: id
    }).then((res: any) => {
      if (res.code == 200) {
        // 弹窗显示
        setClientIdentityData(res.data?.individual || null);
        setClientIdentityModalOpen(true);
        return
      }
      message.open({ type: 'error', content: t(res.message) })
    }).catch(() => { })
  }

  const handleCloseClientIdentityModal = () => {
    setClientIdentityModalOpen(false);
    setClientIdentityData(null);
  };

  const queryCardAccountTXList = () => {

    queryTXList({
      accCardAccountId: location?.state?.id,
      pageNum: 1,
      pageSize: 10,
      event: active,
      postedAtStart: timeValue?.length && timeValue[0]['$d']?.getTime() || undefined,
      //@ts-ignore
      postedAtEnd: timeValue?.length && timeValue[1]['$d']?.getTime() || undefined,
    }).then((res: any) => {
      const { code } = res
      if (code == 200) {
        setTXData(res.data.list)
        // console.log(res.data.list);
        return
      }
    }).catch((err) => console.log(err))
      .finally(() => {
        setLoading(false)
      })
  }

  async function fetchUserList(username?: string): Promise<any> {
    return new Promise((resolve) => {
      getAccountList({
        pageNum: 1,
        pageSize: 10,
        appid: location.state.appid,
        name: username || undefined
      }).then((res: any) => {
        if (res.code == 200) {
          const list = res.data?.list.map((v: any) => {
            let blanace = v.tenantAccountAssets.filter((t: any) => t.asset == 'USD').map((vt: any) => {
              return vt.balanceAmount
            })
            blanace = blanace?.length ? blanace[0] : '0'
            return {
              value: v?.id,
              label: `${v.companyName}   ${v.accountName}     (${blanace})USD`
            }
          })
          resolve(list)
          return
        }
        resolve([])
      })
    })
  };

  const qyeryDefaultList = async () => {
    const list = await fetchUserList()
    setDefaultList(list)
  }

  const queryDetails = () => {
    getCardAccountList({
      pageNum: 1,
      pageSize: 20,
      id: location?.state?.id
    }).then((res: any) => {
      if (res.code == 200) {
        setBalance(res.data?.list[0].downBalance || '')
      }
    }).finally(() => {
      loadingRef.current = true
    })

    postCountries({}).then((res: any) => {
      if (res.code == 200) {
        setCountryList(res.data?.map((v: any) => {
          return {
            value: v.code,
            label: v.name
          }
        }) || [])
      }
    })
    postOccupations({}).then((res: any) => {
      if (res.code == 200) {
        // console.log('postOccupations', res);
        setOccupationList(res.data?.map((v: any) => {
          return {
            value: v.name,
            label: v.name
          }
        }) || [])
      }
    })
  }


  const accountRecharge = () => {
    topupRef.current?.validateFields().then((values: any) => {
      setSubmint(true)
      postCardTopup({
        accountId: values?.accountId?.value,
        cardAccountId: location?.state?.id,
        currency: 'USD',
        amount: values.amount,
      }).then((res: any) => {
        if (res.code == 200) {
          cancel()
          queryDetails()
          queryCardAccountTXList()
          message.open({ type: 'success', content: t('增值成功') })
          return
        }
        message.open({ type: 'error', content: t(res.message) })
      }).catch(() => { })
        .finally(() => {
          setSubmint(false)
        })
    })
  }
  const getBase64 = (img: any, callback: (url: string) => void) => {
    const reader = new FileReader();
    reader.addEventListener('load', () => callback(reader.result as string));
    reader.readAsDataURL(img);
  };


  const handleChange: any = (info: any) => {
    getBase64(info.file, (url: any) => {
      setFront(url);
    });
  };
  const handleChangeBack: any = (info: any) => {

    getBase64(info.file, (url: any) => {
      setBack(url);
    });
  };

  const uploadButton = (
    <button style={{ border: 0, background: 'none' }} type="button">
      {loading ? <LoadingOutlined /> : <PlusOutlined />}
      <div style={{ marginTop: 8 }}>Upload</div>
    </button>
  );


  const [dateOfBirth, setDateOfBirth] = useState()
  const [expiryDate, setExpiryDate] = useState()
  const onDateChange = (date: any, dateString: any) => {
    console.log(date, dateString);
    setDateOfBirth(dateString)
  }
  const onDateChange2 = (date: any, dateString: any) => {
    console.log(date, dateString);
    setExpiryDate(dateString)
  }

  const hairpin = () => {
    applyRef.current?.validateFields().then((values: any) => {
      setSubmint(true)
      postCardApply({
        ...values,
        cardAccountId: location?.state?.id,
        currency: 'USD',
        type: 'physical',
        countryCode: `${values['num1']}`,
        mobileNumber: `${values['num2']}`,
        amount: values.amount,
        cardholderInfo: {
          firstName: values?.firstName,
          lastName: values?.lastName,
          email: values.email,
          // mobile: `${values['num1']}${values['num2']}`,
          countryCode: `${values['num1']}`,
          mobileNumber: `${values['num2']}`,
          annualIncome: values.annualIncome,
          occupation: values.occupation,//职业
          position: values.position,
          dateOfBirth,//出生日期
          document: {
            front,
            back,
            type: values.type,
            number: values.cardNumber,
            country: values.country,//国家
            address: values.address,//国家
            expiryDate //有效期
          }
        }
      }).then((res: any) => {
        if (res.code == 200) {
          cancel()
          query()
          message.open({ type: 'success', content: t('发卡成功') })
          return
        }
        message.open({ type: 'error', content: t(res.message) })
      }).catch(() => { })
        .finally(() => {
          setSubmint(false)
        })
    })
  }


  const download = () => {
    cancel()
  }


  const locking = () => {
    setSubmint(true)
    postCardLock({
      cardAccountId: location?.state?.id,
      cardId: activeData.id
    }).then((res: any) => {
      if (res.code == 200) {
        cancel()
        query()
        message.open({ type: 'success', content: t('当前卡片已锁定') })
        return
      }
      message.open({ type: 'error', content: t(res.message) })
    }).catch(() => { })
      .finally(() => {
        setSubmint(false)
      })
  }
  const unlocking = () => {
    setSubmint(true)
    postCardUnlock({
      cardAccountId: location?.state?.id,
      cardId: activeData.id
    }).then((res: any) => {
      if (res.code == 200) {
        cancel()
        query()
        message.open({ type: 'success', content: t('当前卡片解锁') })
        return
      }
      message.open({ type: 'error', content: t(res.message) })
    }).catch(() => { })
      .finally(() => {
        setSubmint(false)
      })
  }


  const cancelCard = () => {
    setSubmint(true)
    postCardCancel({
      cardAccountId: location?.state?.id,
      cardId: activeData.id,
      reason: reason,
    }).then((res: any) => {
      if (res.code == 200) {
        cancel()
        query()
        message.open({ type: 'success', content: t('当前卡已取消') })
        return
      }
      message.open({ type: 'error', content: t(res.message) })
    }).catch(() => { })
      .finally(() => {
        setSubmint(false)
      })
  }


  const pause = () => {
    setSubmint(true)
    postCardSuspend({
      cardAccountId: location?.state?.id,
      cardId: activeData.id,
    }).then((res: any) => {
      if (res.code == 200) {
        cancel()
        query()
        message.open({ type: 'success', content: t('当前卡已暂停') })
        return
      }
      message.open({ type: 'error', content: t(res.message) })
    }).catch(() => { })
      .finally(() => {
        setSubmint(false)
      })
  }

  const cardUNSuspend = () => {
    setSubmint(true)
    postCardUNSuspend({
      cardAccountId: location?.state?.id,
      cardId: activeData.id,
      // phoneNumber: `${values.num1}${values.num2}`,
    }).then((res: any) => {
      if (res.code == 200) {
        cancel()
        query()
        message.open({ type: 'success', content: t('当前卡已恢复') })
        return
      }
      message.open({ type: 'error', content: t(res.message) })
    }).catch(() => { })
      .finally(() => {
        setSubmint(false)
      })
  }

  const activeCard = () => {
    setSubmint(true)
    postCardActivate({
      cardAccountId: location?.state?.id,
      cardId: activeData.id,
    }).then((res: any) => {
      if (res.code == 200) {
        cancel()
        query()
        message.open({ type: 'success', content: t('当前卡已激活') })
        return
      }
      message.open({ type: 'error', content: t(res.message) })
    }).catch(() => { })
      .finally(() => {
        setSubmint(false)
      })
  }


  const limit = () => {
    let par: any = {};
    (dayConsumptionLimitShow ? (par.dailyPurchaseLimit = limitValue) : '') ||
      (dayATMLimitShow ? (par.dailyAtmLimit = limitValue) : '') ||
      (dayLimitShow ? (par.dailyLimit = limitValue) : '') ||
      (singleLimitShow ? (par.singleTransactionLimit = limitValue) : '')

    setSubmint(true)
    postCardSpendingControls({
      cardAccountId: location?.state?.id,
      cardId: activeData.id,
      ...par
    }).then((res: any) => {
      if (res.code == 200) {
        cancel()
        query()
        message.open({ type: 'success', content: t('设置成功') })
        return
      }
      message.open({ type: 'error', content: t(res.message) })
    }).catch(() => { })
      .finally(() => {
        setSubmint(false)
      })

  }


  const upDataPhone = () => {
    updataPhoneFormRef.current?.validateFields().then((values: any) => {
      setSubmint(true)

      postCardUpdatePhone({
        cardAccountId: location?.state?.id,
        cardId: activeData.id,
        phoneNumber: {
          countryCode: values.num1,
          mobileNumber: values.num2,
        }
        // `${values.num1}${values.num2}`,
      }).then((res: any) => {
        if (res.code == 200) {
          cancel()
          query()
          message.open({ type: 'success', content: t('手机号码修改成功') })
          return
        }
        message.open({ type: 'error', content: t(res.message) })
      }).catch(() => { })
        .finally(() => {
          setSubmint(false)
        })
    })
  }





  const hasConsecutiveOrRepeatedDigits = (pin: string) => {
    // 检查是否有三个或更多连续的数字
    for (let i = 0; i < pin.length - 2; i++) {
      if (parseInt(pin[i]) === parseInt(pin[i + 1]) - 1 &&
        parseInt(pin[i + 1]) === parseInt(pin[i + 2]) - 1) {
        return setContinuity(false)
      }
    }

    // 检查是否有三个或更多重复的数字
    for (let i = 0; i < pin.length - 2; i++) {
      if (pin[i] === pin[i + 1] && pin[i + 1] === pin[i + 2]) {
        return setContinuity(false)
      }
    }
    setContinuity(true)

  }



  const comparePins = () => {
    // 首先检查两个 PIN 是否相等
    const pin1 = Object.values(pinCode).toString().replace(/\,/g, '')
    const pin2 = Object.values(repeatPinCode).toString().replace(/\,/g, '')

    if (pin2 || pin2) {
      if (pin1 == pin2) {
        setIsIdentical(true)
        hasConsecutiveOrRepeatedDigits(pin1)
      } else { setIsIdentical(false) }
    }
  }

  const pinRecharge = () => {
    setSubmint(true)
    postCardChangePin({
      cardAccountId: location?.state?.id,
      cardId: activeData.id,
      pin: Object.values(pinCode).toString().replace(/\,/g, ''),
    }).then((res: any) => {
      if (res.code == 200) {
        cancel()
        message.open({ type: 'success', content: t('PIN码设置成功') })
        queryDetails()
        return
      }
      message.open({ type: 'error', content: t(res.message) })
    }).catch(() => { })
      .finally(() => {
        setSubmint(false)
      })
  }


  const cancel = () => {
    setAccountRecharge(false)
    setHairpinShow(false)
    setDownloadShow(false)
    setLockingShow(false)
    setDayConsumptionLimitShow(false)
    setDayATMLimitShow(false)
    setDayLimitShow(false)
    setPinShow(false)
    setSingleLimitShow(false)
    setPinCode({ ...defaultPinCode })
    setRepeatPinCode({ ...defaultPinCode })
    setIsIdentical(false)
    setContinuity(false)
    setCardInfoShow(false)
    setUpdataPhoneShow(false)
    setCancelCardShow(false)
    setPauseShow(false)
    setActiveData({})
    setActiveCardShow(false)
    setBack(undefined)
    setFront(undefined)
    setunPauseShow(false)
    setUnLockingShow(false)
    setLimitValue(0)
    setSubmint(false)
    setTXAccountShow(false)
    setTXAccountShow2(false)
  }


  const TXCardAccount = () => {
    const accountId = location.state?.id || ''
    topupRef.current.validateFields().then((values: any) => {
      setSubmint(true)
      postAccountTocardAccount({
        sourceCardAccountId: accountId,
        destinationCardAccountId: values.destinationAccountId,
        amount: values.amount,
        appid: location?.state?.appid,
        "feeType": 1,
        "currency": "USD",
      }).then((res: any) => {
        const { code } = res
        if (code == 200) {
          setTXAccountShow(false)
          message.open({ type: 'success', content: t('成功') })
          cancel()
          queryDetails()
          queryCardAccountTXList()
          return
        }
        message.open({ type: 'error', content: t(res.message) })
      }).catch((err: any) => console.log(err))
        .finally(() => {
          setSubmint(false)
        })
    })
  }
  const TXCardAccount2 = () => {
    const accountId = location.state?.id || ''
    topupRef2.current.validateFields().then((values: any) => {
      setSubmint(true)
      postAccountWithdrawAccount({
        cardAccountId: accountId,
        accountId: values.accountId,
        amount: values.amount,
        appid: location?.state?.appid,
        "feeType": 1,
        "currency": "USD",
      }).then((res: any) => {
        const { code } = res
        if (code == 200) {
          setTXAccountShow(false)
          message.open({ type: 'success', content: t('成功') })
          cancel()
          queryDetails()
          queryCardAccountTXList()
          return
        }
        message.open({ type: 'error', content: t(res.message) })
      }).catch((err: any) => console.log(err))
        .finally(() => {
          setSubmint(false)
        })
    })
  }

  const goBack = () => {
    navigate('/acccess/card-account')
  }
  const queryCvc = (row: any) => {
    postQueryCardCVC({ id: row.id }).then((res: any) => {
      if (res.code == 200) {
        setCardActiveCVC(res.data)
        return
      }
      message.open({ type: 'error', content: t(res.message) })
    }).catch(() => { })
      .finally(() => {
      })
  }
  const SegmeChange = (item: any) => {
    setActive(item)
  }

  return (
    <div>
      <PageHeader
        title={''}
        breadcrumbs={[
          {
            title: (
              <span className=' cursor-pointer' onClick={goBack}>
                <HomeOutlined />
                <span className='ml-[10px]'>{t('卡片账户')}</span>
              </span>
            ),
          },
          {
            title: (
              <>
                <span>{t('账户详情')}</span>
              </>
            ),

          },
        ]}
      />
      <Flex>
        <Avatar size={64} icon={<UserOutlined />} />
        <div className='ml-[20px]'>
          <span className='text-[30px] font-bold'>{location?.state?.name}</span>
          <div>
            <Tag color="#000">USD</Tag>
            <Tag color="#0ebbbe">{t('预付')}</Tag>
          </div>
        </div>
      </Flex>
      <Card className='mt-[20px]' >
        <Flex align='center' justify='space-between'>
          <div>
            <Flex>
              <div className='text-[#666666] text-[16px] mr-[10px]'>{t('卡片账户余额')}</div>
              {
                isEye ?
                  <EyeOutlined onClick={() => setEye(false)} /> :
                  <EyeInvisibleOutlined onClick={() => setEye(true)} />
              }
            </Flex>
            <Flex className='text-[#305992] text-[32px] font-bold'>
              {
                isEye ?
                  <CountUp end={balance}
                    duration={0.5}
                    decimals={2}
                    suffix=" USD"
                  /> :
                  <span>******</span>
              }
            </Flex>
          </div>
          <Button shape='round' type='primary' className='min-w-[120px]' onClick={() => getCardAccountClientIdentity(location?.state?.id)} >{t('Query client-identity')}</Button>
          <Button shape='round' type='primary' className='min-w-[120px]' onClick={() => setTXAccountShow(true)} >{t('转账到卡账户')}</Button>
          <Button shape='round' type='primary' className='min-w-[120px]' onClick={() => setTXAccountShow2(true)} >{t('提取卡账户余额')}</Button>
          <Button shape='round' type='primary' className='min-w-[120px]' onClick={() => setAccountRecharge(true)} >{t('增值')}</Button>
        </Flex>
      </Card>
      <Card className='mt-[20px]'
      >
        <Flex className='font-bold text-[24px] mb-[20px]'
          justify='space-between'
        >{t("卡片总览")}

          <Button type='primary' shape='round' className='w-[150px]' onClick={() => setHairpinShow(true)}> {t('发卡')}</Button>
        </Flex>
        <Flex justify='space-between'>
          <Flex justify='space-between'>
            <Select className='w-[300px]'
              options={[
                // { label: 'USDT', value: 'USDT' },
                { label: 'USD', value: 'USD' }
              ]}
            />
          </Flex>
          <Input
            prefix={<SearchOutlined />}
            allowClear
            placeholder={t('请输入关键字进行搜索')}
            className='w-[30%] max-w-[300px]'
            onChange={(e: any) => query(e.target.value, 'search')}
          />
        </Flex>

        <Table
          loading={loading}
          className='mt-[20px]'
          rowKey={(row: any) => row?.id}
          columns={[
            {
              title: t('卡片'),
              dataIndex: 'name',
              key: 'name',
              render: (_, row) => <img src={row?.creditCardImage || '/card.png'} className='w-[60px] h-40px' />,
            },
            {
              title: `${t('姓名')}/${t('卡片名称')}`,
              dataIndex: 'age',
              key: 'age',
              render: (_, row: any) => <div>
                <div className='text-[14px] font-bold'>{row?.embossedName?.toLocaleUpperCase() || '-'}</div>
                <div className='text-[13px] text-[#666]'>{row.creditCardName || ''} **** {(row?.last4 || '').substr(-8)}</div>
              </div>
            },
            {
              title: t(`今日消费`),
              dataIndex: 'address',
              key: 'address',
              render: () => <span className='text-[#305992] text-[14px] font-bold'>0.00 USD</span>
            },
            {
              title: t('每日限额'),
              dataIndex: 'address',
              key: 'address',
              render: (_, row: any) => <div >
                <Flex justify='flex-end' className='text-[12px] text-[#000]'>{row?.dailyPurchaseLimit || '-'}
                  <Tooltip placement="top" title={`${t('每日消费限额')}`}>
                    <span className='text-[20px] ml-[10px]'>
                      <SVGCard2 />
                    </span>
                  </Tooltip>
                </Flex>
                <Flex justify='flex-end' className='text-[12px] text-[#000]'>{row?.dailyAtmLimit || '-'}
                  <Tooltip placement="top" title={`${t('每日ATM取款限额')}`}>
                    <span className='text-[20px] ml-[10px]'>
                      <SVGCard1 />
                    </span>
                  </Tooltip></Flex>
                <Flex justify='flex-end' className='text-[12px] text-[#000]'>{row?.dailyLimit || `-`}
                  <Tooltip placement="top" title={`${t('每日限额')}`}>
                    <span className='text-[20px] ml-[10px]'>
                      <SVGCard1 />
                    </span>
                  </Tooltip></Flex>
                <Flex justify='flex-end' className='text-[12px] text-[#000]'>{row?.singleTransactionLimit || `-`}
                  <Tooltip placement="top" title={`${t('单笔消费限额')}`}>
                    <span className='text-[20px] ml-[10px]'>
                      <SVGCard1 />
                    </span>
                  </Tooltip></Flex>
              </div>
            },
            {
              title: t('卡片状态'),
              dataIndex: 'status',
              key: 'status',
              render: (_, row: any) => <Flex align='center'>
                <div style={{ color: `${CARD_STATUS_COLOR_ENUM[row?.status]}` }} className='flex items-center'>
                  <span style={{ backgroundColor: `${CARD_STATUS_COLOR_ENUM[row?.status]}` }} className='w-[5px] h-[5px] rounded-[50%] mr-[5px] inline-block' ></span>
                  <span className='text-[13px]'> {CARD_STATUS_ENUM()[row?.status]}</span>
                </div>
              </Flex>
            },
            {
              title: t('操作'),
              dataIndex: 'address',
              key: 'address',
              render: (_, row: any) => {
                return row.status == 'cancelled' ? <></> : <Flex align='center' justify='space-between' className=' cursor-pointer'>
                  {
                    row?.status == 'locked' ? <Tooltip placement="top" title={`${t('解除锁定卡片')}`}>
                      <span className='text-[20px]' onClick={() => {
                        setActiveData(row)
                        setUnLockingShow(true)
                      }}>
                        <SVGCard3 />
                      </span>
                    </Tooltip> : <Tooltip placement="top" title={`${t('锁定卡片')}`}>
                      <span className='text-[20px]' onClick={() => {
                        setActiveData(row)
                        setLockingShow(true)
                      }}>
                        <SVGCard3 />
                      </span>
                    </Tooltip>
                  }

                  <Dropdown placement="bottomRight" menu={{
                    items: [
                      {
                        key: '1',
                        onClick: () => {
                          setActiveData({ ...row, limitAccount: row?.dailyPurchaseLimit })
                          setDayConsumptionLimitShow(true)
                          setLimitValue(row?.dailyPurchaseLimit || 0)
                        },
                        label: <span className='text-[13px]'>{t('每日消费限额')}</span>
                      },
                      {
                        key: '2',
                        onClick: () => {
                          setActiveData({ ...row, limitAccount: row?.dailyAtmLimit })
                          setDayATMLimitShow(true)
                          setLimitValue(row?.dailyAtmLimit || 0)
                        },
                        label: <span className='text-[13px]' >{t('每日ATM取款限额')}</span>
                      },
                      {
                        key: '3',
                        onClick: () => {
                          setActiveData({ ...row, limitAccount: row?.dailyLimit })
                          setDayLimitShow(true)
                          setLimitValue(row?.dailyLimit || 0)
                        },
                        label: <span className='text-[13px]' >{t('每日限额')}</span>
                      },
                      {
                        key: '4',
                        onClick: () => {
                          setActiveData({ ...row, limitAccount: row?.singleTransactionLimit })
                          setSingleLimitShow(true)
                          setLimitValue(row?.singleTransactionLimit || 0)
                        },
                        label: <span className='text-[13px]' >{t('单笔消费限额')}</span>
                      }
                    ]
                  }}>
                    <span className='text-[20px]'><SVGCard4 /></span>
                  </Dropdown>
                  <Tooltip title={`${t('变更ATMPIN码')}`}>
                    <span className='text-[20px]' onClick={() => {
                      setActiveData(row)
                      setPinShow(true)

                    }} > <SVGCard5 /></span>
                  </Tooltip>
                  <Tooltip title={`${t('查看卡信息')}`}>
                    <span className='text-[20px]' onClick={() => {
                      setActiveData(row)
                      queryCvc(row)
                      setCardInfoShow(true)

                    }}><SVGCard6 /></span>
                  </Tooltip>
                  <Tooltip title={`${t('更新手机号码')}`}>
                    <span className='text-[20px]' onClick={() => {
                      setActiveData(row)
                      setUpdataPhoneShow(true)

                    }}><SVGCard7 /></span>
                  </Tooltip>
                  <Dropdown placement="bottomRight" menu={{
                    items: [
                      row.status == 'active' ? {
                        key: '1',
                        onClick: () => {
                          setActiveData(row)
                          setCancelCardShow(true)
                        },
                        label: <span className='text-[13px]' >{t('取消卡片')}</span>
                      } : null,
                      row.status == 'active' ? {
                        key: '2',
                        onClick: () => {
                          setActiveData(row)
                          setPauseShow(true)
                        },
                        label: <span className='text-[13px]' >{t('暂停卡片')}</span>
                      } : null,
                      row.status == 'suspended' ? {
                        key: '3',
                        onClick: () => {
                          setActiveData(row)
                          setunPauseShow(true)
                        },
                        label: <span className='text-[13px]' >{t('取消暂停卡片')}</span>
                      } : null,
                      row.status == 'pending-activation' ?
                        {
                          key: '4',
                          onClick: () => {
                            setActiveData(row)
                            setActiveCardShow(true)
                          },
                          label: <span className='text-[13px]' >{t('激活卡片')}</span>
                        } : null
                    ]
                  }}>
                    <span className='text-[20px]'><SVG_Card8 /></span>
                  </Dropdown>
                </Flex>
              }

            },
          ]}
          dataSource={data}
          pagination={false}
        />
        <Flex className='font-bold text-[24px] mb-[10px] mt-[30px]'
          justify='space-between'
        >{t("交易记录")}

          <Button onClick={() => setDownloadShow(true)} type='primary' shape='round' className='w-[150px]' disabled > {t('下载交易报告')}</Button>
        </Flex>
        <ConfigProvider theme={{
          token: {
            borderRadius: 20,
            borderRadiusLG: 20,
            borderRadiusSM: 20,
            borderRadiusXS: 20,
          },
          components: {
            Segmented: {
              itemSelectedBg: '#274673',
              itemSelectedColor: '#fff',
              itemColor: '#fff',
            }
          }
        }}>

          <Segmented options={TRANSACTION_ENUM()?.slice(5, 8)}
            value={active}
            size='small'
            onChange={SegmeChange}
            className='h-[50px] mb-[20px]
              p-[8px] rounded-[50px] bg-[#efefef] _Segmented'
          />
        </ConfigProvider>

        <Flex justify='space-between'>
          <Flex className='mt-[20px]'>
            <RangePicker
              showTime
              onChange={(e: any) => { setTimeValue(e) }}
              className='w-[300px]' />
            <Button onClick={queryCardAccountTXList} className='ml-[10px]'>{t('搜索')}</Button>
          </Flex>
          {/* <Flex justify='space-between'>
            <Button className='mr-[20px]' onClick={queryCardAccountTXList}><SyncOutlined /></Button>
            <RangePicker className='w-[300px]' />
          </Flex> */}
          {/* <Input
            prefix={<SearchOutlined />}
            allowClear
            placeholder={t('请输入关键字进行搜索')}
            className='w-[30%] max-w-[300px]'
            onChange={(e: any) => queryCardAccountTXList(e.target.value, 'search')}
          /> */}
        </Flex>
        <Table
          loading={loading}
          className='mt-[20px] _store_table'
          dataSource={TXData}
          rowKey={(row: any) => row?.id}
          scroll={{ x: 1000 }}
          columns={[
            {
              title: t('ID'),
              dataIndex: 'id',
              key: 'id',
              width: 100,
              sorter: { multiple: 1 },
              filteredValue: checkedList,
              filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
                <div className='p-[20px] bg-[#fff] w-[600px]'>
                  <div>{t("请选择需要隐藏的字段")}</div>
                  <Checkbox.Group
                    options={[
                      { label: t('数据id'), value: 'dataId' },
                      { label: t('唯一key'), value: 'idempotencyKey' },
                      { label: t('账户id'), value: 'accAccountId' },
                      { label: t('卡账户ID'), value: 'cardAccountId' },
                      { label: t('卡id'), value: 'accCardId' },
                      { label: t('事件ID'), value: 'eventId' },
                      { label: t('事件'), value: 'event' },
                      { label: t('链'), value: 'chain' },
                      { label: t('块高'), value: 'blockNumber' },
                      { label: t('交易货币'), value: 'currency' },
                      { label: t('交易金额'), value: 'amount' },
                      { label: t('发起人的层级'), value: 'level' },
                      { label: t('旧余额'), value: 'oldBalance' },
                      { label: t('新余额'), value: 'newBalance' },
                      { label: t('卡帐户交易状态'), value: 'status' },
                      { label: t('入账日期'), value: 'createdAt' },
                      { label: t('到账日期'), value: 'postedAt' },
                      { label: t('创建时间'), value: 'creTime' },
                      { label: t('更新时间'), value: 'updTime' },
                    ]}
                    value={selectedKeys}
                    onChange={(e) => {
                      setSelectedKeys(e)
                    }}
                  />
                  <div style={{ marginTop: 8 }}>
                    <Button
                      type="primary"
                      className='mr-[10px]'
                      onClick={() => {
                        confirm();
                        setCheckedList(selectedKeys)
                      }}
                      size="small"
                      style={{ width: 90 }}
                    >
                      {t('确定')}
                    </Button>
                    <Button
                      onClick={() => {
                        clearFilters && clearFilters()
                        setSelectedKeys([])
                      }}
                      size="small"
                      style={{ width: 90 }}
                    >
                      {t('重置')}
                    </Button>
                  </div>
                </div>)
            },
            {
              title: t('数据ID'),
              dataIndex: 'dataId',
              width: 130,
              key: 'dataId',
              ellipsis: true,
              hidden: checkedList.includes('dataId'),
            },
            {
              title: t('唯一KEY'),
              dataIndex: 'idempotencyKey',
              key: 'idempotencyKey',
              width: 130,
              ellipsis: true,
              hidden: checkedList.includes('idempotencyKey'),
            },
            {
              title: t('账户ID'),
              dataIndex: 'accAccountId',
              width: 130,
              ellipsis: true,
              key: 'accAccountId',
              hidden: checkedList.includes('accAccountId'),
            },
            {
              title: t('卡账户ID'),
              width: 130,
              dataIndex: 'accCardAccountId',
              key: 'accCardAccountId',
              ellipsis: true,
              hidden: checkedList.includes('accCardAccountId'),
            },
            {
              title: t('卡ID'),
              dataIndex: 'accCardId',
              ellipsis: true,
              width: 130,
              key: 'accCardId',
              hidden: checkedList.includes('accCardId'),
            },
            {
              title: t('事件ID'),
              dataIndex: 'eventId',
              width: 130,
              key: 'eventId',
              ellipsis: true,
              hidden: checkedList.includes('eventId'),
            },
            {
              title: t('事件'),
              width: 130,
              dataIndex: 'event',
              key: 'event',
              ellipsis: true,
              sorter: { multiple: 10 }, //eventAsc  eventDesc
              hidden: checkedList.includes('event'),
              render: (text: any) => EVENT_FEEENUM()[text] || '-'
            },
            // {
            //   title: t('链'),
            //   width: 130,
            //   dataIndex: 'chain',
            //   key: 'chain',
            //   ellipsis: true,
            //   hidden: checkedList.includes('chain'),
            // },
            // {
            //   title: t('块高'),
            //   width: 130,
            //   dataIndex: 'blockNumber',
            //   key: 'blockNumber',
            //   ellipsis: true,
            //   sorter: { multiple: 10 }, //blockNumberAsc  blockNumberDesc
            //   hidden: checkedList.includes('blockNumber'),
            // },
            {
              title: t('交易货币'),
              width: 130,
              dataIndex: 'currency',
              key: 'currency',
              ellipsis: true,
              hidden: checkedList.includes('currency'),
            },
            {
              title: t('交易金额'),
              width: 130,
              dataIndex: 'amount',
              key: 'amount',
              ellipsis: true,
              hidden: checkedList.includes('amount'),
              sorter: { multiple: 2 }, //amountAsc  amountDesc
            },
            {
              title: t('旧余额'),
              width: 130,
              dataIndex: 'oldBalance',
              key: 'oldBalance',
              ellipsis: true,
              sorter: { multiple: 4 }, //oldBalanceAsc  oldBalanceDesc
            },
            {
              title: t('新余额'),
              dataIndex: 'newBalance',
              width: 130,
              key: 'newBalance',
              ellipsis: true,
              hidden: checkedList.includes('newBalance'),
              sorter: { multiple: 5 }, //newBalanceAsc  newBalanceDesc
            },

            {
              title: t('交易状态'),//待处理、已过账、拒绝、无效
              dataIndex: 'status',
              width: 130,
              ellipsis: true,
              key: 'status',
              hidden: checkedList.includes('status'),
              sorter: { multiple: 6 }, //statusAsc  statusDesc
              render: (text: any) => TRANSACTION_STATUS_ENUM()[text] || '-'
            },
            {
              title: t('发起人的层级'),
              width: 130,
              dataIndex: 'level',
              key: 'level',
              ellipsis: true,
              hidden: checkedList.includes('level'),
              sorter: { multiple: 3 }, //levelAsc  levelDesc
            },

            {
              title: t('入账日期'),
              dataIndex: 'createdAt',
              width: 190,
              ellipsis: true,
              key: 'createdAt',
              hidden: checkedList.includes('createdAt'),
              sorter: { multiple: 7 }, //postedAtAsc  postedAtDesc
              render: (text: any) => text ? new Date(+text).toLocaleString() : '-'
            },
            {
              title: t('到账日期'),
              dataIndex: 'postedAt',
              width: 190,
              key: 'postedAt',
              ellipsis: true,
              hidden: checkedList.includes('postedAt'),
              sorter: { multiple: 8 }, //createdAtAsc  createdAtDesc
              render: (text: any) => text ? new Date(+text).toLocaleString() : '-'
            },
            {
              title: t('创建时间'),
              dataIndex: 'creTime',
              width: 190,
              key: 'creTime',
              ellipsis: true,
              hidden: checkedList.includes('creTime'),
              sorter: { multiple: 9 }, //creTimeAsc  creTimeDesc
            },
            {
              title: t('更新时间'),
              dataIndex: 'updTime',
              width: 190,
              ellipsis: true,
              key: 'updTime',
              hidden: checkedList.includes('updTime'),
              sorter: { multiple: 10 }, //updTimeAsc  updTimeDesc
            },
          ]}
          pagination={{
            hideOnSinglePage: true,
            pageSize: 10,
            current,
            total,
            onChange: pageChange,
            position: ['bottomRight']
          }}
        />
      </Card>
      <AccountTopUpModal
        accountRechargeShow={accountRechargeShow}
        cancel={cancel}
        submitIng={submitIng}   
        cardAccountId={location?.state?.id}
        appid={location?.state?.appid}
      />

      {/* 发卡 */}
      <Modal
        title={t("发卡")}
        open={hairpinShow}
        width={800}
        centered
        maskClosable={false}
        keyboard={false}
        onCancel={cancel}
        destroyOnClose={true}
        footer={null}
      >
        <Form
          layout="vertical"
          ref={applyRef}
        >
          <Flex>
            <div>
              <Flex >
                <Form.Item
                  label={t('名')}
                  name="firstName"
                  className='w-[50%] mr-[10px]'
                  rules={[
                    { required: true, message: t('名') },
                  ]}
                >
                  <Input />
                </Form.Item>
                <Form.Item
                  name="lastName"
                  label={t('姓氏')}
                  className='w-[50%]'
                  rules={[
                    { required: true, message: t('姓氏') },
                  ]}
                >
                  <Input />
                </Form.Item>
              </Flex>
              <Form.Item
                className='mb-[0] w-full'
                label={t('手机号码')}
              >
                <Flex >
                  <Form.Item
                    name="num1"
                    className='w-[200px] mr-[20px]'
                    rules={[
                      { required: true, message: t('') },
                    ]}
                  >
                    <Select
                      showSearch
                      options={
                        countryCodeList.map((v) => {
                          return {
                            value: v.code,
                            label: v.code,
                          }
                        })
                      } />
                  </Form.Item>
                  <Form.Item
                    name="num2"
                    className='w-full'
                    rules={[
                      { required: true, message: t('请输入账户名称') },
                    ]}
                  >
                    <Input />
                  </Form.Item>
                </Flex>
              </Form.Item>


              <Form.Item
                label="E-Mail"
                name="email"
                className='w-full'
                rules={[
                  { required: true, message: t('请输入邮箱') },
                ]}
              >
                <Input />
              </Form.Item>
              <Flex>
                <Form.Item
                  label={t('证件类型')}
                  name="type"
                  className='w-[40%] mr-[10px]'
                  rules={[
                    { required: true, message: t('证件类型') },
                  ]}
                >
                  <Select
                    placeholder={t('证件类型')}
                    options={[{
                      value: 'passport',
                      label: t('护照'),
                    },
                    {
                      value: 'drivers-license',
                      label: t('驾照'),
                    },
                    {
                      value: 'national-id',
                      label: t('国民身份证'),
                    }
                    ]
                    } />
                </Form.Item>


                <Form.Item
                  label={t('证件号码')}
                  name="cardNumber"
                  className='w-full'
                  rules={[
                    { required: true, message: t('请输入证件号码') },
                  ]}
                >
                  <Input />
                </Form.Item>
              </Flex>
              <Flex>
                <Form.Item
                  label={t('出生日期')}
                  name="dateOfBirth"
                  className='w-full'
                  rules={[
                    { required: true, message: t('出生日期') },
                  ]}
                >
                  <DatePicker onChange={onDateChange} />
                </Form.Item>
                <Form.Item
                  label={t('证件有效期')}
                  name="expiryDate"
                  className='w-full'
                  rules={[
                    { required: true, message: t('证件有效期') },
                  ]}
                >
                  <DatePicker onChange={onDateChange2} />
                </Form.Item>

              </Flex>
              <Flex>
                <Form.Item
                  label={t('证件照正面')}
                  name="front"
                  className='w-full'
                  rules={[
                    { required: true, message: t('证件照正面') },
                  ]}
                >
                  <Upload
                    name="front"
                    listType="picture-card"
                    className="avatar-uploader"
                    showUploadList={false}
                    accept='image/png'
                    beforeUpload={beforeUpload}
                    onChange={handleChange}
                  >
                    {front ? <img src={front} alt="avatar" className='w-full h-auto max-w-[100px] max-h-[100px]' /> : uploadButton}
                  </Upload>
                </Form.Item>

                <Form.Item
                  label={t('证件照反面')}
                  name="back"
                  className='w-full'
                  rules={[
                    { required: true, message: t('证件照反面') },
                  ]}
                >
                  <Upload
                    name="front"
                    accept='image/png'
                    listType="picture-card"
                    className="avatar-uploader"
                    showUploadList={false}
                    beforeUpload={beforeUpload}
                    onChange={handleChangeBack}
                  >
                    {back ? <img src={back} alt="avatar" className='w-full h-auto max-w-[100px] max-h-[100px]' /> : uploadButton}
                  </Upload>
                </Form.Item>
              </Flex>
            </div>
            <div className='w-[50%] ml-[20px]'>
              <Form.Item
                label={t('年收入')}
                name="annualIncome"
                className='w-full'
                rules={[
                  { required: true, message: t('请输入年收入') },
                ]}
              >
                <Input />
              </Form.Item>
              <Form.Item
                label={t('职业')}
                name="occupation"
                className='w-full'
                rules={[
                  { required: true, message: t('请输入职业') },
                ]}
              >
                <Select
                  showSearch
                  placeholder={t('职业')}
                  options={occupationList} />
              </Form.Item>
              <Form.Item
                label={t('职位')}
                className='w-[60%]'
                name="position" >
                <Input />
              </Form.Item>
              <Flex>
                <Form.Item
                  label={t('国家')}
                  name="country"
                  className='w-[30%] mr-[10px]'
                  rules={[
                    { required: true, message: t('国家') },
                  ]}
                >
                  <Select
                    showSearch
                    placeholder={t('国家')}
                    options={countryList} />
                </Form.Item>
                <Form.Item
                  label={t('地址')}
                  className='w-[60%]'
                  name="address" >
                  <Input />
                </Form.Item>

              </Flex>
              <Form.Item
                label={t('卡类型')}
                name="productId"
                rules={[
                  { required: true, message: t('卡类型') },
                ]}
              >
                <Select
                  placeholder={t('选择卡类型')}
                  options={
                    configData.map((v: any) => {
                      return { label: `${v.creditCardName}${userStore?.user?.user?.roleType == "0" ? `-${v.cardMerchantName}` : ''}`, value: v.id }
                    })
                  } />
              </Form.Item>
              <Form.Item
                label={<span className='text-[12px]'>{t('压印名称')}</span>}
                name="embossedName"
              >
                <Input className='w-full' />
              </Form.Item>
              <Form.Item
                //@ts-ignore
                label={<span className='text-[12px]'>{t('卡号')}</span>}
                name="cardSuffix"
                rules={[
                  // { required: true, message: t('卡号') },
                ]}
              >
                <Input className='w-full' />
              </Form.Item>

            </div>
          </Flex>
        </Form>
        <Flex>
          <Button className='w-full mr-[5px]' shape='round' onClick={cancel}>{t('取消')}</Button>
          <Button className='w-full ml-[5px]' shape='round' type='primary' loading={submitIng} onClick={hairpin}>{t('发卡')}</Button>
        </Flex>
      </Modal>

      {/* 下载交易报告 */}
      <Modal
        title={t("下载交易报告")}
        open={downloadShow}
        destroyOnClose={true}
        className='top-[30vh]'
        width={400}
        onCancel={cancel}
        footer={null}
      >
        <div className='text-[14px] font-bold mb-[10px]'>
          {t('您可以选择日期范围或下载所有日期范围')}
        </div>
        <Form
          layout="vertical"
        >
          <Form.Item
            label={<span className='text-[12px]'>{t('日期范围')}</span>}
            name="account"
          >
            <RangePicker className='w-full' />
          </Form.Item>
        </Form>
        <Button className='w-full' shape='round'
          onClick={download}
          type='primary'>{t('下载')}</Button>
      </Modal>

      {/* 锁定卡片指示确认 */}
      <Modal
        title={t("锁定卡片指示确认")}
        open={lockingShow}
        centered
        width={400}
        onCancel={cancel}
        destroyOnClose={true}
        footer={null}
      >

        <Flex justify='center' className=' relative'>
          <div className=' absolute left-[50px] bottom-[30px] text-[#fff]'>
            <div className='text-[20px] font-bold'>{activeData?.embossedName?.toLocaleUpperCase()}</div>
            <div>****  *****  **** {(activeData?.last4 || '').substr(-8)}</div>
          </div>
          <img src={activeData.creditCardImage || '/card.png'} className='w-[300px]' alt="" />
        </Flex>
        <Flex className='mt-[20px]'>
          <Button className='w-full mr-[5px]' shape='round' onClick={cancel}>{t('取消')}</Button>
          <Button className='w-full ml-[5px]' shape='round' type='primary' loading={submitIng} onClick={locking}>{t('下达指示')}</Button>
        </Flex>
      </Modal>

      {/* limit */}
      <Modal
        title={
          (dayConsumptionLimitShow ? t("每日消费限额") : '') ||
          (dayATMLimitShow ? t("每日 ATM 取款限额") : '') ||
          (dayLimitShow ? t("每日限额") : '') ||
          (singleLimitShow ? t("单笔消费限额") : '')
        }
        open={
          dayConsumptionLimitShow ||
          dayATMLimitShow ||
          dayLimitShow ||
          singleLimitShow
        }
        width={400}
        centered
        onCancel={cancel}
        destroyOnClose={true}
        footer={null}
      >
        <div className='font-bold text-[12px]'>{t('设定每笔交易支出限额。')}</div>
        <div className='bg-[#f8f8f8] p-[16px] rounded-[10px] mb-[20px]'>
          <Flex justify='space-between' align='center'>
            <div className='text-[12px]'>
              <div className='font-bold'>{t('使用全部担保信贷限额')}</div>
              <div>{t('卡片限额将设为您的担保信贷额度的最大限额')}</div>
              <div className='text-[red]'>{t('请小心使用')}</div>
            </div>
            <Switch checked={checked} onChange={setCecked} />
          </Flex>
        </div>
        {
          checked ? '' : <div className='flex justify-center items-center flex-col'  >
            <InputNumber prefix="USD"
              min={0}
              controls={false}
              max={cardActive.limitAccountBanlace || 0}
              variant={'borderless'}
              value={(parseFloat(limitValue) || 0)}
              className='w-[300px] min-w-[100px] m-auto text-center _input-number'
              onChange={(e: any) => setLimitValue(e)} />
            <Divider className='my-[5px]' />
            <span className='font-bold text-[13px]'> {t('每日限额')}</span>
            <Slider step={1}
              value={typeof limitValue === 'number' ? limitValue : 0}
              onChange={(e: any) => setLimitValue(e)} min={0} max={cardActive.limitAccountBanlace || 0}
              className='w-full mt-[30px]' />
            <div className='flex w-full -mt-[5px] text-[12px] font-bold justify-between'>
              <span>0 USD</span>
              <span>{cardActive.limitAccountBanlace || 0} USD</span>
            </div>
          </div>
        }


        <Flex className='mt-[20px]'>
          <Button className='w-full mr-[5px]' shape='round' onClick={cancel}>{t('取消')}</Button>
          <Button className='w-full ml-[5px]' shape='round' type='primary' loading={submitIng} onClick={limit}>{t('设定限额')}</Button>
        </Flex>
      </Modal>

      {/* pin 码 */}
      <Modal
        maskClosable={false}
        keyboard={false}
        title={t("变更ATMPIN码")}
        open={pinShow}
        centered
        width={400}
        onCancel={cancel}
        destroyOnClose={true}
        footer={null}
      >
        <div className='text-[12px] font-bold mb-[10px] mt-[20px]'>
          {t('创建新6位数ATMPIN码')}
        </div>

        <Form
          layout="vertical"
        >
          <Form.Item
            className='mb-[5px]'
            label={<span className='text-[12px] mb-[0px]'>{t('输入新PIN码')} </span>}
          >
            <Flex align='center'>
              {
                [1, 2, 3, 4, 5, 6].map((v) => {
                  return <InputNumber
                    key={`pin-code-${v}`}
                    ref={pinCodeRef[v]}
                    value={pinCode[v]}
                    onInput={(value: any) => {
                      const c: any = {}
                      c[v] = `${value}`.slice(-1)
                      setPinCode({ ...pinCode, ...c })
                      if (value) pinCodeRef[v + 1]?.current?.focus()
                    }}
                    maxLength={2} min={0} controls={false} className='mr-[10px] _input_number_center' />
                })
              }
            </Flex>
          </Form.Item>
          <Form.Item
            className='mb-[25px]'
            label={<span className='text-[12px]  mb-[0px]'>{t('重新输入新PIN码')}</span>}
          >

            <Flex align='center'>
              {
                [1, 2, 3, 4, 5, 6].map((v) => {
                  return <InputNumber
                    key={`repeat-pin-code-${v}`}
                    ref={repeatPinCodeRef[v]}
                    value={repeatPinCode[v]}
                    onInput={(value: any) => {
                      console.log(value);
                      const c: any = {}
                      c[v] = `${value}`.slice(-1)
                      setRepeatPinCode({ ...repeatPinCode, ...c })
                      if (value) repeatPinCodeRef[v + 1]?.current?.focus()
                    }}
                    maxLength={2} min={0} controls={false} className='mr-[10px] _input_number_center' />
                })
              }
            </Flex>
          </Form.Item>
          <div className='mb-[10px]'>
            <div className={continuity ? `text-[#23bd6a] text-[12px]` : 'text-[12px] font-bold'}> {
              continuity ? <CheckOutlined /> : <ExclamationOutlined />
            } {t('无3个或更多连续/重复数字')}</div>
            <div className={isIdentical ? `text-[#23bd6a] text-[12px]` : 'text-[12px] font-bold'}>{
              isIdentical ? <CheckOutlined /> : <ExclamationOutlined />
            } {t('输入的两个PIN码匹配')} </div>
          </div>
        </Form>
        <Flex>
          <Button className='w-full mr-[5px]' shape='round' onClick={cancel}>{t('取消')}</Button>
          <Button className='w-full ml-[5px]' shape='round' type='primary' loading={submitIng} onClick={pinRecharge}>{t('更新')}</Button>
        </Flex>
      </Modal>

      {/* 卡片资讯 */}
      <Modal
        maskClosable={false}
        keyboard={false}
        title={t("卡片资讯")}
        open={cardInfoShow}
        centered
        width={400}
        onCancel={cancel}
        destroyOnClose={true}
        footer={null}
      >

        <Flex justify='center' className=' relative'>
          <div className=' absolute left-[50px] bottom-[20px] text-[#fff]'>
            <div className='text-[20px] font-bold'>{activeData?.embossedName?.toLocaleUpperCase()}</div>
            <div className='mb-[5px]'>****  *****  **** {(activeData?.last4 || '').substr(-8)}</div>
            <Flex justify='space-between'>
              <div className='text-[10px]'>
                <div>{t("到期日")}</div>
                <div>{activeData?.expiryMonth}/{activeData?.expiryYear}</div>
              </div>
              <div className='text-[10px]'>
                <div>CVC2</div>
                <div>{cardActiveCVC}</div>
              </div>
            </Flex>
          </div>
          <img src={activeData.creditCardImage || '/card.png'} className='w-[300px]' alt="" />
        </Flex>
        <Flex className='mt-[20px]'>
          <Button className='w-full mr-[5px]' shape='round' onClick={cancel}>{t('关闭')}</Button>
        </Flex>
      </Modal>


      {/* 更新手机号码 */}
      <Modal
        maskClosable={false}
        keyboard={false}
        title={t("更新手机号码")}
        open={updataPhoneShow}
        className=''
        centered
        onCancel={cancel}
        width={500}
        footer={null}
      >
        <div className='text-[14px] font-bold mb-[10px]'>
          {t('请填写以下表格以更新手机号码')}
        </div>
        <Form
          ref={updataPhoneFormRef}
          autoComplete="off"
          requiredMark={false}
          layout="vertical"
        >
          <Form.Item
            label={t('输入新手机号码')}
            name={'输入新手机号码'}
          >
            <div className='bg-[#f8f8f8] p-[16px] text-[12px]'>
              <div className='text-[#666]'>{t('目前手机号码')}</div>
              <div>(+{activeData?.countryCode}) {`${activeData?.mobileNumber}`.replace(/(\d{2})\d+(\d{4})/, "$1****$2")}</div>
            </div>
          </Form.Item>
          <Form.Item
            className='mb-[0] w-full'
            label={t('手机号码')}
          >
            <Flex >
              <Form.Item
                name="num1"
                className='w-[150px] mr-[20px]'
                rules={[
                  { required: true, message: t('') },
                ]}
              >
                <Select
                  showSearch
                  options={
                    countryCodeList.map((v) => {
                      return {
                        value: v.code,
                        label: v.code,
                      }
                    })
                  } />
              </Form.Item>
              <Form.Item
                name="num2"
                className='w-full'
                rules={[
                  { required: true, message: t('请输入手机号码') },
                ]}
              >
                <Input />
              </Form.Item>
            </Flex>
          </Form.Item>
        </Form>
        <Flex>
          <Button className='w-full mr-[5px]' shape='round' onClick={cancel}>{t('取消')}</Button>
          <Button className='w-full ml-[5px]' shape='round' type='primary' loading={submitIng} onClick={upDataPhone}>{t('更新')}</Button>
        </Flex>
      </Modal>

      {/* 取消卡片 */}
      <Modal
        maskClosable={false}
        keyboard={false}
        title={t("取消卡片")}
        open={cancelCardShow}
        width={400}
        centered
        onCancel={cancel}
        destroyOnClose={true}
        footer={null}
      >
        <div className='mb-[10px] font-bold mt-[20px]'>
          {t('请选择您取消卡片的原因')}
        </div>
        <Form
          layout="vertical"
        >
          <Form.Item
            label={<span className='text-[12px]'>{t('原因')}</span>}
            rules={[
              { required: true, message: t('原因') },
            ]}
            name="reason" >
            <Select
              placeholder={t('原因')}
              defaultValue={''}
              onChange={(e: any) => (setReason(e))}
              options={[
                { label: t('欺诈'), value: 'fraud' },
                { label: t('丢失'), value: 'lost' },
                { label: t('邮件退回'), value: 'mail-rejected' },
                { label: t('未收到卡片'), value: 'card-not-received' },
                { label: t('过期'), value: 'expired' },
                { label: t('被盗'), value: 'stolen' },
                { label: t('损坏'), value: 'broken' },
                { label: t('非活跃'), value: 'inactive' },
                { label: t('更换'), value: 'replaced' },
                { label: t('锁定'), value: 'locked' },
                { label: t('其他'), value: 'other' },
                { label: t('损伤'), value: 'damaged' }
              ]} />
          </Form.Item>
        </Form>
        <Flex>
          <Button className='w-full mr-[5px]' shape='round' onClick={cancel}>{t('取消')}</Button>
          <Button className='w-full ml-[5px]' shape='round' type='primary' loading={submitIng} onClick={cancelCard}>{t('取消卡片')}</Button>
        </Flex>
      </Modal>

      {/* 暂停卡片指示确认 */}
      <Modal
        maskClosable={false}
        keyboard={false}
        title={t("暂停卡片指示确认")}
        open={pauseShow}
        centered
        width={400} 
        onCancel={cancel}
        destroyOnClose={true}
        footer={null}
      >

        <Flex justify='center' className=' relative'>
          <div className=' absolute left-[50px] bottom-[30px] text-[#fff]'>
            <div className='text-[20px] font-bold'>{activeData?.embossedName?.toLocaleUpperCase()}</div>
            <div>****  *****  **** {(activeData?.last4 || '').substr(-8)}</div>
          </div>
          <img src={activeData.creditCardImage || '/card.png'} className='w-[300px]' alt="" />
        </Flex>
        <Flex className='mt-[20px]'>
          <Button className='w-full mr-[5px]' shape='round' onClick={cancel}>{t('取消')}</Button>
          <Button className='w-full ml-[5px]' shape='round' type='primary' loading={submitIng} onClick={pause}>{t('下达指示')}</Button>
        </Flex>
      </Modal>

      {/* 激活卡片 */}
      <Modal
        maskClosable={false}
        keyboard={false}
        title={t("激活卡片指示确认")}
        open={activeCardShow}
        centered
        width={400} 
        onCancel={cancel}
        destroyOnClose={true}
        footer={null}
      >

        <Flex justify='center' className=' relative'>
          <div className=' absolute left-[50px] bottom-[30px] text-[#fff]'>
            <div className='text-[20px] font-bold'>{activeData?.embossedName?.toLocaleUpperCase()}</div>
            <div>****  *****  **** {(activeData?.last4 || '').substr(-8)}</div>
          </div>
          <img src={activeData.creditCardImage || '/card.png'} className='w-[300px]' alt="" />
        </Flex>
        <Flex className='mt-[20px]'>
          <Button className='w-full mr-[5px]' shape='round' onClick={cancel}>{t('取消')}</Button>
          <Button className='w-full ml-[5px]' shape='round' type='primary' loading={submitIng} onClick={activeCard}>{t('下达指示')}</Button>
        </Flex>
      </Modal>
      {/* 取消暂停 */}
      <Modal
        maskClosable={false}
        keyboard={false}
        title={t("取消暂停卡片指示确认")}
        open={unpauseShow}
        centered
        width={400} 
        onCancel={cancel}
        destroyOnClose={true}
        footer={null}
      >

        <Flex justify='center' className=' relative'>
          <div className=' absolute left-[50px] bottom-[30px] text-[#fff]'>
            <div className='text-[20px] font-bold'>{activeData?.embossedName?.toLocaleUpperCase()}</div>
            <div>****  *****  **** {(activeData?.last4 || '').substr(-8)}</div>
          </div>
          <img src={activeData.creditCardImage || '/card.png'} className='w-[300px]' alt="" />
        </Flex>
        <Flex className='mt-[20px]'>
          <Button className='w-full mr-[5px]' shape='round' onClick={cancel}>{t('取消')}</Button>
          <Button className='w-full ml-[5px]' shape='round' type='primary' loading={submitIng} onClick={cardUNSuspend}>{t('下达指示')}</Button>
        </Flex>
      </Modal>

      {/* 解锁 */}
      <Modal
        maskClosable={false}
        keyboard={false}
        title={t("取消锁定卡片指示确认")}
        open={unlockingShow}
        centered
        width={400} 
        onCancel={cancel}
        destroyOnClose={true}
        footer={null}
      >

        <Flex justify='center' className=' relative'>
          <div className=' absolute left-[50px] bottom-[30px] text-[#fff]'>
            <div className='text-[20px] font-bold'>{activeData?.embossedName?.toLocaleUpperCase()}</div>
            <div>****  *****  **** {(activeData?.last4 || '').substr(-8)}</div>
          </div>
          <img src={activeData.creditCardImage || '/card.png'} className='w-[300px]' alt="" />
        </Flex>
        <Flex className='mt-[20px]'>
          <Button className='w-full mr-[5px]' shape='round' onClick={cancel}>{t('取消')}</Button>
          <Button className='w-full ml-[5px]' shape='round' type='primary' loading={submitIng} onClick={unlocking}>{t('下达指示')}</Button>
        </Flex>
      </Modal>
      {/* 卡账户转账 */}
      <Modal
        maskClosable={false}
        keyboard={false}
        title={t("账户转账")}
        open={TXAccountShow}
        centered
        width={400} 
        onCancel={cancel}
        destroyOnClose={true}
        footer={null}
      >
        <Form
          ref={topupRef}
          layout="vertical"
        >
          <Form.Item
            label={<span className='text-[12px]'>{t('目标账户')}</span>}
            name="destinationAccountId"
            rules={[
              { required: true, message: t('目标账户') },
            ]}
          >
            <Input className='w-full' />
          </Form.Item>

          <Form.Item
            label={<span className='text-[12px]'>{t('金额')}</span>}
            name="amount"
            rules={[
              { required: true, message: t('金额') },
            ]}
          >
            <Input className='w-full' />
          </Form.Item>

        </Form>
        <Flex>
          <Button className='w-full mr-[5px]' shape='round' onClick={cancel}>{t('取消')}</Button>
          <Button className='w-full ml-[5px]' shape='round' type='primary' loading={submitIng} onClick={TXCardAccount}>{t('转账')}</Button>
        </Flex>
      </Modal>

      {/* 提取卡账户余额 */}
      <Modal
        maskClosable={false}
        keyboard={false}
        title={t("提取卡账户余额")}
        open={TXAccountShow2}
        centered
        width={400} 
        onCancel={cancel}
        destroyOnClose={true}
        footer={null}
      >
        <Form
          ref={topupRef2}
          layout="vertical"
        >
          <Form.Item
            label={<span className='text-[12px]'>{t('目标账户')}</span>}
            name="accountId"
            rules={[
              { required: true, message: t('目标账户') },
            ]}
          >
            <Input className='w-full' />
          </Form.Item>

          <Form.Item
            label={<span className='text-[12px]'>{t('金额')}</span>}
            name="amount"
            rules={[
              { required: true, message: t('金额') },
            ]}
          >
            <Input className='w-full' />
          </Form.Item>

        </Form>
        <Flex>
          <Button className='w-full mr-[5px]' shape='round' onClick={cancel}>{t('取消')}</Button>
          <Button className='w-full ml-[5px]' shape='round' type='primary' loading={submitIng} onClick={TXCardAccount2}>{t('转账')}</Button>
        </Flex>
      </Modal>

      {/* Client Identity 弹窗 */}
      <Modal
        title={t("Client Identity")}
        open={clientIdentityModalOpen}
        onCancel={handleCloseClientIdentityModal}
        footer={[
          <Button key="close" onClick={handleCloseClientIdentityModal}>
            {t("关闭")}
          </Button>
        ]}
        width={800}
        centered
        destroyOnClose
      >
        {clientIdentityData && (
          <div style={{ maxHeight: '60vh', overflowY: 'auto' }}>
            {/* 个人信息 */}
            <div style={{ marginBottom: '24px' }}>
              <h3 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px' }}>{t("个人信息")}</h3>
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '12px' }}>
                <div>
                  <span style={{ fontWeight: 'bold' }}>{t("名")}:</span>
                  <span style={{ marginLeft: '8px' }}>{clientIdentityData.firstName || '-'}</span>
                </div>
                <div>
                  <span style={{ fontWeight: 'bold' }}>{t("姓")}:</span>
                  <span style={{ marginLeft: '8px' }}>{clientIdentityData.lastName || '-'}</span>
                </div>
                <div>
                  <span style={{ fontWeight: 'bold' }}>{t("姓(Local)")}:</span>
                  <span style={{ marginLeft: '8px' }}>{clientIdentityData.lastNameLocal || '-'}</span>
                </div>
                <div>
                  <span style={{ fontWeight: 'bold' }}>{t("出生日期")}:</span>
                  <span style={{ marginLeft: '8px' }}>{clientIdentityData.dateOfBirth || '-'}</span>
                </div>
                <div>
                  <span style={{ fontWeight: 'bold' }}>{t("邮箱")}:</span>
                  <span style={{ marginLeft: '8px' }}>{clientIdentityData.email || '-'}</span>
                </div>
                <div>
                  <span style={{ fontWeight: 'bold' }}>{t("手机号")}:</span>
                  <span style={{ marginLeft: '8px' }}>{clientIdentityData.mobile || '-'}</span>
                </div>
                <div>
                  <span style={{ fontWeight: 'bold' }}>{t("年收入")}:</span>
                  <span style={{ marginLeft: '8px' }}>{clientIdentityData.annualIncome || '-'}</span>
                </div>
                <div>
                  <span style={{ fontWeight: 'bold' }}>{t("职业")}:</span>
                  <span style={{ marginLeft: '8px' }}>{clientIdentityData.occupation || '-'}</span>
                </div>
                <div>
                  <span style={{ fontWeight: 'bold' }}>{t("职位")}:</span>
                  <span style={{ marginLeft: '8px' }}>{clientIdentityData.position || '-'}</span>
                </div>
                <div>
                  <span style={{ fontWeight: 'bold' }}>{t("状态")}:</span>
                  <span style={{ marginLeft: '8px' }}>{clientIdentityData.status || '-'}</span>
                </div>
                <div style={{ gridColumn: 'span 2' }}>
                  <span style={{ fontWeight: 'bold' }}>{t("地址")}:</span>
                  <span style={{ marginLeft: '8px' }}>{clientIdentityData.address || '-'}</span>
                </div>
              </div>
            </div>

            {/* 证件信息 */}
            {clientIdentityData.document && (
              <div>
                <h3 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '12px' }}>{t("证件信息")}</h3>
                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '12px' }}>
                  <div>
                    <span style={{ fontWeight: 'bold' }}>{t("证件类型")}:</span>
                    <span style={{ marginLeft: '8px' }}>{clientIdentityData.document.type || '-'}</span>
                  </div>
                  <div>
                    <span style={{ fontWeight: 'bold' }}>{t("证件号码")}:</span>
                    <span style={{ marginLeft: '8px' }}>{clientIdentityData.document.number || '-'}</span>
                  </div>
                  <div>
                    <span style={{ fontWeight: 'bold' }}>{t("国家")}:</span>
                    <span style={{ marginLeft: '8px' }}>{clientIdentityData.document.country || '-'}</span>
                  </div>
                  <div>
                    <span style={{ fontWeight: 'bold' }}>{t("证件有效期")}:</span>
                    <span style={{ marginLeft: '8px' }}>{clientIdentityData.document.expiryDate || '-'}</span>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </Modal>
    </div >
  );
};
