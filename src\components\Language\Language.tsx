import { Select } from 'antd';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '@/redux/store';
import { setLanguageSlice } from '@/redux/language/index.ts';
import { useTranslation } from 'react-i18next';
import { localesList } from '@/i18n'
import {
    GlobalOutlined
} from '@ant-design/icons';

export const Languange = (props: any) => {
    const dispatch = useDispatch();
    const { t, i18n } = useTranslation();
    const { language } = useSelector((state: RootState) => state.language);
    const localesChange = (language: string) => {
        i18n.changeLanguage(language);
        dispatch(setLanguageSlice(language))
        console.log(props);
    }

    return <Select
        defaultValue={language}
        className='w-[130px]'
        onChange={localesChange}
        labelRender={(v) => {
            return <div className='flex items-center'><GlobalOutlined className='mr-[10px] text-[18px]' />{v.label}</div>
        }}
        options={localesList.map((v) => {
            return {
                value: v.code,
                label: t(v.label)
            }
        })
        }
    />
};
