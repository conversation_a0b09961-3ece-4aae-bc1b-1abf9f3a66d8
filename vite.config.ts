import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  base: '/admin/',
  resolve: {
    alias: {
      //@ts-ignore
      '@': path.resolve(__dirname, './src'),
      //@ts-ignore
      '@components': path.resolve(__dirname, './src/components'),
      //@ts-ignore
      '@assets': path.resolve(__dirname, './src/assets'),
    },
  },
  server: {
    proxy: {
      // uat
      // '/trade/api': {
      //   target: 'https://sandbox.uatbeupay.net',
      //   changeOrigin: true,
      //   // rewrite: (path) => path.replace(/^\/trade\/api/, '/trade/api'),
      // },
      // // test
      // '/trade/api': {
      //   target: 'http://192.168.3.47',
      //   changeOrigin: true,
      //   // rewrite: (path) => path.replace(/^\/trade\/api/, '/trade/api'),
      // },
      // dev
      '/trade/api': {
        target: 'http://127.0.0.1:8132',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/trade\/api/, ''),
      },
    },
  },
})
