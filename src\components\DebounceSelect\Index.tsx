import React, { useMemo, useState } from 'react';
import { Select, Spin } from 'antd';
import type { SelectProps } from 'antd/es/select';
import debounce from 'lodash/debounce';

export interface DebounceSelectProps<ValueType = any>
    extends Omit<SelectProps<ValueType>, 'options' | 'children'> {
    fetchOptions: (search: string) => Promise<ValueType[]>;
    defaultList?: ValueType[];
}

export function DebounceSelect<
    ValueType extends { key?: string; label: string; value: string | number }
>({
    fetchOptions,
    defaultList = [],
    ...props
}: DebounceSelectProps<ValueType>) {
    const [fetching, setFetching] = useState(false);
    const [options, setOptions] = useState<ValueType[]>(defaultList);

    // 当 defaultList 变化时更新 options
    useMemo(() => {
        setOptions(defaultList);
    }, [defaultList]);

    const debounceFetcher = useMemo(() => {
        const loadOptions = (value: string) => {
            setFetching(true);
            fetchOptions(value).then((newOptions) => {
                setOptions(newOptions);
                setFetching(false);
            });
        };
        return debounce(loadOptions, 800);
    }, [fetchOptions]);

    return (
        <Select
            labelInValue
            filterOption={false}
            onSearch={debounceFetcher}
            notFoundContent={fetching ? <Spin size="small" /> : null}
            {...props}
            options={options}
        />
    );
}