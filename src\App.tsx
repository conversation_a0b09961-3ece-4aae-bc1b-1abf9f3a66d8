import { Router<PERSON>rovider } from 'react-router-dom';
import { ConfigProvider, theme as antdTheme, App as AntdApp } from 'antd';

import { He<PERSON><PERSON>Provider } from 'react-helmet-async';
import { StylesContext } from './context';
import routes from './routes/routes.tsx';
import { useSelector } from 'react-redux';
import { RootState } from './redux/store';
import { useTranslation } from 'react-i18next';
import { LanguageObj } from '@/i18n'
import './assets/css/common.scss'
import { useEffect } from 'react';
import { localesList } from '@/i18n'

// color palettes: triadic #A1A7CB, #CBA1A7, #A7CBA1
// 10 color objects of primary #2378c3 as generated by https://smart-swatch.netlify.app/#2378c3
// This is for reference purposes

export const COLOR = {
  50: '#e0f1ff',
  100: '#b0d2ff',
  200: '#7fb0ff',
  300: '#4d8bff',
  400: '#1e79fe',
  500: '#076ee5',
  600: '#0062b3',
  700: '#004f81',
  800: '#003650',
  900: '#001620',
  borderColor: '#E7EAF3B2',
};
let isInit = true
function App() {
  const { mytheme } = useSelector((state: RootState) => state.theme);
  let { language } = useSelector((state: RootState) => state.language);
  const { i18n } = useTranslation()
  useEffect(() => {
    if (isInit) {
      isInit = false
      // i18n.changeLanguage(language);

      let has = false;
      for (let i = 0; i < localesList.length; i++) {
        if (language === localesList[i].code) {
          has = true;
          break;
        }
      }
      // 语言不存在，使用默认
      if (!has) {
        console.log("语言不存在，使用默认", language);
        language = localesList[0].code;
        i18n.changeLanguage(language);
        console.log("set defaule:", language);
      }
    }
    return () => {
      isInit = false
    }
  }, [])

  return (
    <HelmetProvider>
      <ConfigProvider
        locale={LanguageObj[language]?.antdLanguage}
        theme={{
          token: {
            colorPrimary: COLOR['500'],
            borderRadius: 6,
            fontFamily: 'Lato, sans-serif',
          },
          components: {
            Breadcrumb: {
              // linkColor: 'rgba(0,0,0,.8)',
              // itemColor: 'rgba(0,0,0,.8)',
            },
            Button: {

              colorLink: COLOR['500'],
              colorLinkActive: COLOR['700'],
              colorLinkHover: COLOR['300'],
            },
            Calendar: {
              colorBgContainer: 'none',
            },
            Card: {
              colorBorderSecondary: COLOR['borderColor'],
            },
            Carousel: {
              colorBgContainer: COLOR['800'],
              dotWidth: 8,
            },
            Rate: {
              colorFillContent: COLOR['100'],
              colorText: COLOR['600'],
            },
            Segmented: {
              colorBgLayout: COLOR['100'],
              borderRadius: 6,
              colorTextLabel: '#000000',
            },
            Table: {
              // borderColor: COLOR['100'],
              colorBgContainer: 'none',
              headerBg: 'rgba(0, 0, 0, 0.06)',
              borderColor: 'rgba(0, 0, 0, 0.06)',
              rowHoverBg: COLOR['50'],
            },
            Tabs: {
              colorBorderSecondary: COLOR['100'],
            },
            Timeline: {
              dotBg: 'none',
            },
            Typography: {
              colorLink: COLOR['500'],
              colorLinkActive: COLOR['700'],
              colorLinkHover: COLOR['300'],
              linkHoverDecoration: 'underline',
            },
          },
          algorithm:
            mytheme === 'dark'
              ? antdTheme.darkAlgorithm
              : antdTheme.defaultAlgorithm,
        }}
      >
        <AntdApp>
          <StylesContext.Provider
            value={{
              rowProps: {
                gutter: [
                  { xs: 8, sm: 16, md: 24, lg: 32 },
                  { xs: 8, sm: 16, md: 24, lg: 32 },
                ],
              },
              carouselProps: {
                autoplay: true,
                dots: true,
                dotPosition: 'bottom',
                infinite: true,
                slidesToShow: 3,
                slidesToScroll: 1,
              },
            }}
          >
            <RouterProvider router={routes} />
          </StylesContext.Provider>
        </AntdApp>
      </ConfigProvider>
    </HelmetProvider>
  );
}

export default App;
