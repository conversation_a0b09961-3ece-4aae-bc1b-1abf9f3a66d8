import { HomeOutlined, PieChartOutlined } from '@ant-design/icons';

import { PageHeader } from './PageHeader.tsx';

const meta = {
  title: 'Components/Page header',
  component: PageHeader,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  decorators: [],
}
export default meta;


export const Simple: any = {
  args: {
    title: 'Dashboard',
    breadcrumbs: [
      {
        title: (
          <>
            <HomeOutlined />
            <span>home</span>
          </>
        ),
        path: '/',
      },
      {
        title: 'default dashboard',
      },
    ],
    style: { width: 800 },
  },
};

export const Complex: any = {
  args: {
    title: 'Dashboard',
    breadcrumbs: [
      {
        title: (
          <>
            <HomeOutlined />
            <span>home</span>
          </>
        ),
        path: '/',
      },
      {
        title: (
          <>
            <PieChartOutlined />
            <span>dashboards</span>
          </>
        ),
        menu: {

        },
      },
      {
        title: 'default',
      },
    ],
    style: { width: 800 },
  },
};
