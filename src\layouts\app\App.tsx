import {
  But<PERSON>,
  Dropdown,
  Flex,
  F<PERSON><PERSON><PERSON>on,
  Layout,
  MenuProps,
  message,
  theme,
  Tooltip,
  Spin
} from 'antd';
import { useLocation, useNavigate } from 'react-router-dom';
import { ReactNode, useEffect, useRef, useState } from 'react';
import {
  LogoutOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  SettingOutlined,
  UserOutlined,
  MehOutlined,
} from '@ant-design/icons';
import { TransitionGroup, } from 'react-transition-group';
import { useMediaQuery } from 'react-responsive';
import SideNav from './SideNav.tsx';
import HeaderNav from './HeaderNav.tsx';
import { NProgress } from '@/components';
import { Languange } from '@/components/Language/Language.tsx'
import { useSelector, useDispatch } from 'react-redux';
import { setuserSlice } from '@/redux/user';
// import { asuncUserInfoWithToken } from '@/redux/user'
import { useTranslation } from 'react-i18next';
import { logout } from '@/api/login.ts'

const { Content } = Layout;

type AppLayoutProps = {
  children: ReactNode;
};

export const AppLayout = ({ children }: AppLayoutProps) => {
  const { token: { borderRadius }, } = theme.useToken();
  const isMobile = useMediaQuery({ maxWidth: 769 });
  const [collapsed, setCollapsed] = useState(true);
  const [navFill, setNavFill] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const location = useLocation();
  const { t } = useTranslation()
  // const nodeRef = useRef(null);
  const floatBtnRef = useRef(null);
  const queryUseRef = useRef(true)
  const dispatch = useDispatch()
  const navigate = useNavigate();
  const userStore = useSelector((state: any) => { return state.user });


  const logoutFn = async () => {
    const res: any = await logout()
    if (res.code == 200) {
      message.open({ type: 'loading', content: t('退出登录') });
      dispatch(setuserSlice(undefined))
      setTimeout(() => {
        navigate('/');
      }, 300);
      return
    }
    message.open({ type: 'warning', content: t(res.message) });
  }

  const items: MenuProps['items'] = [
    {
      key: 'user-profile-link',
      label: 'profile',
      icon: <UserOutlined />,
      onClick: () => {
        navigate('/profile/info');
      },
    },
    {
      key: 'user-settings-link',
      label: 'settings',
      icon: <SettingOutlined />,
      onClick: () => {
        navigate('/profile/security');
      },
    },
    {
      type: 'divider',
    },
    {
      key: 'user-logout-link',
      label: 'logout',
      icon: <LogoutOutlined />,
      danger: true,
      onClick: logoutFn
    },
  ];



  useEffect(() => {
    setCollapsed(isMobile);
    setIsLoading(false)
  }, [isMobile]);



  useEffect(() => {
    if (userStore.user && queryUseRef.current) {
      queryUseRef.current = false
    }
  }, [userStore.user])

  useEffect(() => {
    if (userStore.UserInfoWithTokenser) queryUseRef.current = true
  }, [userStore.UserInfoWithTokenser])

  useEffect(() => {
    window.addEventListener('scroll', () => {
      if (window.scrollY > 5) {
        setNavFill(true);
      } else {
        setNavFill(false);
      }
    });
  }, []);

  return (
    <>
      <NProgress isAnimating={isLoading} key={location.key} />
      <Layout
        style={{
          minHeight: '100vh',
        }}
      >
        <SideNav
          trigger={null}
          collapsible
          collapsed={collapsed}
          onCollapse={(value) => setCollapsed(value)}
          style={{
            overflow: 'auto',
            position: 'fixed',
            left: 0,
            top: 0,
            bottom: 0,
            background: 'none',
            border: 'none',
            transition: 'all .2s',
          }}
        />
        <Layout style={{ marginLeft: collapsed ? 0 : '60px' }}>
          <HeaderNav
            style={{
              marginLeft: collapsed ? 0 : '233px',
              padding: '0 2rem 0 0',
              background: navFill ? 'rgba(255, 255, 255, .5)' : '#f5f5f5',
              backdropFilter: navFill ? 'blur(8px)' : 'none',
              boxShadow: navFill ? '0 0 8px 2px rgba(0, 0, 0, 0.05)' : 'none',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              position: 'sticky',
              top: 0,
              zIndex: 1,
              gap: 8,
              transition: 'all .25s',
            }}
          >
            <Flex align="center">
              <Tooltip title={`${collapsed ? 'Expand' : 'Collapse'} Sidebar`}>
                <Button
                  type="text"
                  icon={
                    collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />
                  }
                  onClick={() => setCollapsed(!collapsed)}
                  style={{
                    fontSize: '16px',
                    width: 64,
                    height: 64,
                  }}
                />
              </Tooltip>
              {/* <Input.Search
                placeholder="search"
                style={{
                  width: isMobile ? '100%' : '400px',
                  marginLeft: isMobile ? 0 : '.5rem',
                }}
                size="middle"
              /> */}
            </Flex>
            <Flex align="center" gap="small">
              {/* <Tooltip title="Apps">
                <Button icon={<AppstoreOutlined />} type="text" size="large" />
              </Tooltip> */}
              <Languange />
              {/* <Tooltip title="Messages">
                <Button icon={<MessageOutlined />} type="text" size="large" />
              </Tooltip> */}
              {/* <Tooltip title="Theme">
                <Switch
                  className=" hidden sm:inline py-1"
                  checkedChildren={<MoonOutlined />}
                  unCheckedChildren={<SunOutlined />}
                  checked={mytheme === 'light' ? true : false}
                  onClick={() => dispatch(toggleTheme())}
                />
              </Tooltip> */}
              <Dropdown menu={{ items }} trigger={['hover']}>
                <Flex className=' cursor-pointer ml-[30px]'>
                  <MehOutlined className='mr-[5px]' />
                  {userStore?.user?.user?.userName || ''}
                </Flex>
              </Dropdown>
            </Flex>
          </HeaderNav>
          <Content
            style={{
              margin: `0 0 0 ${collapsed ? 0 : '200px'}`,
              // background: '#ebedf0',
              borderRadius: collapsed ? 0 : borderRadius,
              transition: 'all .25s',
              padding: '24px 32px',
              minHeight: 360,
            }}
          >
            <Spin spinning={isLoading}>
              <TransitionGroup>
                {/* <SwitchTransition>
                  <CSSTransition
                    key={`css-transition-${location.key}`}
                    nodeRef={nodeRef}
                    onEnter={() => {
                      setIsLoading(true);
                    }}
                    onEntered={() => {
                      setIsLoading(false);
                    }}
                    timeout={300}
                    classNames="bottom-to-top"
                    unmountOnExit
                  > */}
                {children}
                {/* {() => (
                      <div ref={nodeRef} style={{ background: 'none' }}>
                        {children}
                      </div>
                    )} */}
                {/* </CSSTransition>
                </SwitchTransition> */}
              </TransitionGroup>
              <div ref={floatBtnRef}>
                <FloatButton.BackTop />
              </div>
            </Spin>
          </Content>
        </Layout>
      </Layout>
    </>
  );
};
