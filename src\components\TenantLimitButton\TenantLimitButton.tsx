import { Button } from 'antd';
import { useState } from 'react';
import TenantLimitModal from '../TenantLimitModal/TenantLimitModal';

interface TenantLimitButtonProps {
  row: {
    appid: string;
    companyName: string; // 作为remark传入
  };
}

const TenantLimitButton = ({ row }: TenantLimitButtonProps) => {
  const [visible, setVisible] = useState(false);

  return (
    <>
      <Button type="link" onClick={() => setVisible(true)}>
        设置USAFE限额
      </Button>
      <TenantLimitModal
        visible={visible}
        onCancel={() => setVisible(false)}
        appId={row.appid}
        remark={row.companyName}
      />
    </>
  );
};

export default TenantLimitButton;
