import request from '../request.ts'
 


//获取库存列表
export function getInventoryList(params: any) {
    return request({
        url: "/admin/creditCard/repo/listpage",
        method: "get",
        params,
    });
}


//导入库存列表
export function postImportList(data: any) {
    return request({
        url: "/admin/creditCard/repo/import",
        method: "post",
        data,
    });
}

//获取卡CVC 信息
export function postQueryCardCVC(data: any) {
    return request({
        url: "/admin/creditCard/repo/query/cvc",
        method: "post",
        data,
    });
}

//删除库存卡
export function postDelInventorCard(data: any) {
    return request({
        url: "/admin/creditCard/repo/remove",
        method: "post",
        data,
    });
}

// 设置为预留
export function reservedCardSuffix(data: any) {
    return request({
        url: "/admin/creditCard/repo/reserved",
        method: "post",
        data,
    });
}

export default {
    getInventoryList,
    postImportList,
    postQueryCardCVC,
    postDelInventorCard,
    reservedCardSuffix
}