import {
  Card,
  Flex,
  Table,
  Input,
  Select,
  message,
  Space,
  Button,
  DatePicker,
} from "antd";
import dayjs from "dayjs"; // 顶部添加导入
import { PageHeader } from "@/components";
import { useTranslation } from "react-i18next";
import { useEffect, useRef, useState } from "react";
import { useNavigate } from "react-router-dom";
import cardAccountTransactionLogApi, {
  reSettlement,
} from "@/api/cardAccountTransactionLog";
import { OrganizationSelect } from "@/components/OrganizationSelect";
import type { AxiosResponse } from "axios";
import { ApiResponse } from "@/types/organization";

interface SearchParams {}

export const CardAccountTransactionLogPage = () => {
  const { t } = useTranslation();
  const [current, setCurrent] = useState<number>(1);
  const [total, setTotal] = useState<number>(0);
  const loadingRef = useRef(true);
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const navigate = useNavigate();
  const formRef: any = useRef();
  const [searchParams, setSearchParams] = useState<SearchParams>({
    // needSync: 1, // 设置默认值
  });

  useEffect(() => {
    if (loadingRef.current) {
      loadingRef.current = false;
      query();
      qyeryDefaultList();
    }
  }, [current]);

  const pageChange = (page: number) => {
    setCurrent(page);
  };

  const query = () => {
    if (!loading) {
      setLoading(true);
      cardAccountTransactionLogApi
        .waitListpage({
          pageNum: current,
          pageSize: 10,
          ...searchParams,
        })
        .then((response) => {
          const res:any = response ;
          if (res.code === 200) {
            setTotal(res.data?.total);
            setData(res.data?.list || []);
          }
        })
        .catch((err) => {
          console.error(err);
        })
        .finally(() => {
          setLoading(false);
          loadingRef.current = true;
        });
    }
  };

  const qyeryDefaultList = async () => {};

  // 发起重新结算
  const reSettlement = (ids: React.Key[] = selectedRowKeys) => {
    if (ids.length === 0) {
      message.warning(t('请选择至少一条记录'));
      return;
    }
    const params = {
      historyLogIds: ids,
    };
    setLoading(true);
    cardAccountTransactionLogApi
      .reSettlement(params)
      .then((response) => {
        const res:any = response ;
        if (res.code === 200) {
          message.open({
            type: "success",
            content: "处理成功",
          });
        }
      })
      .catch((err) => {
        console.error(err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  return (
    <div className="relative">
      <PageHeader title={t("未结算交易处理")} breadcrumbs={[]} />

      <Card className="mt-[20px]">
        <Flex wrap="wrap" gap="small" className="mb-[20px]">
          <Flex className="ml-auto gap-[10px]">
            <Button type="primary" onClick={() => query()}>
              {t("搜索")}
            </Button>
            <Button 
              type="primary" 
              onClick={() => reSettlement()}
              disabled={selectedRowKeys.length === 0}
            >
              {t("批量重新结算")}
            </Button>
          </Flex>
        </Flex>
        <Table
          loading={loading}
          rowKey={(row: any) => row.id}
          rowSelection={{
            selectedRowKeys,
            onChange: (newSelectedRowKeys: React.Key[]) => {
              setSelectedRowKeys(newSelectedRowKeys);
            },
          }}
          className="mt-[20px]"
          columns={[
            {
              title: t("ID"),
              dataIndex: "id",
              key: "id",
            },

            {
              title: t("账户ID"),
              dataIndex: "accCardAccountId",
              key: "accCardAccountId",
              render: (text) => <span>{text}</span>,
            },

            {
              title: t("卡ID"),
              dataIndex: "accCardId",
              key: "accCardId",
              render: (text) => <span>{text}</span>,
            },

            {
              title: t("货币"),
              dataIndex: "currency",
              key: "currency",
              render: (text) => <span>{text}</span>,
            },

            {
              title: t("handleNum"),
              dataIndex: "handleNum",
              key: "handleNum",
            },
            {
              title: t("status"),
              dataIndex: "status",
              key: "status",
            },
            {
              title: t("handle"),
              dataIndex: "handle",
              key: "handle",
            },
            {
              title: t("createdAt"),
              dataIndex: "createdAt",
              key: "createdAt",
              render: (text: number) => (
                <span>
                  {text ? dayjs(text).format("YYYY-MM-DD HH:mm:ss") : "-"}
                </span>
              ),
            },
            {
              title: t("hookEvent"),
              dataIndex: "hookEvent",
              key: "hookEvent",
            },

            {
              title: t("操作"),
              key: "action",
              render: (_, record) => (
                <Space size="middle">
                  <Button
                    type="text"
                    className="_primary"
                    onClick={reSettlement.bind(this, [record.id])}
                  >
                    {t("重新结算")}{" "}
                  </Button>
                </Space>
              ),
            },
          ]}
          dataSource={data}
          pagination={{
            hideOnSinglePage: true,
            showSizeChanger: false,
            pageSize: 10,
            current,
            total,
            onChange: pageChange,
            position: ["bottomRight"],
          }}
        />
      </Card>
    </div>
  );
};
