import {
  Input,
  Button,
  Modal,
  Flex,
  Tag,
  Divider,
  Pagination,
  Form,
  Select,
  DatePicker,
  Card,
  message,
  Upload,
} from "antd";
import { PageHeader } from "../../components";
import { useState, useRef, useEffect } from "react";
import CountUp from "react-countup";
import { DebounceSelect } from "@/components/DebounceSelect/Index";
import countryCodeList from "@/assets/json/country.json";
import {
  UserOutlined,
  SearchOutlined,
  TagsOutlined,
  RightOutlined,
  LoadingOutlined,
  PlusOutlined,
} from "@ant-design/icons";

import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import {
  getCardAccountList,
  postCreateCardAccount,
  getCardConfigList,
} from "@/api/card";
import { getOrganizList } from "@/api/sys";
import { postCountries, postOccupations } from "@/api/card";
import { OrganizationSelect } from "@/components/OrganizationSelect";
// import { getAccountList } from '@/api/account'

const { RangePicker } = DatePicker;

const beforeUpload = (file: any) => {
  console.log(file);
  return false;
  // const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
  // if (!isJpgOrPng) {
  //   message.error('You can only upload JPG/PNG file!');
  // }
  // const isLt2M = file.size / 1024 / 1024 < 2;
  // if (!isLt2M) {
  //   message.error('Image must smaller than 2MB!');
  // }
  // return isJpgOrPng && isLt2M;
};

interface SearchParams {
  appid?: string;
  name?: string;
  id?: string;
  accountNumber?: string;
}

export const CardAccountPage = () => {
  const navigate = useNavigate();

  const { t } = useTranslation();
  const [current, setCurrent] = useState<number>(1);
  const [total, setTotal] = useState<number>(0);
  const [downloadShow, setDownloadShow] = useState(false);
  const [createShow, setCreateShow] = useState(false);
  const formRef: any = useRef();
  const loadingRef = useRef(true);
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState([]);
  // const [configData, setConfigData] = useState([])
  const [tlementAccountValue, setTlementAccountValue] = useState<any>();
  const [defaultList, setDefaultList] = useState([]);
  const [front, setFront] = useState();
  const [back, setBack] = useState();
  const [submitLoading, setSubmitLoading]: any = useState(false);
  const [countryList, setCountryList] = useState<any>([]);
  const [occupationList, setOccupationList] = useState<any>([]);
  const [searchParams, setSearchParams] = useState<SearchParams>({});

  useEffect(() => {
    if (loadingRef.current) {
      loadingRef.current = false;
      query();
    }
  }, [current]);

  useEffect(() => {
    qyeryDefaultList();
  }, []);

  const pageChange = (page: number) => {
    setCurrent(page);
  };
  const getBase64 = (img: any, callback: (url: string) => void) => {
    const reader = new FileReader();
    reader.addEventListener("load", () => callback(reader.result as string));
    reader.readAsDataURL(img);
  };

  const handleChange: any = (info: any) => {
    getBase64(info.file, (url: any) => {
      setFront(url);
    });
  };
  const handleChangeBack: any = (info: any) => {
    getBase64(info.file, (url: any) => {
      setBack(url);
    });
  };

  const uploadButton = (
    <button style={{ border: 0, background: "none" }} type="button">
      {loading ? <LoadingOutlined /> : <PlusOutlined />}
      <div style={{ marginTop: 8 }}>Upload</div>
    </button>
  );

  const query = (value?: any, type?: string) => {
    if (!loading) {
      setLoading(true);
      getCardAccountList({
        pageNum: current,
        pageSize: 10,
        ...searchParams,
      })
        .then((res: any) => {
          if (res.code == 200) {
            setTotal(res.data?.total);
            setData(res.data?.list || []);
          }
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          setLoading(false);
          loadingRef.current = true;
        });
    }
  };

  useEffect(() => {
    if (tlementAccountValue?.value) {
      queryCardConfig();
    }
  }, [tlementAccountValue]);

  const queryCardConfig = () => {
    getCardConfigList({
      pageNum: current,
      pageSize: 10,
      appid: tlementAccountValue?.value,
    })
      .then((res: any) => {
        if (res.code == 200) {
          // setConfigData(res.data || [])
        }
      })
      .catch(console.log);
  };

  const [dateOfBirth, setDateOfBirth] = useState();
  const [expiryDate, setExpiryDate] = useState();
  const onDateChange = (date: any, dateString: any) => {
    console.log(date, dateString);
    setDateOfBirth(dateString);
  };
  const onDateChange2 = (date: any, dateString: any) => {
    console.log(date, dateString);
    setExpiryDate(dateString);
  };
  const add = () => {
    formRef.current?.validateFields().then((values: any) => {
      let api = postCreateCardAccount;
      setSubmitLoading(true);
      api({
        // creditCardTypeId: values.creditCardTypeId,
        currency: values.currency,
        name: `${values?.firstName}${values?.lastName}`,
        countryCode: `${values["num1"]}`,
        mobileNumber: `${values["num2"]}`,
        appId: values["settlementAccount"].value,
        identityType: "individual",
        individual: {
          firstName: values?.firstName,
          lastName: values?.lastName,
          email: values.email,
          // mobile: `${values['num1']}${values['num2']}`,
          countryCode: `${values["num1"]}`,
          mobileNumber: `${values["num2"]}`,
          annualIncome: values.annualIncome,
          occupation: values.occupation, //职业
          position: values.position,
          dateOfBirth, //出生日期
          document: {
            front,
            back,
            type: values.type,
            number: values.cardNumber,
            country: values.country, //国家
            address: values.address, //address
            expiryDate, //有效期
          },
        },
      })
        .then((res: any) => {
          const { code } = res;
          if (code == 200) {
            message.open({
              type: "success",
              content: t("添加成功"),
            });
            close();
            query();
            return;
          }
          message.open({
            type: "error",
            content: t(res.message),
          });
        })
        .finally(() => {
          setSubmitLoading(false);
        });
    });
  };

  const download = () => {
    close();
  };
  const close = () => {
    formRef?.current?.resetFields();
    setDownloadShow(false);
    setCreateShow(false);
    setFront(undefined);
    setBack(undefined);
    setTlementAccountValue(undefined);
  };

  const toDetails = (row: any) => {
    navigate("/acccess/card-account/details", {
      state: {
        id: row.id,
        appid: row.accAppid,
        balance: row.downBalance,
        name: `${row?.firstName || ""}${row?.lastName || ""} ` || "Name",
      },
    });
  };

  async function fetchUserList(username?: string): Promise<any> {
    // console.log('fetching user', username);
    return new Promise((resolve) => {
      getOrganizList({
        pageNum: 1,
        name: username || undefined,
      }).then((res: any) => {
        if (res.code == 200) {
          const list = res.data?.map((v: any) => {
            return {
              value: v?.appid,
              label: `${v.companyName}`,
            };
          });
          resolve(list);
          return;
        }
        resolve([]);
      });
    });
  }

  const qyeryDefaultList = async () => {
    const list = await fetchUserList();
    setDefaultList(list);

    postCountries({}).then((res: any) => {
      if (res.code == 200) {
        // console.log(res);
        setCountryList(
          res.data?.map((v: any) => {
            return {
              value: v.code,
              label: v.name,
            };
          }) || []
        );
      }
    });
    postOccupations({}).then((res: any) => {
      if (res.code == 200) {
        // console.log('postOccupations', res);
        setOccupationList(
          res.data?.map((v: any) => {
            return {
              value: v.name,
              label: v.name,
            };
          }) || []
        );
      }
    });
  };

  return (
    <div className=" relative">
      <PageHeader title={t("卡片账户")} breadcrumbs={[]} />
      <Button
        onClick={() => setDownloadShow(true)}
        shape="round"
        disabled
        style={{
          backgroundImage:
            "linear-gradient(90deg, rgb(243, 227, 180), rgb(227, 179, 84))",
        }}
        className=" absolute right-[20px] top-[10px]"
      >
        {" "}
        {t("下载交易报告")}
      </Button>
      <Card
        className="card-show pt-[20px]"
        title={
          <Flex wrap="wrap" gap="small" className="w-full">
            <OrganizationSelect
              allowClear
              className="w-[200px]"
              placeholder={t("请选择组织")}
              onChange={(value) => {
                setSearchParams((prev) => ({ ...prev, appid: value }));
              }}
            />
            <Input
              allowClear
              className="w-[200px]"
              placeholder={t("请输入名称")}
              onChange={(e) => {
                setSearchParams((prev) => ({ ...prev, name: e.target.value }));
              }}
              onPressEnter={() => query()}
            />
            <Input
              allowClear
              className="w-[200px]"
              placeholder={t("请输入卡账户ID")}
              onChange={(e) => {
                setSearchParams((prev) => ({ ...prev, id: e.target.value }));
              }}
              onPressEnter={() => query()}
            />
            <Input
              allowClear
              className="w-[200px]"
              placeholder={t("请输入卡账户编号")}
              onChange={(e) => {
                setSearchParams((prev) => ({
                  ...prev,
                  accountNumber: e.target.value,
                }));
              }}
              onPressEnter={() => query()}
            />
          </Flex>
        }
        extra={
          <Flex className="ml-auto gap-[10px]">
            <Button type="primary" onClick={() => query()}>
              {t("搜索")}
            </Button>
            <Button
              shape="round"
              onClick={() => setCreateShow(true)}
              type="primary"
            >
              {" "}
              {t("新增卡账户")}
            </Button>
          </Flex>
        }
      >
        {data.map((item: any) => {
          return (
            <Card className="card-show mb-[20px]">
              <Flex justify="space-between" className=" cursor-pointer">
                <Flex align="center">
                  <UserOutlined />
                  <span className="text-[20px] font-bold ml-[10px] mr-[10px]">
                    {`${item?.firstName || ""}${item?.lastName || ""} ` ||
                      "Name"}
                  </span>
                  <span
                    onClick={toDetails.bind(this, item)}
                    className="text-[12px] text-[#dc9b32]"
                  >
                    {t("账户详情")} <RightOutlined className="text-[12px]" /> 
               
                  </span>
                </Flex>
                <div>
                  <Tag color="#87d068">BEU{t("自动转账已启用")}</Tag>
                  <Tag color="#000">USD</Tag>
                  <Tag color="#0ebbbe">{t("预付")}</Tag>
                </div>
              </Flex>
              <Divider orientation="right" className="my-[10px]" />
              <Flex justify="space-between" align="start" wrap>
                <Flex>
                  <TagsOutlined className="text-[20px]" />
                  <div className="ml-[10px]">
                    <div className="text-[#666] text-[14px]">
                      {t("卡片账户余额")}
                    </div>
                    <div className="font-bold text-[#305992] text-[20px]">
                      <CountUp
                        end={item?.downBalance || "0"}
                        duration={0.5}
                        decimals={2}
                        suffix=" USD"
                      />
                    </div>
                    <div><span> 状态: </span> {item?.status==1?'active':'frozen' }</div>
                  </div>
                </Flex>
                <Card className="px-[10px] card-show _card-header-py">
                  <Flex
                    justify="space-between"
                    onClick={toDetails.bind(this, item)}
                  >
                    <Flex justify="space-between" align="center">
                      <div className="w-[280px]">
                        <div>
                          {t("组织ID")}: {item.accAppid}
                        </div>
                        {/* <div>{t('账号号码')}: {item.accAccountNumber}</div> */}
                      </div>
                      <Flex align="center">
                        <div>
                          <span className="text-[14px] text-[#000] font-bold">
                            {t("卡号码")}
                          </span>
                          <span
                            className="bg-[#efefef] inline-block px-[5px] text-[#808080] text-[12px] rounded-[10px] ml-[10px]"
                            style={{ lineHeight: "14px" }}
                          >
                            {item?.accAccountNumber}
                          </span>
                          <div>
                            <span className="text-[12px] text-[#000] font-bold">
                              {t("卡账户ID")}
                            </span>
                            <span
                              className="bg-[#efefef] inline-block px-[5px] text-[#808080] text-[12px] rounded-[10px] ml-[10px]"
                              style={{ lineHeight: "12px" }}
                            >
                              {item?.id}
                            </span>
                          </div>
                        </div>
                      </Flex>
                    </Flex>
                    <Flex justify="space-between" align="center">
                      {/* <Flex align='center'>
                    <span className='text-[#666] text-[12px] mr-[5px]'>{t('今日支出')}</span>
                    <span className='text-[#305992] text-[12px] font-bold'>0  <span>USD</span></span>
                  </Flex> */}
                      <div className="pl-[100px] text-[#dc9b32] text-[12px]">
                        {t("卡片详情")}
                        <RightOutlined className="ml-[5px]" />
                      </div>
                    </Flex>
                  </Flex>
                </Card>
              </Flex>
            </Card>
          );
        })}

        <div className="flex justify-end mt-[20px]">
          <Pagination
            hideOnSinglePage={true}
            pageSize={10}
            current={current}
            showSizeChanger={false}
            total={total}
            onChange={pageChange}
          />
        </div>
      </Card>
      <Modal
        maskClosable={false}
        keyboard={false}
        title={t("下载交易报告")}
        open={downloadShow}
        destroyOnClose={true}
        className="top-[30vh]"
        width={400}
        onCancel={close}
        footer={null}
      >
        <div className="text-[14px] font-bold mb-[10px]">
          {t("您可以选择日期范围或下载所有日期范围")}
        </div>
        <Form layout="vertical">
          <Form.Item
            label={<span className="text-[12px]">{t("账户")}</span>}
            name="coin"
          >
            <Select
              placeholder={t("选择账户")}
              options={[
                { label: "USDT", value: "USDT" },
                { label: "USD", value: "USD" },
              ]}
            />
          </Form.Item>
          <Form.Item
            label={<span className="text-[12px]">{t("日期范围")}</span>}
            name="account"
          >
            <RangePicker className="w-full" />
          </Form.Item>
        </Form>
        <Button
          className="w-full"
          shape="round"
          loading={submitLoading}
          onClick={download}
          type="primary"
        >
          {t("下载")}
        </Button>
      </Modal>
      <Modal
        maskClosable={false}
        keyboard={false}
        title={t("新增卡账户")}
        open={createShow}
        centered
        className=""
        onCancel={close}
        width={800}
        footer={null}
      >
        <div className="text-[14px] font-bold mb-[10px]">
          {t("请填写以下表格以创建新帐户")}
        </div>
        <Form
          ref={formRef}
          autoComplete="off"
          requiredMark={false}
          layout="vertical"
        >
          <Flex>
            <div>
              <Flex>
                <Form.Item
                  label={t("名")}
                  name="firstName"
                  className="w-[50%] mr-[10px]"
                  rules={[{ required: true, message: t("名") }]}
                >
                  <Input />
                </Form.Item>
                <Form.Item
                  name="lastName"
                  label={t("姓氏")}
                  className="w-[50%]"
                  rules={[{ required: true, message: t("姓氏") }]}
                >
                  <Input />
                </Form.Item>
              </Flex>
              <Form.Item className="mb-[0] w-full" label={t("手机号码")}>
                <Flex>
                  <Form.Item
                    name="num1"
                    className="w-[100px] mr-[10px]"
                    rules={[{ required: true, message: t("名称") }]}
                  >
                    <Select
                      className="w-[100px] mr-[10px]"
                      showSearch
                      options={countryCodeList.map((v: any) => {
                        return {
                          value: v.code,
                          label: v.code,
                        };
                      })}
                    />
                    {/* <Input className='w-[60px] mr-[10px]' /> */}
                  </Form.Item>
                  <Form.Item
                    name="num2"
                    className="w-full"
                    rules={[{ required: true, message: t("请输入账户名称") }]}
                  >
                    <Input />
                  </Form.Item>
                </Flex>
              </Form.Item>

              <Form.Item
                label="E-Mail"
                name="email"
                className="w-full"
                rules={[{ required: true, message: t("请输入邮箱") }]}
              >
                <Input />
              </Form.Item>
              <Flex>
                <Form.Item
                  label={t("证件类型")}
                  name="type"
                  className="w-[40%] mr-[10px]"
                  rules={[{ required: true, message: t("证件类型") }]}
                >
                  <Select
                    placeholder={t("证件类型")}
                    options={[
                      {
                        value: "passport",
                        label: t("护照"),
                      },
                      {
                        value: "drivers-license",
                        label: t("驾照"),
                      },
                      {
                        value: "national-id",
                        label: t("国民身份证"),
                      },
                    ]}
                  />
                </Form.Item>

                <Form.Item
                  label={t("证件号码")}
                  name="cardNumber"
                  className="w-full"
                  rules={[{ required: true, message: t("请输入证件号码") }]}
                >
                  <Input />
                </Form.Item>
              </Flex>
              <Flex>
                <Form.Item
                  label={t("出生日期")}
                  name="dateOfBirth"
                  className="w-full"
                  rules={[{ required: true, message: t("出生日期") }]}
                >
                  <DatePicker onChange={onDateChange} />
                </Form.Item>
                <Form.Item
                  label={t("证件有效期")}
                  name="expiryDate"
                  className="w-full"
                  rules={[{ required: true, message: t("证件有效期") }]}
                >
                  <DatePicker onChange={onDateChange2} />
                </Form.Item>
              </Flex>
              <Flex>
                <Form.Item
                  label={t("证件照正面")}
                  name="front"
                  className="w-full"
                  rules={[{ required: true, message: t("证件照正面") }]}
                >
                  <Upload
                    name="front"
                    listType="picture-card"
                    className="avatar-uploader"
                    showUploadList={false}
                    accept="image/png"
                    beforeUpload={beforeUpload}
                    onChange={handleChange}
                  >
                    {front ? (
                      <img
                        src={front}
                        alt="avatar"
                        className="w-full h-auto max-w-[100px] max-h-[100px]"
                      />
                    ) : (
                      uploadButton
                    )}
                  </Upload>
                </Form.Item>

                <Form.Item
                  label={t("证件照反面")}
                  name="back"
                  className="w-full"
                  rules={[{ required: true, message: t("证件照反面") }]}
                >
                  <Upload
                    name="front"
                    accept="image/png"
                    listType="picture-card"
                    className="avatar-uploader"
                    showUploadList={false}
                    beforeUpload={beforeUpload}
                    onChange={handleChangeBack}
                  >
                    {back ? (
                      <img
                        src={back}
                        alt="avatar"
                        className="w-full h-auto max-w-[100px] max-h-[100px]"
                      />
                    ) : (
                      uploadButton
                    )}
                  </Upload>
                </Form.Item>
              </Flex>
            </div>
            <div className="w-[50%] ml-[20px]">
              <Form.Item
                label={t("年收入")}
                name="annualIncome"
                className="w-full"
                rules={[{ required: true, message: t("请输入年收入") }]}
              >
                <Input />
              </Form.Item>
              <Form.Item
                label={t("职业")}
                name="occupation"
                className="w-full"
                rules={[{ required: true, message: t("请输入职业") }]}
              >
                <Select
                  showSearch
                  placeholder={t("职业")}
                  options={occupationList}
                />
              </Form.Item>
              <Form.Item label={t("职位")} className="w-[60%]" name="position">
                <Input />
              </Form.Item>
              <Flex>
                <Form.Item
                  label={t("国家")}
                  name="country"
                  className="w-[30%] mr-[10px]"
                  rules={[{ required: true, message: t("国家") }]}
                >
                  <Select
                    showSearch
                    placeholder={t("国家")}
                    options={countryList}
                  />
                </Form.Item>
                <Form.Item label={t("地址")} className="w-[60%]" name="address">
                  <Input />
                </Form.Item>
              </Flex>
              <Form.Item
                label={t("组织")}
                name="settlementAccount"
                rules={[{ required: true, message: t("组织") }]}
              >
                <DebounceSelect
                  showSearch
                  value={tlementAccountValue}
                  placeholder=""
                  defaultList={defaultList}
                  fetchOptions={fetchUserList}
                  onChange={(newValue) => {
                    setTlementAccountValue(newValue);
                  }}
                  style={{ width: "100%" }}
                />
              </Form.Item>
              {/* {
                tlementAccountValue?.value ?
                  <Form.Item
                    label={t('卡类型')}
                    name="creditCardTypeId"
                    rules={[
                      { required: true, message: t('卡类型') },
                    ]}
                  >
                    <Select
                      placeholder={t('选择卡类型')}
                      options={
                        configData.map((v: any) => {
                          return { label: `${v.creditCardName}-${v.cardMerchantName}`, value: v.id }
                        })
                      } />
                  </Form.Item> : ''
              } */}

              <Form.Item label={t("币种")} initialValue={"USD"} name="currency">
                <Select
                  placeholder={t("币种")}
                  options={[{ label: "USD", value: "USD" }]}
                />
              </Form.Item>
              <Form.Item label={t("地址")} className="w-[60%]" name="address">
                <Input />
              </Form.Item>
            </div>
          </Flex>
        </Form>
        <Flex>
          <Button className="w-full mr-[5px]" shape="round" onClick={close}>
            {t("取消")}
          </Button>
          <Button
            className="w-full ml-[5px]"
            shape="round"
            type="primary"
            loading={submitLoading}
            onClick={add}
          >
            {t("确认")}
          </Button>
        </Flex>
      </Modal>
    </div>
  );
};
