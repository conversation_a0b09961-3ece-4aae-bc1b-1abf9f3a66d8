import request from '../request.ts'


export function getLlist(params: any) {
    return request({
        url: "/admin/card/merchant/utgl/hook/listpage",
        method: "get",
        params,
    });
}

export function postCreate(data: any) {
    return request({
        url: "/admin/card/merchant/utgl/hook/create",
        method: "post",
        data,
    });
}

export function postRemove(data: any) {
    return request({
        url: "/admin/card/merchant/utgl/hook/remove",
        method: "post",
        data,
    });
}

export function postStatus(data: any) {
    return request({
        url: "/admin/card/merchant/utgl/hook/status",
        method: "post",
        data,
    });
}

export function postResume(data: any) {
    return request({
        url: "/admin/card/merchant/utgl/hook/resume",
        method: "post",
        data,
    });
}

export default {
    getLlist,
    postCreate,
    postRemove,
    postStatus,
    postResume
};
