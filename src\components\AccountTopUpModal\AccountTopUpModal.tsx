import { Modal, Form, Input, Button, Flex, message, Select } from 'antd';
import { postCardTopup } from '../../api/card';
import { useTranslation } from 'react-i18next';
import { getAccountList } from '../../api/account';
import { useEffect, useState, useCallback } from 'react';
import { useLocation } from 'react-router-dom';
import { debounce } from 'lodash';

interface AccountItem {
  id: string;
  accountName: string;
  tenantAccountId: string;
  currency: string;
  usd: number;
}

interface AccountTopUpModalProps {
  accountRechargeShow: boolean;
  cancel: () => void;
  submitIng?: boolean;   
  cardAccountId: string;
  appid: string;
}

export const AccountTopUpModal = ({
  accountRechargeShow,
  cancel,
  submitIng = false,  
  cardAccountId,
  appid
}: AccountTopUpModalProps) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [options, setOptions] = useState<{value: string; label: string}[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchParams, setSearchParams] = useState({id: '', name: ''});
  const location = useLocation();

  const fetchAccounts = useCallback(debounce(async (params: {id?: string; name?: string} = {}) => {
    if (!accountRechargeShow) return;
    
    setLoading(true);
    try {
      const response = await getAccountList({
        pageNum: 1,
        pageSize: 40,
        appid: location.state.appid,
        id: params.id,
        accountName: params.name
      });
      const { data } = response;
      setOptions(data?.list?.map((item: AccountItem) => ({
        value: item.id,
        label: `${item.accountName} [ ${item.id} ] USD ( ${item.usd} )`,
      })));
      form.resetFields(['accountId']); // Clear selection after fetching new accounts
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  }, 500), [accountRechargeShow, location.state.appid, form]);

  useEffect(() => {
    if (!accountRechargeShow) {
      setOptions([]);
      setSearchParams({id: '', name: ''});
      form.resetFields(['accountId']);
      return;
    }
    fetchAccounts();
    form.resetFields(['accountId']);
  }, [accountRechargeShow, fetchAccounts, form]);

  const handleIdSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const id = e.target.value;
    setSearchParams(prev => ({...prev, id}));
    fetchAccounts({id, name: searchParams.name});
  };

  const handleNameSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const name = e.target.value;
    setSearchParams(prev => ({...prev, name}));
    fetchAccounts({id: searchParams.id, name});
  };

  const accountRecharge = async () => {
    try {
      const values = await form.validateFields();
      const response:any = await postCardTopup({
        accountId: values?.accountId,
        cardAccountId,
        currency: 'USD',
        amount: values.amount,
        appid
      });

      if (response?.code === 200) {
        message.success(t('增值成功'));
        cancel();
        return true;
      }
      message.error(response?.message);
    } catch (error) {
      console.error(error);
    }
    return false;
  };

  return (
    <Modal
      maskClosable={false}
      keyboard={false}
      title={t("账户增值")}
      open={accountRechargeShow}
      centered
      width={700}
      onCancel={cancel}
      destroyOnClose={true}
      footer={null}
    >
      <Form form={form} layout="vertical">
        <div className="mb-4">
          <div className="mb-2">
            <span className="text-[12px]">{t('搜索账户')}</span>
          </div>
          <div className="flex gap-2 mb-10">
            <div className="flex-1">
              <div className="text-[12px] mb-1">{t('账户ID')}</div>
              <Input
                placeholder={t('输入账户ID')}
                value={searchParams.id}
                onChange={handleIdSearch}
                allowClear
              />
            </div>
            <div className="flex-1">
              <div className="text-[12px] mb-1">{t('账户名称')}</div>
              <Input
                placeholder={t('输入账户名称')}
                value={searchParams.name}
                onChange={handleNameSearch}
                allowClear
              />
            </div>
          </div>
          <Form.Item
            label={<span className='text-[12px]'>{t('选择账户')} ({options.length})</span>}
            name="accountId"
            rules={[{ required: true, message: t('请选择账户') }]}
          >
            <Select
              showSearch
              placeholder=""
              loading={loading}
              options={options}
              style={{ width: '100%' }}
              filterOption={(input, option) =>
                (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
              }
            />
          </Form.Item>
        </div>
        <Form.Item
          label={<span className='text-[12px]'>{t('金额')}</span>}
          name="amount"
          rules={[{ required: true, message: t('金额') }]}
        >
          <Input className='w-full' />
        </Form.Item>
      </Form>
      <Flex>
        <Button className='w-full mr-[5px]' shape='round' onClick={cancel}>{t('取消')}</Button>
        <Button className='w-full ml-[5px]' shape='round' type='primary' loading={submitIng} onClick={accountRecharge}>{t('增值')}</Button>
      </Flex>
    </Modal>
  );
};
