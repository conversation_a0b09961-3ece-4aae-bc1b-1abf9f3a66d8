import { useEffect, useState } from "react";
import { getOrganizList } from "@/api/sys";
import { DebounceSelect } from "@/components/DebounceSelect/Index";
import { useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import type { SelectProps } from "antd";
import { Organization, OrganizationOption } from "@/types/organization";
import { RootState } from "@/redux/store";
import { ApiResponse } from "@/types/organization";

interface OrganizationSelectProps extends Omit<SelectProps, "options"> {
  value?: string;
  onChange?: (value: string | undefined) => void;
  autoSelectFirst?: boolean;
}

export const OrganizationSelect = ({
  value,
  onChange,
  autoSelectFirst = false,
  ...props
}: OrganizationSelectProps) => {
  const { t } = useTranslation();
  const [defaultList, setDefaultList] = useState<OrganizationOption[]>([]);
  const userStore = useSelector((state: RootState) => state.user);

  const fetchOrganizationList = async (searchText?: string) => {
    try {
      if (!searchText && defaultList.length > 0) {
        return defaultList;
      }

      const response = await getOrganizList({
        pageNum: 1,
        pageSize: 20,
        companyName: searchText,
      }) as unknown as ApiResponse<Organization[]>;

      if (response.code === 200 && Array.isArray(response.data)) {
        const list = response.data.map((org: Organization) => ({
          value: org.appid,
          label: org.companyName,
        }));
        
        if (!searchText) {
          setDefaultList(list);
        }
        return list;
      }
      return [];
    } catch (error) {
      console.error("Failed to fetch organizations:", error);
      return [];
    }
  };

  useEffect(() => {
    if (userStore?.user) {
      fetchOrganizationList().then((list) => {
        setDefaultList(list);
        if (autoSelectFirst && list.length === 1 && onChange) {
          onChange(list[0].value);
        }
      });
    }
  }, [userStore.user, autoSelectFirst, onChange]);

  return (
    <DebounceSelect
      showSearch
      value={value ? {
        key: value,
        value,
        label: defaultList.find(item => item.value === value)?.label || value
      } : undefined}
      placeholder={t("请选择组织")}
      defaultList={defaultList}
      fetchOptions={fetchOrganizationList}
      onChange={(newValue: OrganizationOption & { key?: string } | null) => {
        if (!newValue) {
          onChange?.(undefined);
        } else {
          onChange?.(newValue.value);
        }
      }}
      {...props}
    />
  );
};
