import axios from "axios";
import { md5 } from "js-md5";
import i18n from '@/i18n';
import { message } from "antd";
import { store } from "@/redux/store";
import { setuserSlice } from '@/redux/user'


// 是否显示重新登录
export let isRelogin = { show: false };
export const errorCode: any = {
    '401': i18n.t('认证失败，无法访问系统资源'),
    '403': i18n.t('当前操作没有权限'),
    '404': i18n.t('访问资源不存在'),
    'default': i18n.t('系统未知错误，请反馈给管理员'),
    "ERR_BAD_REQUEST": i18n.t("错误请求"),
    "11401": i18n.t("登录状态过期,重新登录"),
}


export function randomString(e: any) {
    e = e || 32;
    var t = "ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678",
        a = t.length,
        n = "";
    for (let i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a));
    return n;
}

export function getSignature(headers: any, data: any, appkey: any, secret: any) {
    // let { timestamp, nonce } = headers;
    let nonce = randomString(14);
    let timestamp = new Date().getTime();
    headers["appkey"] = appkey;
    headers["nonce"] = nonce;
    headers["timestamp"] = timestamp;
    let buf: any = appkey + secret;
    if (data) buf += data;
    buf += nonce + timestamp + "verify";
    return md5(buf);
}

const getToken = () => {
    let userInfo: any = localStorage.getItem('persist:root')
    try {
        userInfo = JSON.parse(userInfo)
        userInfo = JSON.parse(userInfo.user)
        return userInfo
    } catch (e) {
        console.log(e);
        return {}
    }
}


// 创建axios实例
const service = axios.create({
    baseURL: '/trade/api',
    // baseURL: import.meta.env.DEV ? 'http://192.168.3.47/trade/api' : '/trade/api',
    // baseURL:  'http://127.0.0.1:8132'  ,
    timeout: 60000,
});


service.defaults.headers["Content-Type"] = "application/json;charset=utf-8";

const excludeAPiList: any = ["/captchaImage"]

// request拦截器
service.interceptors.request.use(
    (config) => {
        // 是否需要防止数据重复提交
        const authInfo = getToken();//
        let data = null;
        if (config.method === "post") {
            if (config.data) data = JSON.stringify(config.data);
        }
        if (authInfo?.user && !excludeAPiList.includes(config.url)) {
            let { appkey, secret } = authInfo.user
            let sign = getSignature(config.headers, data, appkey, secret);
            config.headers["signature"] = sign;
        }

        return config;
    },
    (error) => {
        // console.log(error);
        Promise.reject(error);
    }
);

// 响应拦截器
service.interceptors.response.use(
    (res) => res.data,
    (error: any) => {
        const response = error.response
        const { status } = response
        if (errorCode[status]) {
            if (response.data.code == '11401') {
                message.destroy()
                message.open({
                    type: 'error',
                    content: errorCode['11401']
                })

                store.dispatch(setuserSlice(null))
                setTimeout(() => {
                    location.pathname !== '/admin' && (location.href = '/admin')
                }, 100)
                return error
            }
            message.destroy()
            message.open({
                type: 'error',
                content: errorCode[status]
            })
        }

        if ([400, 500].includes(response?.data?.code)) {
            message.destroy()
            message.open({
                type: 'error',
                content: i18n.t(response?.data?.message) || i18n.t("错误请求"),
            })
            return error?.response?.data;
        }

        return error
    }
);

export default service;
