import {
  Card,
  Flex,
  Table,
  Input,
  message,
  Space,
  Button,
  Modal,
  Form,
  Select,
  DatePicker,
  Upload,
  Image,
} from "antd";
const { RangePicker } = DatePicker;
import {
  SearchOutlined,
  ExclamationCircleOutlined,
  LoadingOutlined,
  PlusOutlined,
  CopyOutlined
} from "@ant-design/icons";
import { PageHeader } from "@/components";
import { useTranslation } from "react-i18next";
import { useEffect, useRef, useState } from "react";
import countryCodeList from "@/assets/json/country.json";
import dayjs from "dayjs";
import { DebounceSelect } from "@/components/DebounceSelect/Index";
import { postCountries, postOccupations } from "@/api/card";
import {
  getKYCList,
  postKYCAdd,
  postKYCAudit,
  postKYCDetails,
  postKYCAuditHistory,
  poseKYCIdentityUpdate,
} from "@/api/kyc";

import { getOrganizList } from "@/api/sys";
import { useSelector } from "react-redux";
import { AUDIOS_ENUM } from "@/utils/enum";
import { OrganizationSelect } from "@/components/OrganizationSelect";
import { KYCSnapshotHistory, KYCForm } from "@/components";
import type { KYCFormRef } from "@/components/KYCForm";

interface SearchParams {
  appid?: string;
  username?: string;
  mobileNumber?: string;
  email?: string;
  status?: string;
  beginTime?: number;
  endTime?: number;
}

const beforeUpload = () => false;
const { confirm } = Modal;
export const KYCPage = () => {
  const { t } = useTranslation();
  const [current, setCurrent] = useState<number>(1);
  const [total, setTotal] = useState<number>(0);
  const loadingRef = useRef(true);
  const [loading, setLoading] = useState(false);
  const [open, setOpen] = useState(false);
  const [data, setData] = useState([]);
  const [activeData, setActiveData] = useState<any>({});
  const formRef: any = useRef();
  // const [submitLoading, setSubmitLoading] = useState(false)
  const [front, setFront] = useState();
  const [back, setBack] = useState();
  const [tlementAccountValue, setTlementAccountValue] = useState<any>();
  const [defaultList, setDefaultList] = useState([]);
  const [submitIng, setSubmint] = useState(false);
  const userStore = useSelector((state: any) => {
    return state.user;
  });
  const [countryList, setCountryList] = useState<any>([]);
  const [occupationList, setOccupationList] = useState<any>([]);
  const [organizationValue, setOrganizationValue] = useState<string>();
  const [searchParams, setSearchParams] = useState<SearchParams>({});
  const [dateRange, setDateRange]: any = useState([]);
  const [auditHistoryModalOpen, setAuditHistoryModalOpen] = useState(false);
  const [auditHistoryData, setAuditHistoryData] = useState([]);
  const [auditHistoryLoading, setAuditHistoryLoading] = useState(false);
  const [selectedKycId, setSelectedKycId] = useState<string>('');
  const [snapshotHistoryModalOpen, setSnapshotHistoryModalOpen] = useState(false);
  const [updateModalOpen, setUpdateModalOpen] = useState(false);
  const [updateKycData, setUpdateKycData] = useState<any>(null);
  const [updateLoading, setUpdateLoading] = useState(false);
  const updateKycFormRef = useRef<KYCFormRef>(null);
  const [updateDateOfBirth, setUpdateDateOfBirth] = useState<string>();
  const [updateExpiryDate, setUpdateExpiryDate] = useState<string>();
  const [updateFrontImage, setUpdateFrontImage] = useState<string>();
  const [updateBackImage, setUpdateBackImage] = useState<string>();

  useEffect(() => {
    if (loadingRef.current) {
      loadingRef.current = false;
      query();
    }
  }, [current]);

  useEffect(() => {
    if (userStore && userStore.user) {
      qyeryDefaultList();
      queryCountry();
    }
  }, [userStore.user]);

  const pageChange = (page: number) => {
    setCurrent(page);
  };

  const query = (value?: any, type?: string) => {
    setLoading(true);
    getKYCList({
      pageNum: current,
      pageSize: 10,
      ...searchParams,
      beginTime: new Date(dateRange[0])?.getTime() || undefined,
      endTime: new Date(dateRange[1])?.getTime() || undefined,
    })
      .then((res: any) => {
        if (res.code === 200) {
          setTotal(res.data?.total);
          setData(res.data?.list || []);
        }
      })
      .catch((err) => {
        console.error(err);
      })
      .finally(() => {
        setLoading(false);
        loadingRef.current = true;
      });
  };
  const queryCountry = () => {
    postCountries({}).then((res: any) => {
      if (res.code == 200) {
        setCountryList(
          res.data?.map((v: any) => {
            return {
              value: v.code,
              label: v.name,
            };
          }) || []
        );
      }
    });
    postOccupations({}).then((res: any) => {
      if (res.code == 200) {
        setOccupationList(
          res.data?.map((v: any) => {
            return {
              value: v.name,
              label: v.name,
            };
          }) || []
        );
      }
    });
  };

  async function fetchUserList(username?: string): Promise<any> {
    return new Promise((resolve) => {
      getOrganizList({
        pageNum: 1,
        appid: userStore.user?.user?.appid,
        name: username || undefined,
      }).then((res: any) => {
        if (res.code == 200) {
          const list = res.data?.map((v: any) => {
            return {
              value: v?.appid,
              label: `${v.companyName}`,
            };
          });
          resolve(list);
          resolve(list);
          return;
        }
        resolve([]);
      });
    });
  }

  const qyeryDefaultList = async () => {
    const list = await fetchUserList();
    setDefaultList(list);
  };

  const add = () => {
    if (activeData.id) {
      close();
      return;
    }
    setOpen(true);
    if (defaultList.length == 1) {
      setTimeout(() => {
        //@ts-ignore
        setTlementAccountValue(defaultList[0]?.value);
        //@ts-ignore
        formRef.current?.setFieldsValue({ appid: defaultList[0]?.value });
      }, 50);
    }
  };
  const details = (row: any) => {
    // console.log(row);
    setLoading(true);
    postKYCDetails({
      id: row.id,
    })
      .then((res: any) => {
        if (res.code == 200) {
          setActiveData(res.data.infoVo);
          setOpen(true);
          setFront(res.data.documentBack.data);
          setBack(res.data.documentFront.data);
          setDateOfBirth(res.data.infoVo.dateOfBirth);
          setExpiryDate(res.data.infoVo.expiryDate);

          setTimeout(() => {
            // console.log(res.data.infoVo);
            formRef.current?.setFieldsValue({
              ...res.data.infoVo,
              num1: `+${res.data.infoVo.countryCode}`,
              num2: res.data.infoVo.mobileNumber,
              cardNumber: res.data.infoVo.number,
              dateOfBirth: dayjs(res.data.infoVo.dateOfBirth),
              expiryDate: dayjs(res.data.infoVo.expiryDate),
            });
          }, 30);
        }
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        setLoading(false);
        loadingRef.current = true;
      });
  };

  const handleAuditHistory = (record: any) => {
    setSelectedKycId(record.id);
    setAuditHistoryLoading(true);
    setAuditHistoryModalOpen(true);

    postKYCAuditHistory({ kycId: record.id })
      .then((res: any) => {
        if (res.code === 200) {
          setAuditHistoryData(res.data || []);
        } else {
          message.error(t("获取审核记录失败"));
          setAuditHistoryData([]);
        }
      })
      .catch((err) => {
        console.error(err);
        message.error(t("获取审核记录失败"));
        setAuditHistoryData([]);
      })
      .finally(() => {
        setAuditHistoryLoading(false);
      });
  };

  const handleCloseAuditHistoryModal = () => {
    setAuditHistoryModalOpen(false);
    setAuditHistoryData([]);
    setSelectedKycId('');
  };

  const getAuditStatusText = (status: number) => {
    const statusMap: { [key: number]: string } = {
      0: t("待处理"),
      1: t("通过"),
      2: t("拒绝")
    };
    return statusMap[status] || status;
  };

  const handleSnapshotHistory = (record: any) => {
    setSelectedKycId(record.id);
    setSnapshotHistoryModalOpen(true);
  };

  const handleCloseSnapshotHistoryModal = () => {
    setSnapshotHistoryModalOpen(false);
    setSelectedKycId('');
  };

  const handleUpdate = (record: any) => {
    setUpdateLoading(true);
    postKYCDetails({
      id: record.id,
    })
      .then((res: any) => {
        if (res.code === 200) {
          const kycInfo = res.data.infoVo;
          setUpdateKycData({
            ...kycInfo,
            frontImage: res.data.documentFront?.data,
            backImage: res.data.documentBack?.data,
          });
          setUpdateFrontImage(res.data.documentFront?.data);
          setUpdateBackImage(res.data.documentBack?.data);
          setUpdateDateOfBirth(kycInfo.dateOfBirth);
          setUpdateExpiryDate(kycInfo.expiryDate);
          setUpdateModalOpen(true);
        } else {
          message.error(t("获取KYC详情失败"));
        }
      })
      .catch((err) => {
        console.error(err);
        message.error(t("获取KYC详情失败"));
      })
      .finally(() => {
        setUpdateLoading(false);
      });
  };

  const handleCloseUpdateModal = () => {
    setUpdateModalOpen(false);
    setUpdateKycData(null);
    setUpdateFrontImage(undefined);
    setUpdateBackImage(undefined);
    setUpdateDateOfBirth(undefined);
    setUpdateExpiryDate(undefined);
    updateKycFormRef.current?.resetFields();
  };

  const handleSubmitUpdate = () => {
    updateKycFormRef.current?.validateFields().then((values: any) => {
      setUpdateLoading(true);

      const submitData = {
        id: updateKycData.id,
        identityType: "individual",
        accountType: "prepaid",
        firstName: values.firstName,
        lastName: values.lastName,
        firstNameLocal: values.firstNameLocal,
        lastNameLocal: values.lastNameLocal,
        email: values.email,
        dateOfBirth: updateDateOfBirth,
        countryCode: values.num1?.replace('+', ''),
        mobileNumber: values.num2,
        annualIncome: values.annualIncome,
        occupation: values.occupation,
        position: values.position,
        document: {
          front: updateFrontImage,
          back: updateBackImage,
          type: values.type,
          number: values.cardNumber,
          country: values.country,
          address: values.address,
          expiryDate: updateExpiryDate,
        },
      };

      // // 这里需要添加更新KYC的API调用
      // // 暂时使用postKYCAdd作为示例，实际应该使用更新API
      // console.log('提交更新数据:', submitData);

      // // 模拟API调用
      // setTimeout(() => {
      //   message.success(t("KYC更新成功"));
      //   handleCloseUpdateModal();
      //   query(); // 刷新列表
      //   setUpdateLoading(false);
      // }, 1000);
      poseKYCIdentityUpdate(submitData)
              .then((res: any) => {
                if (res.code === 200) {
                  message.success(t("KYC更新成功"));
                  handleCloseUpdateModal();
                  query(); // 刷新列表
                } else {
                  message.error(t(res.message || "更新失败"));
                }
              })
              .catch((err) => {
                console.error(err);
                message.error(t("更新失败"));
              })
              .finally(() => {
                setUpdateLoading(false);
              });
    });
  };



  const hairpin = () => {
    formRef.current?.validateFields().then((values: any) => {
      setSubmint(true);
      postKYCAdd({
        appid: values.appid?.value || values.appid,
        accountType: "prepaid",
        identityType: "individual",
        username: `${values?.firstName}${values?.lastName}`,
        firstName: values?.firstName,
        lastName: values?.lastName,
        firstNameLocal: values?.firstNameLocal,
        lastNameLocal: values?.lastNameLocal,
        email: values.email,
        dateOfBirth,
        countryCode: `${values["num1"]}`,
        mobileNumber: `${values["num2"]}`,
        annualIncome: values.annualIncome,
        occupation: values.occupation, //职业
        position: values.position, //职位
        document: {
          front,
          back,
          type: values.type,
          number: values.cardNumber,
          country: values.country, //国家
          address: values.address,
          expiryDate, //有效期
        },
      })
        .then((res: any) => {
          if (res.code == 200) {
            close();
            query();
            message.open({ type: "success", content: t("添加成功") });
            return;
          }
          message.open({ type: "error", content: t(res.message) });
        })
        .catch(() => {})
        .finally(() => {
          setSubmint(false);
        });
    });
  };

  const getBase64 = (img: any, callback: (url: string) => void) => {
    const reader = new FileReader();
    reader.addEventListener("load", () => callback(reader.result as string));
    reader.readAsDataURL(img);
  };

  const handleChange: any = (info: any) => {
    getBase64(info.file, (url: any) => {
      setFront(url);
    });
  };
  const handleChangeBack: any = (info: any) => {
    getBase64(info.file, (url: any) => {
      setBack(url);
    });
  };

  const uploadButton = (
    <button style={{ border: 0, background: "none" }} type="button">
      {loading ? <LoadingOutlined /> : <PlusOutlined />}
      <div style={{ marginTop: 8 }}>Upload</div>
    </button>
  );

  const close = () => {
    setOpen(false);
    setActiveData({});
    setFront(undefined);
    setBack(undefined);
    setDateOfBirth(undefined);
    setExpiryDate(undefined);
    setSubmint(false);
  };

  const resume = () => {
    confirm({
      icon: <ExclamationCircleOutlined />,
      centered: true,
      content: (
        <>
          {t("请确认是否通过")}
          <div>
            {t("申请人")}: {activeData.firstName}
            {activeData.lastName}
          </div>
        </>
      ),
      onOk() {
        setLoading(true);
        postKYCAudit({ kycId: activeData.id, status: "approved" })
          .then((res: any) => {
            const { code } = res;
            if (code == 200) {
              message.open({
                type: "success",
                content: t("成功"),
              });
              close();
              query();
              return;
            }
            message.open({
              type: "error",
              content: t(res.message),
            });
          })
          .finally(() => setLoading(false));
      },
      onCancel() {
        close();
        query();
        console.log("Cancel");
      },
    });
  };
  const del = () => {
    let value: any;
    confirm({
      icon: <ExclamationCircleOutlined />,
      centered: true,
      okButtonProps: {
        loading: loading,
      },
      content: (
        <>
          {t("请确认是否驳回")}
          <div>
            {" "}
            {t("申请人")}: {activeData.firstName}
            {activeData.lastName}
          </div>
          <div>
            {" "}
            {t("驳回原因")}:
            <Input.TextArea
              maxLength={500}
              onChange={(e: any) => {
                value = e.target.value;
              }}
              placeholder={t("请输入驳回原因")}
              autoSize={{ minRows: 3 }}
            />
          </div>
        </>
      ),

      onOk(_close) {
        setLoading(true);
        Modal.destroyAll();
        if (!value) {
          message.open({
            type: "error",
            content: t("请输入驳回原因"),
          });
          setLoading(false);
          return Promise.reject();
        }
        postKYCAudit({
          kycId: activeData.id,
          status: "rejected",
          reason: value,
        })
          .then((res: any) => {
            const { code } = res;
            if (code == 200) {
              message.open({
                type: "success",
                content: t("成功"),
              });
              close();
              _close();
              query();
              return;
            }
            message.open({
              type: "error",
              content: t(res.message),
            });
          })
          .finally(() => {
            setLoading(false);
          });
      },
      onCancel() {
        console.log("Cancel");
      },
    });
  };

  const [dateOfBirth, setDateOfBirth] = useState();
  const [expiryDate, setExpiryDate] = useState();
  const onDateChange = (_: any, dateString: any) => {
    // console.log(date, dateString);
    setDateOfBirth(dateString);
  };
  const onDateChange2 = (_: any, dateString: any) => {
    // console.log(date, dateString);
    setExpiryDate(dateString);
  };

  return (
    <div className=" relative">
      <PageHeader title={"KYC"} breadcrumbs={[]} />
      <Card className="mt-[20px]">
        <Flex wrap="wrap" gap="small" className="mb-[20px]">
          <OrganizationSelect
            allowClear
            className="w-[200px]"
            placeholder={t("请选择组织")}
            onChange={(value) => {
              setSearchParams((prev) => ({ ...prev, appid: value }));
            }}
          />
          <Input
            allowClear
            className="w-[200px]"
            placeholder={t("请输入名称")}
            onChange={(e) => {
              setSearchParams((prev) => ({ ...prev, username: e.target.value }));
            }}
            onPressEnter={() => query()}
          />
          <Input
            allowClear
            className="w-[200px]"
            placeholder={t("请输入电话号码")}
            onChange={(e) => {
              setSearchParams((prev) => ({ ...prev, mobileNumber: e.target.value }));
            }}
            onPressEnter={() => query()}
          />
          <Input
            allowClear
            className="w-[200px]"
            placeholder={t("请输入邮箱")}
            value={searchParams.email}
            onChange={(e) => {
              setSearchParams((prev) => ({ ...prev, email: e.target.value }));
            }}
            onPressEnter={() => query()}
          />
          <Select
            allowClear
            className="w-[200px]"
            placeholder={t("请选择状态")}
            onChange={(value) => {
              setSearchParams((prev) => ({ ...prev, status: value }));
            }}
            options={Object.keys(AUDIOS_ENUM()).map((v) => ({
              label: AUDIOS_ENUM()[v],
              value: v,
            }))}
          />
          <RangePicker
            showTime
            allowClear
            className="w-[300px]"
            onChange={(_, dateStrings) => {
              setDateRange(dateStrings);
            }}
          />
          <Flex className="ml-auto gap-[10px]">
            <Button type="primary" onClick={() => query()}>
              {t("搜索")}
            </Button>
            <Button onClick={add} type="primary">
              {t("新增")}
            </Button>
          </Flex>
        </Flex>
        <Table
          loading={loading}
          rowKey={(row: any) => row.id}
          scroll={{ x: 1000 }}
          className="mt-[20px]"
          columns={[
            {
              title: t("ID"),
              dataIndex: "id",
              key: "id",
            },
            {
              title: t("名称"),
              dataIndex: "username",
              key: "username",
              ellipsis: true
            },
            {
              title: t("firstName"),
              dataIndex: "firstName",
              key: "firstName",
              ellipsis: true
            },
            {
              title: t("lastName"),
              dataIndex: "lastName",
              key: "lastName",
              ellipsis: true
            },
            {
              title: t("名称Local"),
              dataIndex: "name",
              key: "name",
              ellipsis: true,
              render: (_, row: any) => {
                return (row?.firstNameLocal || "") + (row?.lastNameLocal || "");
              },
            },
            {
              title: t("电话号码"),
              dataIndex: "countryCode",
              key: "countryCode",
              ellipsis: true,
              render: (_, row: any) => {
                return `+${row.countryCode} ${row.mobileNumber}`;
              },
            },
            {
              title: t("邮箱"),
              dataIndex: "email",
              key: "email",
              render: (text) => (
                <span>
                  {text}
                  <CopyOutlined
                    className="ml-2 cursor-pointer text-blue-500"
                    onClick={() => {
                      console.log(text)
                      setSearchParams(prev => ({ ...prev, email: text }));
                    }}
                  />
                </span>
              ),
            },
            {
              title: t("使用的卡数量"),
              dataIndex: "cardCount",
              key: "cardCount",
              ellipsis: true,
            },
            {
              title: t("卡账户数量"),
              dataIndex: "cardAccountCount",
              key: "cardAccountCount",
              ellipsis: true,
            },
            {
              title: t("申请时间"),
              dataIndex: "creTime",
              key: "creTime",
              ellipsis: true,
            },

            {
              title: t("状态"),
              dataIndex: "status",
              key: "status",
              render: (_, row: any) => {
                return AUDIOS_ENUM()[row.status];
              },
            },
            {
              title: t("操作"),
              key: "action",
              fixed: "right",
              width: 280,
              render: (_, record) => (
                <Space size="small">
                  <Button
                    type="text"
                    className="_primary"
                    size="small"
                    onClick={details.bind(this, record)}
                  >
                    {t("详情")}{" "}
                  </Button>
                  {record.status === 2 && (
                    <Button
                      type="text"
                      className="_primary"
                      size="small"
                      loading={updateLoading}
                      onClick={() => handleUpdate(record)}
                    >
                      {t("更新")}
                    </Button>
                  )}
                  <Button
                    type="text"
                    className="_primary"
                    size="small"
                    onClick={() => handleAuditHistory(record)}
                  >
                    {t("审核记录")}
                  </Button>
                  <Button
                    type="text"
                    className="_primary"
                    size="small"
                    onClick={() => handleSnapshotHistory(record)}
                  >
                    {t("快照记录")}
                  </Button>
                </Space>
              ),
            },
          ]}
          dataSource={data}
          pagination={{
            hideOnSinglePage: true,
            showSizeChanger: false,
            pageSize: 10,
            current,
            total,
            onChange: pageChange,
            position: ["bottomRight"],
          }}
        />
      </Card>
      <Modal
        title={activeData.id ? t("详情") : t("新建")}
        open={open} 
        onCancel={close}
        centered
        maskClosable={false}
        keyboard={false}
        footer={null}
        width={800}
        confirmLoading={submitIng}
        okText={t("确认")}
        destroyOnClose={true}
        cancelText={t("取消")}
      >
        <Form
          ref={formRef}
          autoComplete="off"
          disabled={activeData.id ? true : false}
          requiredMark={false}
          layout="vertical"
        >
          <Flex>
            <div>
              <Flex>
                <Form.Item
                  label={t("名")}
                  name="firstName"
                  className="w-[50%] mr-[10px]"
                  rules={[{ required: true, message: t("名") }]}
                >
                  <Input />
                </Form.Item>
                <Form.Item
                  name="lastName"
                  label={t("姓氏")}
                  className="w-[50%]"
                  rules={[{ required: true, message: t("姓氏") }]}
                >
                  <Input />
                </Form.Item>
              </Flex>

              <Flex>
                <Form.Item
                  label={t("名(Local)")}
                  name="firstNameLocal"
                  className="w-[50%] mr-[10px]"
                  rules={[{ required: true, message: t("名(Local)") }]}
                >
                  <Input />
                </Form.Item>
                <Form.Item
                  name="lastNameLocal"
                  label={t("姓氏(Local)")}
                  className="w-[50%]"
                  rules={[{ required: true, message: t("姓氏(Local)") }]}
                >
                  <Input />
                </Form.Item>
              </Flex>

              <Form.Item className="mb-[0] w-full" label={t("手机号码")}>
                <Flex>
                  <Form.Item
                    name="num1"
                    className="w-[100px] mr-[10px]"
                    rules={[{ required: true, message: t("名称") }]}
                  >
                    <Select
                      className="w-[100px] mr-[10px]"
                      showSearch
                      options={countryCodeList.map((v) => {
                        return {
                          value: v.code,
                          label: v.code,
                        };
                      })}
                    />
                    {/* <Input className='w-[60px] mr-[10px]' /> */}
                  </Form.Item>
                  <Form.Item
                    name="num2"
                    className="w-full"
                    rules={[{ required: true, message: t("请输入账户名称") }]}
                  >
                    <Input />
                  </Form.Item>
                </Flex>
              </Form.Item>

              <Form.Item
                label="E-Mail"
                name="email"
                className="w-full"
                rules={[{ required: true, message: t("请输入邮箱") }]}
              >
                <Input />
              </Form.Item>
              <Flex>
                <Form.Item
                  label={t("证件类型")}
                  name="type"
                  className="w-[40%] mr-[10px]"
                  rules={[{ required: true, message: t("证件类型") }]}
                >
                  <Select
                    placeholder={t("证件类型")}
                    options={[
                      {
                        value: "passport",
                        label: t("护照"),
                      },
                      {
                        value: "drivers-license",
                        label: t("驾照"),
                      },
                      {
                        value: "national-id",
                        label: t("国民身份证"),
                      },
                    ]}
                  />
                </Form.Item>

                <Form.Item
                  label={t("证件号码")}
                  name="cardNumber"
                  className="w-full"
                  rules={[{ required: true, message: t("请输入证件号码") }]}
                >
                  <Input />
                </Form.Item>
              </Flex>
              <Flex>
                <Form.Item
                  label={t("出生日期")}
                  name="dateOfBirth"
                  className="w-full"
                  rules={[{ required: true, message: t("出生日期") }]}
                >
                  <DatePicker onChange={onDateChange} />
                </Form.Item>
                <Form.Item
                  label={t("证件有效期")}
                  name="expiryDate"
                  className="w-full"
                  rules={[{ required: true, message: t("证件有效期") }]}
                >
                  <DatePicker onChange={onDateChange2} />
                </Form.Item>
              </Flex>
              <Flex>
                <Form.Item
                  label={t("证件照正面")}
                  name="front"
                  className="w-full"
                  rules={[{ required: true, message: t("证件照正面") }]}
                >
                  <Flex align="center">
                    {front ? (
                      <Image
                        className="w-full h-auto max-w-[100px] max-h-[100px] "
                        src={front}
                      />
                    ) : (
                      ""
                    )}
                    {activeData.id ? (
                      ""
                    ) : (
                      <Upload
                        name="front"
                        listType="picture-card"
                        className="avatar-uploader ml-[20px]"
                        showUploadList={false}
                        accept="image/png"
                        beforeUpload={beforeUpload}
                        onChange={handleChange}
                      >
                        {uploadButton}
                      </Upload>
                    )}
                  </Flex>
                </Form.Item>

                <Form.Item
                  label={t("证件照反面")}
                  name="back"
                  className="w-full"
                  rules={[{ required: true, message: t("证件照反面") }]}
                >
                  <Flex align="center">
                    {back ? (
                      <Image
                        className="w-full ml-[10px] h-auto max-w-[100px] max-h-[100px] "
                        src={back}
                      />
                    ) : (
                      ""
                    )}
                    {activeData.id ? (
                      ""
                    ) : (
                      <Upload
                        name="back"
                        listType="picture-card"
                        className="avatar-uploader ml-[20px]"
                        showUploadList={false}
                        accept="image/png"
                        beforeUpload={beforeUpload}
                        onChange={handleChangeBack}
                      >
                        {uploadButton}
                      </Upload>
                    )}
                  </Flex>
                </Form.Item>
              </Flex>
            </div>
            <div className="w-[50%] ml-[20px]">
              <Form.Item
                label={t("年收入")}
                name="annualIncome"
                className="w-full"
                rules={[{ required: true, message: t("请输入年收入") }]}
              >
                <Input />
              </Form.Item>
              <Form.Item
                label={t("职业")}
                name="occupation"
                className="w-full"
                rules={[{ required: true, message: t("请输入职业") }]}
              >
                <Select
                  showSearch
                  placeholder={t("职业")}
                  options={occupationList}
                />
              </Form.Item>
              <Form.Item
                label={t("职位")}
                name="position"
                className="w-full"
                rules={[{ required: true, message: t("请输入职位") }]}
              >
                <Input />
              </Form.Item>
              <Flex>
                <Form.Item
                  label={t("国家")}
                  name="country"
                  className="w-[30%] mr-[10px]"
                  rules={[{ required: true, message: t("国家") }]}
                >
                  <Select
                    showSearch
                    placeholder={t("国家")}
                    options={countryList}
                  />
                </Form.Item>
                <Form.Item label={t("地址")} className="w-[60%]" name="address">
                  <Input />
                </Form.Item>
              </Flex>
              <Form.Item
                label={t("组织")}
                name="appid"
                rules={[{ required: true, message: t("请选择组织") }]}
              >
                <OrganizationSelect
                  allowClear
                  placeholder={t("请选择组织")}
                  autoSelectFirst={true}
                />
              </Form.Item>
              {activeData?.reason && activeData?.id ? (
                <Form.Item label={t("驳回原因")} name="reason">
                  {activeData.reason}
                </Form.Item>
              ) : (
                ""
              )}
            </div>
          </Flex>
          <Flex justify="center">
            <Button
              className="w-full mr-[5px] max-w-[300px]"
              disabled={false}
              shape="round"
              onClick={close}
            >
              {t("取消")}
            </Button>
            {activeData.id &&
            activeData?.status == 0 &&
            userStore?.user?.user?.roleType == "0" ? (
              <>
                <Button
                  className="w-full mr-[5px]"
                  shape="round"
                  disabled={false}
                  type="primary"
                  onClick={resume}
                >
                  {t("通过")}
                </Button>
                <Button
                  loading={submitIng}
                  disabled={submitIng}
                  className="w-full ml-[5px]"
                  shape="round"
                  color="primary"
                  danger
                  onClick={del}
                >
                  {t("拒绝")}
                </Button>
              </>
            ) : (
              ""
            )}
            {!activeData.id ? (
              <Button
                loading={submitIng}
                disabled={submitIng}
                className="w-full ml-[5px]"
                shape="round"
                type="primary"
                onClick={hairpin}
              >
                {t("确认")}
              </Button>
            ) : (
              ""
            )}
          </Flex>
        </Form>
      </Modal>

      {/* 审核记录弹窗 */}
      <Modal
        title={t("审核记录")}
        open={auditHistoryModalOpen}
        onCancel={handleCloseAuditHistoryModal}
        footer={[
          <Button key="close" onClick={handleCloseAuditHistoryModal}>
            {t("关闭")}
          </Button>
        ]}
        width={800}
        centered
      >
        <Table
          loading={auditHistoryLoading}
          rowKey={(row: any) => row.id}
          dataSource={auditHistoryData}
          pagination={false}
          size="small"
          columns={[
            {
              title: t("ID"),
              dataIndex: "id",
              key: "id",
              width: 100,
            },
            {
              title: t("审核员"),
              dataIndex: "auditorUsername",
              key: "auditorUsername",
              width: 120,
            },
            {
              title: t("审核时间"),
              dataIndex: "auditTime",
              key: "auditTime",
              width: 160,
              render: (timestamp: number) => {
                return timestamp ? new Date(timestamp).toLocaleString() : '-';
              },
            },
            {
              title: t("审核结果"),
              dataIndex: "auditStatus",
              key: "auditStatus",
              width: 100,
              render: (status: number) => {
                return getAuditStatusText(status);
              },
            },
            {
              title: t("原因"),
              dataIndex: "reason",
              key: "reason",
              ellipsis: true,
              render: (text: string) => text || '-',
            },
            {
              title: t("备注"),
              dataIndex: "remark",
              key: "remark",
              ellipsis: true,
              render: (text: string) => text || '-',
            },
          ]}
        />
      </Modal>

      {/* 快照记录组件 */}
      <KYCSnapshotHistory
        open={snapshotHistoryModalOpen}
        onClose={handleCloseSnapshotHistoryModal}
        kycId={selectedKycId}
      />

      {/* KYC更新弹窗 */}
      <Modal
        title={t("更新KYC信息")}
        open={updateModalOpen}
        onCancel={handleCloseUpdateModal}
        footer={[
          <Button key="cancel" onClick={handleCloseUpdateModal}>
            {t("取消")}
          </Button>,
          <Button
            key="submit"
            type="primary"
            loading={updateLoading}
            onClick={handleSubmitUpdate}
          >
            {t("提交更新")}
          </Button>
        ]}
        width={1000}
        centered
        destroyOnClose
      >
        {updateKycData && (
          <KYCForm
            ref={updateKycFormRef}
            initialData={updateKycData}
            showOrganization={false}
            onDateOfBirthChange={setUpdateDateOfBirth}
            onExpiryDateChange={setUpdateExpiryDate}
            onFrontImageChange={setUpdateFrontImage}
            onBackImageChange={setUpdateBackImage}
          />
        )}
      </Modal>
    </div>
  );
};
