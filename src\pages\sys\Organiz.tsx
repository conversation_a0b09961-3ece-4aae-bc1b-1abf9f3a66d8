import {
    Table, Input, Switch, InputNumber,
    Modal, Form, message, Card, Flex, Button,
    // Select,
    Radio
} from 'antd';
import { TenantLimitButton } from '@/components';
import {
    HomeOutlined,
    SearchOutlined
} from '@ant-design/icons';
import { PageHeader } from '@/components';
import { useTranslation } from 'react-i18next';
import { useEffect, useRef, useState } from 'react';
import {
    getOrganiz,
    postAddOrganiz,
    postUpdateOrganiz,
    postQueryTenantFee,
    postUpdTenantFee,
    repairAccountTenantFeeSetting,
} from '@/api/sys'
import { useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { SYS_ROLE_ENUM, EVENT_FEEENUM } from '@/utils/enum';

export const OrganizPage = () => {
    const { t } = useTranslation()
    const [current, setCurrent] = useState<number>(1)
    const [total, setTotal] = useState<number>(0)
    const loadingRef = useRef(true)
    const [loading, setLoading] = useState(false)
    const [open, setOpen] = useState(false);
    const [openFee, setOpenFee] = useState(false);
    const [data, setData] = useState([])
    const formRef: any = useRef()
    // const formRef2: any = useRef()
    const [activeData, setActive] = useState<any>()
    const navigate = useNavigate();
    const [activeFeeList, setActiveFeeList] = useState([])
    const userStore = useSelector((state: any) => { return state.user });


    useEffect(() => {
        if (loadingRef.current) {
            loadingRef.current = false
            query()
        }
    }, [])
    const pageChange = (page: number) => {
        setCurrent(page)
    }

    const query = (value?: any, type?: string) => {
        if (!loading) {
            getOrganiz({
                pageNum: 1,
                pageSize: 20,
                companyName: type == 'search' ? value : undefined
            }).then((res: any) => {
                if (res.code == 200) {
                    setTotal(res.data?.total)
                    setData(res.data?.list.map((v: any) => {
                        return {
                            ...v,
                            tanantAccountNumber: v.tanantAccountNumber ? `${v.tanantAccountNumber}`.replace(/^168\-/g, '') : ''
                        }
                    }))
                }
            }).catch(err => {
                console.log(err);
            }).finally(() => {
                setLoading(false)
            })
        }
    }
    const queryFee = (row: any) => {
        if (!loading) {
            postQueryTenantFee({
                appid: row.appid
            }).then((res: any) => {
                if (res.code == 200) {
                    setActiveFeeList(res.data)
                    setOpenFee(true)
                }
            }).catch(err => {
                console.log(err);
            }).finally(() => {
                setLoading(false)
            })
        }
    }
    
    const handelGenTenantId = () => {
        return "TN-" + Math.floor(Math.random() * 999999 + 100000);
    }
    const cancel = () => {
        setOpen(false);
        setActive({})
        setOpenFee(false)
        // setActiveFeeType(true)
    }
    const addAccount = () => {
        // cancel()
        let api = postAddOrganiz
        if (activeData?.id) api = postUpdateOrganiz
        formRef.current?.validateFields().then((values: any) => {
            api({
                roleType: "1",
                merchantId: handelGenTenantId(),
                ...values,
                tanantAccountNumber: values?.tanantAccountNumber ? `168-${values?.tanantAccountNumber}` : '',
                id: activeData?.id || undefined,
                withdrawSwitch: values?.withdrawSwitch ? 1 : 0,
                transferSwitch: values?.transferSwitch ? 1 : 0,
                smsSwitch: values?.smsSwitch ? 1 : 0,
                vpSwitch: values?.vpSwitch ? 1 : 0,
            }).then((res: any) => {
                const { code } = res
                if (code == 200) {
                    message.open({
                        type: 'success',
                        content: activeData?.id ? t('修改成功') : t('添加成功')
                    })
                    cancel()
                    query()
                    return
                }
                message.open({
                    type: 'error',
                    content: t(res.message)
                })
            })
        })

    }


    const add = () => {
        setOpen(true);
    }

    const handleRepairUsdtFeeSetting  = () => {
        Modal.confirm({
            title: '确认操作',
            content: '您确定要修复 USDT 费率设置吗？',
            onOk() {
                repairAccountTenantFeeSetting({})
                .then((res: any) => {
                    if (res.code == 200) {
                        message.success('USDT 费率设置修复成功');
                    } else {
                        message.error('USDT 费率设置修复失败');
                    }
                })
                .catch(err => {
                    console.log(err);
                    message.error('操作失败，请重试');
                })
                .finally(() => {
                    // 可以在这里添加一些最终操作，例如关闭加载状态等
                });
            },
            onCancel() {
              console.log('取消操作');
            },
          });  
    }

    const toUrl = (row: any) => {
        navigate('/sys/organiz/apiConfig', { state: { id: row?.appid } });
    }
    const toUrl2 = (row: any) => {
        navigate('/sys/organiz/cardConfig', { state: { id: row?.appid } });
    }

    const updataFee = () => {

        setLoading(true)
        // formRef2.current?.validateFields().then((values: any) => {
        // })
        Promise.allSettled(
            activeFeeList.map((v: any) =>
                postUpdTenantFee({
                    ...v,
                    multiplicationFactor: parseFloat((v?.multiplicationFactor).toFixed(4)),
                })
            )
        ).then((res: any) => {
            const successes = res.filter((res: any) => res.status === "fulfilled")
            if (successes.length === activeFeeList.length) {
                message.open({
                    type: 'success',
                    content: activeData?.id ? t('修改成功') : t('添加成功'),
                })
                cancel()
                query()
            } else {
                message.open({
                    type: 'error',
                    content: t('部分请求失败，请检查网络或数据'),
                })
            }
        }).finally(() => {
            setLoading(false)
        })
    }
    return (
        <div>
            <PageHeader
                title=""
                breadcrumbs={[
                    {
                        title: (
                            <>
                                <HomeOutlined />
                                <span>{t('组织管理')}</span>
                            </>
                        ),

                    },
                ]}
            />
            <Card className='mt-[20px]'>
                <Flex justify='space-between'>
                    <Input
                        prefix={<SearchOutlined />}
                        allowClear
                        placeholder={t('请输入关键字进行搜索')}
                        className='w-[30%] max-w-[300px]'
                        onBlur={(e: any) => query(e.target.value, 'search')}
                    />
                    <div>
                        {userStore?.user?.user?.roleType == "0" ? <Button style={{ marginRight: '10px' }}  onClick={handleRepairUsdtFeeSetting } type='primary'> {t('修复USDT feeSetting')}</Button> : ''}
                        {userStore?.user?.user?.roleType == "0" ? <Button onClick={add} type='primary'> {t('新增组织')}</Button> : ''}
                    </div>
                </Flex>
                <Table
                    loading={loading}
                    className='mt-[20px]'
                    rowKey={(row: any) => row.id}
                    columns={[
                        {
                            title: t('组织名称'),
                            dataIndex: 'companyName',
                            key: 'companyName',
                        },
                        {
                            title: t('组织ID'),
                            dataIndex: 'appid',
                            key: 'appid',
                        },
                        {
                            title: t('组织类型'),
                            dataIndex: 'roleType',
                            key: 'roleType',
                            render: (roleType: number) => SYS_ROLE_ENUM()[`${roleType}`] || t('未知类型'),
                        },
                        {
                            title: t('组织电话'),
                            dataIndex: 'companyPhone',
                            key: 'companyPhone',
                        },
                        {
                            title: t('组织编号'),
                            dataIndex: 'tanantAccountNumber',
                            key: 'tanantAccountNumber',
                            render: (v: any) => v && `168-${v}` || '-'
                        },
                        {
                            title: t('新增时间'),
                            dataIndex: 'createTime',
                            key: 'createTime',
                            render: (createTime: number) => {
                                return createTime ? new Date(createTime).toLocaleString() : t('');
                            },
                        },
                        {
                            title: t('操作'),
                            key: 'action',
                            render: (_, row: any) => (
                                <>
                                    <Button type="text" className='cursor-pointer _primary' size='small' onClick={() => {
                                        setActive(row)
                                        setOpen(true)
                                        setTimeout(() => {
                                            formRef.current?.setFieldsValue({ ...row })
                                        }, 10)
                                    }}>{t('修改')}</Button>
                                    <Button type='text' className='cursor-pointer _primary' size='small' onClick={() => {
                                        toUrl(row)
                                    }}>{t('API配置')}</Button>
                                    <Button type='text' className='cursor-pointer _primary' size='small' onClick={() => {
                                        toUrl2(row)
                                    }}>{t('信用卡产品')}</Button>

                                    <Button type="text" className='cursor-pointer _primary' size='small' onClick={() => {
                                        setActive(row)
                                        queryFee(row)
                                    }}>{t('费率')}</Button>
                                    <TenantLimitButton row={row} />

                                    {/* 
                                    增加一个组件，组件放在当前目录的 components目录，用于设置USAFE 限额 组件里有一个按钮，点击后打开一个模态框，
                                    这个模态框里有
                                    appId 是 row.appid  传入
                                    groupName  是代理名称  row.companyName 传入
                                    groupType  = TENANT 固定
                                    singleLimit  传入单笔限额 数字 不能小于0
                                    singleLimitCount 传入单笔限额次数 数字  不能小于0
                                    totalLimit 传入总限额 数字 不能小于0

                                    两个按钮  确认和取消
                                    确认后调用接口 tenantLimit.index.ts  有创建和更新 和 获取
                                    打开弹窗是 调用 get 传入 row.appid

                                    如果有数据， 确认调用更新，没数据 调用创建

                                    
                                    */}
                                </>
                            ),
                        },
                    ]}
                    dataSource={data}
                    pagination={{
                        hideOnSinglePage: true,
                        pageSize: 10,
                        current,
                        total,
                        onChange: pageChange,
                        position: ['bottomRight']
                    }}
                />
            </Card>
            <Modal
                maskClosable={false}
                keyboard={false}
                title={activeData?.id ? t("修改组织信息") : t("新增组织")}
                open={open}
                onOk={addAccount}
                onCancel={cancel} 
                centered
                okText={t('确认')}
                destroyOnClose={true}
                cancelText={t("取消")}
            >
                <Form
                    ref={formRef}
                    name="form"
                    layout="horizontal"
                    autoComplete="off"
                    labelCol={{ flex: '150px' }}
                    labelWrap
                    wrapperCol={{ flex: 1 }}
                    initialValues={{ roleType: '1' }}  // 设置初始值
                    defaultValue={{ ...activeData }}
                    requiredMark={false}
                >
                    <Form.Item
                        label={t('组织名称')}
                        name="companyName"
                        rules={[
                            { required: true, message: t('请输入组织名称') },
                        ]}
                    >
                        <Input maxLength={20} />
                    </Form.Item>
                    <Form.Item
                        label={t('组织ID')}
                        name="appid"
                        rules={[]}
                    >
                        {activeData?.appid || '-'}
                    </Form.Item>
                    <Form.Item
                        label={t('地址')}
                        name="companyAddress"
                        rules={[
                            { required: true, message: t('请输入地址') },
                        ]}
                    >
                        <Input maxLength={200} />
                    </Form.Item>
                    <Form.Item
                        label={t('组织电话')}
                        name="companyPhone"
                        rules={[
                            { required: true, message: t('请输入电话') },
                        ]}
                    >
                        <Input maxLength={18} />
                    </Form.Item>
                    <Form.Item
                        label={t('组织编号')}
                        name="tanantAccountNumber"
                        rules={[
                            {
                                required: true,
                                validator(_, value) {//message: t('组织编号') }
                                    if (!value) {
                                        return Promise.reject(new Error(t('组织编号')));
                                    }
                                    if (value && `${value}`.length == 3) {
                                        return Promise.resolve()
                                    }
                                    return Promise.reject(new Error(t('自定义编号长度必须为3')));
                                },
                            },
                        ]}
                    >
                        <Input prefix='168-' maxLength={3} disabled={activeData?.id} />
                    </Form.Item>
                    <Form.Item
                        label={t('角色')}
                        name="roleType"
                        initialValue={'1'}
                        rules={[]}
                    >
                        <Radio.Group disabled={activeData?.id}>
                            {
                                Object.keys(SYS_ROLE_ENUM()).map((v: any) => {
                                    return <Radio value={v}>{SYS_ROLE_ENUM()[v]}</Radio>
                                })
                            }
                        </Radio.Group>
                    </Form.Item>
                    <Form.Item
                        label={t('卡账户转账户')}
                        name="withdrawSwitch"
                        rules={[
                            { required: true, message: t('卡账户转账户') },
                        ]}
                    >
                        <Switch />
                    </Form.Item>

                    <Form.Item
                        label={t('卡账户转卡账户')}
                        name="transferSwitch"
                        rules={[
                            { required: true, message: t('卡账户转卡账户') },
                        ]}
                    >
                        <Switch />
                    </Form.Item>

                    <Form.Item
                        label={t('短信开关')}
                        name="smsSwitch"
                        rules={[
                            { required: true, message: t('短信开关') },
                        ]}
                    >
                        <Switch />
                    </Form.Item>
                    <Form.Item
                        label={t('VP开关')}
                        name="vpSwitch"
                    >
                        <Switch />
                    </Form.Item>
                    <Form.Item
                        label={t('主账户结余通知阈值')}
                        name="minBalanceThreshold"
                    >
                        <InputNumber suffix="USD" min={0} precision={0} style={{ width: '100%' }}/>
                    </Form.Item>
                </Form >
            </Modal >
            <Modal
                maskClosable={false}
                keyboard={false}
                title={t("费率")}
                open={openFee}
                onOk={updataFee}
                onCancel={cancel} 
                centered
                width={800}
                {...(userStore?.user?.user?.roleType !== "0") ? { footer: null } : {}}
                okText={t('修改当前页')}
                destroyOnClose={true}
                confirmLoading={loading}
                cancelText={t("关闭")}
            >
                <Table
                    dataSource={activeFeeList}
                    pagination={false}
                    columns={[
                        {
                            title: <div className='pl-[5px]'>{t('费率类型')}</div>,
                            dataIndex: 'id',
                            className: 'p-0 pl-[5px]',
                            key: 'id',
                            render: (_, row: any) => {
                                return EVENT_FEEENUM()[row.event]
                            }
                        },
                        {
                            title: t('百分比'),
                            width: 150,
                            className: 'pl-[5px]',
                            dataIndex: 'multiplicationFactor',
                            key: 'multiplicationFactor',
                            render: (_, row: any) => {
                                return <InputNumber
                                    className='w-full'
                                    min={0}
                                    max={100}
                                    value={parseFloat((row?.multiplicationFactor * 100).toFixed(4))}
                                    controls={false}
                                    suffix={"%"}
                                    onChange={(e: any) => {
                                        setActiveFeeList((list: any) => {
                                            return list.map((t: any) => {
                                                if (t.id == row.id) {
                                                    return {
                                                        ...t,
                                                        multiplicationFactor: parseFloat((e / 100).toFixed(4))
                                                    }
                                                }
                                                return t
                                            })
                                        })
                                    }}
                                    maxLength={20} />
                            }
                        },
                        {
                            title: t('固定金额'),
                            dataIndex: 'fixedAmount',
                            key: 'fixedAmount',
                            width: 150,
                            className: 'pl-[5px]',
                            render: (_, row: any) => {
                                return <InputNumber
                                    className='w-full'
                                    min={0}
                                    controls={false}
                                    value={row?.fixedAmount}
                                    maxLength={20}
                                    onChange={(e: any) => {
                                        setActiveFeeList((list: any) => {
                                            return list.map((t: any) => {
                                                if (t.id == row.id) {
                                                    return {
                                                        ...t,
                                                        fixedAmount: e
                                                    }
                                                }
                                                return t
                                            })
                                        })
                                    }}
                                />
                            }
                        },
                        {
                            title: t('最低收费'),
                            className: 'pl-[5px]',
                            width: 150,
                            dataIndex: 'minimumCharge',
                            key: 'minimumCharge',
                            render: (_, row: any) => {
                                return <InputNumber
                                    className='w-full'
                                    min={0}
                                    controls={false}
                                    value={row?.minimumCharge}
                                    maxLength={20}
                                    onChange={(e: any) => {
                                        setActiveFeeList((list: any) => {
                                            return list.map((t: any) => {
                                                if (t.id == row.id) {
                                                    return {
                                                        ...t,
                                                        minimumCharge: e
                                                    }
                                                }
                                                return t
                                            })
                                        })
                                    }}
                                />
                            }
                        },
                        {
                            title: t('货币'),
                            className: 'pl-[5px]',
                            width: 150,
                            dataIndex: 'currency',
                            key: 'currency',
                            render: (_, row: any) => {
                                return <div className='w-full'>
                                    {row.currency}
                                </div>
                            }
                        },
                        {
                            title: t('备注'),
                            dataIndex: 'remark',
                            className: 'pl-[5px]',
                            key: 'remark',
                            render: (_, row: any) => {
                                return <Input
                                    onChange={(e: any) => {
                                        setActiveFeeList((list: any) => {
                                            return list.map((t: any) => {
                                                if (t.id == row.id) {
                                                    return {
                                                        ...t,
                                                        remark: e?.target?.value
                                                    }
                                                }
                                                return t
                                            })
                                        })
                                    }}
                                />
                            }
                        },
                    ]} />

            </Modal>
        </div >
    );
};
