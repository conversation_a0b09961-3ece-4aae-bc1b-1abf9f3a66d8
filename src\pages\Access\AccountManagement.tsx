import {
  Card,
  Flex,
  Table,
  Input,
  message,
  Space,
  Button,
  Modal,
  Form,
  Select,
  DatePicker,
} from "antd";
import AccountLimitModal from "@/components/AccountLimitModal/AccountLimitModal";
import { SearchOutlined, CopyOutlined } from "@ant-design/icons";
import { PageHeader } from "@/components";
import { useTranslation } from "react-i18next";
import { useEffect, useRef, useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  getAccountList,
  postAccountAdd,
  postAccountUpdate,
} from "@/api/account";
import { getOrganiz } from "@/api/sys";
import { DebounceSelect } from "@/components/DebounceSelect/Index";
import { OrganizationSelect } from '@/components/OrganizationSelect';
import type { AxiosResponse } from "axios";

const { RangePicker } = DatePicker;

interface SearchParams {
  appid?: string;
  accountName?: string;
  id?: string;
  tenantAccountNumber?: string;
  beginTime?: string;
  endTime?: string;
}

interface ApiResponse<T = any> extends Partial<AxiosResponse<T>> {
  code: number;
  data: T;
  message?: string;
}

export const AccountManagementPage = () => {
  const { t } = useTranslation();
  const [current, setCurrent] = useState<number>(1);
  const [total, setTotal] = useState<number>(0);
  const loadingRef = useRef(true);
  const [loading, setLoading] = useState(false);
  const [open, setOpen] = useState(false);
  const [data, setData] = useState([]);
  const [activeData, setActiveData] = useState<any>({});
  const navigate = useNavigate();
  const [downloadShow, setDownloadShow] = useState(false);
  const [value, setValue] = useState<any>("");
  const [defaultList, setDefaultList] = useState([]);
  const formRef: any = useRef();
  const [submitLoading, setSubmitLoading] = useState(false);
  const [searchParams, setSearchParams] = useState<SearchParams>({});
  const [dateRange, setDateRange] = useState<[string, string]>(['', '']);
  const [limitModalVisible, setLimitModalVisible] = useState(false);
  const [currentAccountId, setCurrentAccountId] = useState('');

  useEffect(() => {
    if (loadingRef.current) {
      loadingRef.current = false;
      query();
      qyeryDefaultList();
    }
  }, [current]);

  const pageChange = (page: number) => {
    setCurrent(page);
  };

  const query = () => {
    if (!loading) {
      setLoading(true);
      getAccountList({
        pageNum: current,
        pageSize: 10,
        ...searchParams,
        beginTime: dateRange[0] ? new Date(dateRange[0]).getTime() : undefined,
        endTime: dateRange[1] ? new Date(dateRange[1]).getTime() : undefined,
      }).then((response) => {
        const res = response as unknown as ApiResponse;
        if (res.code === 200) {
          setTotal(res.data?.total);
          setData(res.data?.list || []);
        }
      })
        .catch((err) => {
          console.error(err);
        })
        .finally(() => {
          setLoading(false);
          loadingRef.current = true;
        });
    }
  };

  const add = () => {
    setOpen(true);
  };

  async function fetchUserList(username?: string): Promise<any> {
    // console.log('fetching user', username);
    return new Promise((resolve) => {
      getOrganiz({
        pageNum: 1,
        pageSize: 20,
        companyName: username || undefined,
      }).then((res: any) => {
        if (res.code == 200) {
          const list = res.data?.list.map((v: any) => {
            return {
              value: v.appid,
              label: v.companyName,
            };
          });
          resolve(list);
          return;
        }
        resolve([]);
      });
    });
  }

  const qyeryDefaultList = async () => {
    const list = await fetchUserList();
    setDefaultList(list);
  };

  const addAccount = () => {
    let api = postAccountAdd;
    if (activeData.id) api = postAccountUpdate;
    formRef.current?.validateFields().then((values: any) => {
      setSubmitLoading(true);
      api({
        appid: values["appid"].value,
        // exchangeRateBuy: values['exchangeRateBuy'],
        // exchangeRateSell: values['exchangeRateSell'],
        currency: "USD",
        accountName: values.accountName,
        assets: ["USD"],
        id: activeData?.id || undefined,
      })
        .then((res: any) => {
          const { code } = res;
          if (code == 200) {
            message.open({
              type: "success",
              content: activeData.id ? t("修改成功") : t("添加成功"),
            });
            close();
            query();
            return;
          }
          message.open({
            type: "error",
            content: t(res.message),
          });
        })
        .finally(() => setSubmitLoading(false));
    });
  };

  const download = () => {
    close();
  };
  const close = () => {
    setOpen(false);
    setDownloadShow(false);
    setActiveData({});
  };

  const uploadData = (row: any) => {
    setActiveData(row);
    setOpen(true);
    setTimeout(() => {
      formRef.current?.setFieldsValue({ ...row });
    }, 30);
  };

  const goDetails = (row: any) => {
    navigate("/acccess/account-management/details", { state: { id: row.id } });
  };

  return (
    <>
      <div className="relative">
      <PageHeader title={t("账户管理")} breadcrumbs={[]} />
      <Button
        onClick={() => setDownloadShow(true)}
        shape="round"
        disabled
        style={{
          backgroundImage:
            "linear-gradient(90deg, rgb(243, 227, 180), rgb(227, 179, 84))",
        }}
        className="absolute right-[20px] top-[10px]"
      >
        {" "}
        {t("下载交易报告")}
      </Button>
      <Card className="mt-[20px]">
        <Flex wrap="wrap" gap="small" className="mb-[20px]">
          <OrganizationSelect
            allowClear
            className="w-[200px]"
            placeholder={t("请选择组织")}
            onChange={(value) => {
              setSearchParams(prev => ({ ...prev, appid: value || undefined }));
            }}
          />
          <Input
            allowClear
            className="w-[200px]"
            placeholder={t("请输入账户名称")}
            value={searchParams.accountName}
            onChange={(e) => {
              setSearchParams(prev => ({ ...prev, accountName: e.target.value }));
            }}
            onPressEnter={()=>{query()}}
          />
          <Input
            allowClear
            className="w-[200px]"
            placeholder={t("请输入账户ID")}
            value={searchParams.id}
            onChange={(e) => {
              setSearchParams(prev => ({ ...prev, id: e.target.value }));
            }}
            onPressEnter={()=>{query()}}
          />
          <Input
            allowClear
            className="w-[200px]"
            placeholder={t("请输入账户号码")}
            value={searchParams.tenantAccountNumber}
            onChange={(e) => {
              setSearchParams(prev => ({ ...prev, tenantAccountNumber: e.target.value }));
            }}
            onPressEnter={()=>{query()}}
          />
          <RangePicker
            showTime
            allowClear
            className="w-[300px]"
            onChange={(_, dateStrings) => {
              setDateRange(dateStrings as [string, string]);
            }}
          />
          <Flex className="ml-auto gap-[10px]">
            <Button type="primary" onClick={() => query()}>
              {t("搜索")}
            </Button>
            <Button onClick={add} type="primary">
              {t("新增账户")}
            </Button>
          </Flex>
        </Flex>
        <Table
          loading={loading}
          rowKey={(row: any) => row.id}
          className="mt-[20px]"
          columns={[
            {
              title: t("组织名称"),
              dataIndex: "companyName",
              key: "companyName",
            },
            {
              title: t("账户名称"),
              dataIndex: "accountName",
              key: "accountName",
              render: (text) => (
                <span>
                  {text}
                  <CopyOutlined
                    className="ml-2 cursor-pointer text-blue-500"
                    onClick={() => {
                      console.log(text)
                      setSearchParams(prev => ({ ...prev, accountName: text }));
                    }}
                  />
                </span>
              ),
            },
            {
              title: t("账户ID"),
              dataIndex: "id",
              key: "id",
              render: (text) => (
                <span>
                  {text}
                  <CopyOutlined
                    className="ml-2 cursor-pointer text-blue-500"
                    onClick={() => {
                      setSearchParams(prev => ({ ...prev, id: text }));
                    }}
                  />
                </span>
              ),
            },
            {
              title: t("账户号码"),
              dataIndex: "tenantAccountNumber",
              key: "tenantAccountNumber",
              render: (text) => (
                <span>
                  {text}
                  <CopyOutlined
                    className="ml-2 cursor-pointer text-blue-500"
                    onClick={() => {
                      setSearchParams(prev => ({ ...prev, tenantAccountNumber: text }));
                    }}
                  />
                </span>
              ),
            },
            {
              title: t("价值"),
              dataIndex: "tenantAccountAssets",
              key: "tenantAccountAssets",
              render: (_, row: any) => {
                const d =
                  row?.tenantAccountAssets
                    ?.filter((v: any) => v?.asset == "USD")
                    ?.map((v: any) => v?.balanceAmount) || "0";
                return (d && d?.length && d) || "0";
              },
            },
            {
              title: t("操作"),
              key: "action",
              render: (_, record) => (
                <Space size="middle">
                  <Button
                    type="text"
                    className="_primary"
                    onClick={goDetails.bind(this, record)}
                  >
                    {t("详情")}{" "}
                  </Button>
                  <Button
                    type="text"
                    className="_primary"
                    onClick={uploadData.bind(this, record)}
                  >
                    {t("修改")}{" "}
                  </Button>
                  <Button
                    type="text"
                    className="_primary"
                    onClick={() => {
                      setCurrentAccountId(record.id);
                      setLimitModalVisible(true);
                    }}
                  >
                    {t("USAFE 账户限额设置")}
                  </Button>
                </Space>
              ),
            },
          ]}
          dataSource={data}
          pagination={{
            hideOnSinglePage: true,
            showSizeChanger: false,
            pageSize: 10,
            current,
            total,
            onChange: pageChange,
            position: ["bottomRight"],
          }}
        />
      </Card>
      <Modal
        maskClosable={false}
        keyboard={false}
        title={t("下载交易报告")}
        open={downloadShow}
        className="top-[30vh]"
        width={400}
        onCancel={close}
        destroyOnClose={true}
        footer={null}
      >
        <div className="text-[14px] font-bold mb-[10px]">
          {t("您可以选择日期范围或下载所有日期范围")}
        </div>
        <Form layout="vertical">
          <Form.Item
            label={<span className="text-[12px]">{t("账户")}</span>}
            name="coin"
          >
            <Select
              placeholder={t("选择账户")}
              options={[
                { label: "USDT", value: "USDT" },
                { label: "USD", value: "USD" },
              ]}
            />
          </Form.Item>
          <Form.Item
            label={<span className="text-[12px]">{t("日期范围")}</span>}
            name="account"
          >
            <RangePicker className="w-full" />
          </Form.Item>
        </Form>
        <Button
          className="w-full"
          shape="round"
          onClick={download}
          type="primary"
        >
          {t("下载")}
        </Button>
      </Modal>
      <Modal
        maskClosable={false}
        keyboard={false}
        title={t("新建账户")}
        open={open}
        onCancel={close}
        centered
        footer={null}
        okText={t("确认")}
        destroyOnClose={true}
        cancelText={t("取消")}
      >
        <Form
          name="sign-up-form"
          layout="horizontal"
          labelCol={{ span: 24 }}
          wrapperCol={{ span: 24 }}
          autoComplete="off"
          ref={formRef}
          requiredMark={false}
        >
          <Form.Item
            label={t("组织")}
            name="appid"
            rules={[{ required: true, message: t("请选择组织") }]}
          >
            <DebounceSelect
              value={value}
              disabled={activeData.id}
              placeholder=""
              defaultList={defaultList}
              fetchOptions={fetchUserList}
              onChange={(newValue) => {
                setValue(newValue);
              }}
              style={{ width: "100%" }}
            />
          </Form.Item>
          {/* <Form.Item
            label={t('货币')}
            name="currency"
            rules={[
              { required: true, message: t('请选择货币') },
            ]}
          >
            <Select defaultValue={'USD'} options={[
              { label: 'USD', value: 'USD' }
            ]} />
          </Form.Item> */}
          <Form.Item
            label={t("账户名称")}
            name="accountName"
            rules={[{ required: true, message: t("请输入账户名称") }]}
          >
            <Input />
          </Form.Item>
          {/* <Form.Item
            label={t('卖出费率')}
            name="exchangeRateSell"
          >
            <Input />
          </Form.Item>
          <Form.Item
            label={t('卖入费率')}
            name="exchangeRateBuy"
          >
            <Input />
          </Form.Item> */}
          <Flex>
            <Button className="w-full mr-[5px]" shape="round" onClick={close}>
              {t("取消")}
            </Button>
            <Button
              className="w-full ml-[5px]"
              shape="round"
              type="primary"
              loading={submitLoading}
              onClick={addAccount}
            >
              {t("确认")}
            </Button>
          </Flex>
        </Form>
      </Modal>
      </div>
      <AccountLimitModal
        visible={limitModalVisible}
        onCancel={() => setLimitModalVisible(false)}
        accountId={currentAccountId}
      />
    </>
  );
};
