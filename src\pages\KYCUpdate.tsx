import {
  Card,
  Flex,
  Table,
  Input,
  Space,
  Button,
  Select,
  DatePicker,
  Modal,
  Descriptions,
  message,
} from "antd";
const { RangePicker } = DatePicker;

import { PageHeader } from "@/components";
import { useTranslation } from "react-i18next";
import { useEffect, useRef, useState } from "react";
import { kycEventTodoListpage } from "@/api/kycEventTodo";
import { postKYCDetails, poseKYCIdentityUpdate } from "@/api/kyc";
import { useSelector } from "react-redux";

import { OrganizationSelect } from "@/components/OrganizationSelect";
import { KYCForm, type KYCFormRef } from "@/components/KYCForm";

interface SearchParams {
  appid?: number;
  kycInfoId?: string;
  eventType?: number;
  status?: number;
  processedRole?: string;
  beginTime?: number;
  endTime?: number;
}

export const KYCUpdatePage = () => {
  const { t } = useTranslation();
  const [current, setCurrent] = useState<number>(1);
  const [total, setTotal] = useState<number>(0);
  const loadingRef = useRef(true);
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState([]);
  const userStore = useSelector((state: any) => {
    return state.user;
  });
  const [searchParams, setSearchParams] = useState<SearchParams>({
    status: 1 // 默认为待处理状态
  });
  const [dateRange, setDateRange]: any = useState([]);
  const [detailModalOpen, setDetailModalOpen] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<any>(null);
  const [updateModalOpen, setUpdateModalOpen] = useState(false);
  const [kycData, setKycData] = useState<any>(null);
  const [updateLoading, setUpdateLoading] = useState(false);
  const kycFormRef = useRef<KYCFormRef>(null);
  const [dateOfBirth, setDateOfBirth] = useState<string>();
  const [expiryDate, setExpiryDate] = useState<string>();
  const [frontImage, setFrontImage] = useState<string>();
  const [backImage, setBackImage] = useState<string>();

  useEffect(() => {
    if (loadingRef.current) {
      loadingRef.current = false;
      query();
    }
  }, [current]);

  const pageChange = (page: number) => {
    setCurrent(page);
  };

  const query = () => {
    setLoading(true);

    // 过滤掉undefined和null的参数
    const filteredParams = Object.fromEntries(
      Object.entries(searchParams).filter(([_, value]) => value !== undefined && value !== null && value !== '')
    );

    kycEventTodoListpage({
      pageNum: current,
      pageSize: 10,
      ...filteredParams,
      beginTime: new Date(dateRange[0])?.getTime() || undefined,
      endTime: new Date(dateRange[1])?.getTime() || undefined,
    })
      .then((res: any) => {
        if (res.code === 200) {
          setTotal(res.data?.total);
          setData(res.data?.list || []);
        }
      })
      .catch((err) => {
        console.error(err);
      })
      .finally(() => {
        setLoading(false);
        loadingRef.current = true;
      });
  };

  const handleDetails = (record: any) => {
    setSelectedRecord(record);
    setDetailModalOpen(true);
  };

  const handleCloseModal = () => {
    setDetailModalOpen(false);
    setSelectedRecord(null);
  };

  const formatTimestamp = (timestamp: number) => {
    return timestamp ? new Date(timestamp).toLocaleString() : "-";
  };

  const getEventTypeText = (eventType: number) => {
    return eventType === 1 ? t("KYC更新") : eventType;
  };

  const getStatusText = (status: number) => {
    const statusMap: { [key: number]: string } = {
      1: t("待处理"),
      2: t("已完成"),
    };
    return statusMap[status] || status;
  };

  const getProcessedRoleText = (role: string) => {
    const roleMap: { [key: string]: string } = {
      user: t("用户"),
      admin: t("管理员"),
      system: t("系统"),
    };
    return roleMap[role] || role || "-";
  };

  const handleUpdateKYC = (record: any) => {
    if (!record.kycInfoId) {
      message.error(t("KYC资料ID不存在"));
      return;
    }

    setUpdateLoading(true);
    postKYCDetails({ id: record.kycInfoId })
      .then((res: any) => {
        if (res.code === 200) {
          const kycInfo = res.data.infoVo;
          setKycData({
            ...kycInfo,
            frontImage: res.data.documentFront?.data,
            backImage: res.data.documentBack?.data,
          });
          setFrontImage(res.data.documentFront?.data);
          setBackImage(res.data.documentBack?.data);
          setDateOfBirth(kycInfo.dateOfBirth);
          setExpiryDate(kycInfo.expiryDate);
          setUpdateModalOpen(true);
        } else {
          message.error(t("获取KYC详情失败"));
        }
      })
      .catch((err) => {
        console.error(err);
        message.error(t("获取KYC详情失败"));
      })
      .finally(() => {
        setUpdateLoading(false);
      });
  };

  const handleCloseUpdateModal = () => {
    setUpdateModalOpen(false);
    setKycData(null);
    setFrontImage(undefined);
    setBackImage(undefined);
    setDateOfBirth(undefined);
    setExpiryDate(undefined);
    kycFormRef.current?.resetFields();
  };

  const handleSubmitUpdate = () => {
    kycFormRef.current?.validateFields().then((values: any) => {
      setUpdateLoading(true);

      const submitData = {
        id: kycData.id,
        identityType: "individual", // 固定参数
        accountType: "prepaid",     // 固定参数
        firstName: values.firstName,
        lastName: values.lastName,
        firstNameLocal: values.firstNameLocal,
        lastNameLocal: values.lastNameLocal,
        email: values.email,
        dateOfBirth,
        countryCode: values.num1?.replace('+', ''),
        mobileNumber: values.num2,
        annualIncome: values.annualIncome,
        occupation: values.occupation,
        position: values.position,
        document: {
          front: frontImage,
          back: backImage,
          type: values.type,
          number: values.cardNumber,
          country: values.country,
          address: values.address,
          expiryDate,
        },
      };

      poseKYCIdentityUpdate(submitData)
        .then((res: any) => {
          if (res.code === 200) {
            message.success(t("KYC更新成功"));
            handleCloseUpdateModal();
            query(); // 刷新列表
          } else {
            message.error(t(res.message || "更新失败"));
          }
        })
        .catch((err) => {
          console.error(err);
          message.error(t("更新失败"));
        })
        .finally(() => {
          setUpdateLoading(false);
        });
    });
  };

  return (
    <div className=" relative">
      <PageHeader title={t("KYC 更新")} breadcrumbs={[]} />
      <Card className="mt-[20px]">
        <Flex wrap="wrap" gap="small" className="mb-[20px]">
          <OrganizationSelect
            allowClear
            className="w-[200px]"
            placeholder={t("请选择组织")}
            onChange={(value) => {
              setSearchParams((prev) => ({
                ...prev,
                appid: value ? Number(value) : undefined
              }));
            }}
          />
          <Input
            allowClear
            className="w-[200px]"
            placeholder={t("请输入KYC资料ID")}
            onChange={(e) => {
              setSearchParams((prev) => ({
                ...prev,
                kycInfoId: e.target.value,
              }));
            }}
            onPressEnter={() => query()}
          />
          <Select
            allowClear
            className="w-[200px]"
            placeholder={t("请选择事件类型")}
            onChange={(value) => {
              setSearchParams((prev) => ({ ...prev, eventType: value }));
            }}
            options={[{ label: t("KYC更新"), value: 1 }]}
          />
          <Select
            allowClear
            className="w-[200px]"
            placeholder={t("请选择状态")}
            value={searchParams.status}
            onChange={(value) => {
              setSearchParams((prev) => ({ ...prev, status: value }));
            }}
            options={[
              { label: t("待处理"), value: 1 },
              { label: t("已完成"), value: 2 },
            ]}
          />
          {/* <Select
            allowClear
            className="w-[200px]"
            placeholder={t("请选择处理角色")}
            onChange={(value) => {
              setSearchParams((prev) => ({ ...prev, processedRole: value }));
            }}
            options={[
              { label: t("用户"), value: "user" },
              { label: t("管理员"), value: "admin" },
              { label: t("系统"), value: "system" },
            ]}
          /> */}
          <RangePicker
            showTime
            allowClear
            className="w-[300px]"
            onChange={(_, dateStrings) => {
              setDateRange(dateStrings);
            }}
          />
          <Flex className="ml-auto gap-[10px]">
            <Button type="primary" onClick={() => query()}>
              {t("搜索")}
            </Button>
          </Flex>
        </Flex>
        <Table
          loading={loading}
          rowKey={(row: any) => row.id}
          scroll={{ x: 1500 }}
          className="mt-[20px]"
          columns={[
            {
              title: t("ID"),
              dataIndex: "id",
              key: "id",
              width: 100,
            },
            {
              title: t("事件类型"),
              dataIndex: "eventType",
              key: "eventType",
              width: 100,
              render: (value: number) => {
                return value === 1 ? t("KYC更新") : value;
              },
            },
            {
              title: t("KYC资料ID"),
              dataIndex: "kycInfoId",
              key: "kycInfoId",
              ellipsis: true,
              width: 120,
            },
            // {
            //   title: t("应用ID"),
            //   dataIndex: "appid",
            //   key: "appid",
            //   width: 100,
            // },

            {
              title: t("创建时间"),
              dataIndex: "createTime",
              key: "createTime",
              width: 160,
              render: (timestamp: number) => {
                return timestamp ? new Date(timestamp).toLocaleString() : "-";
              },
            },
            {
              title: t("处理角色"),
              dataIndex: "processedRole",
              key: "processedRole",
              width: 100,
              render: (role: string) => {
                const roleMap: { [key: string]: string } = {
                  user: t("用户"),
                  admin: t("管理员"),
                  system: t("系统"),
                };
                return roleMap[role] || role || "-";
              },
            },
            {
              title: t("处理人"),
              dataIndex: "processedUserName",
              key: "processedUserName",
              ellipsis: true,
              width: 120,
              render: (text: string) => text || "-",
            },
            {
              title: t("处理时间"),
              dataIndex: "processedAt",
              key: "processedAt",
              width: 160,
              render: (timestamp: number) => {
                return timestamp ? new Date(timestamp).toLocaleString() : "-";
              },
            },
            {
              title: t("处理备注"),
              dataIndex: "processedRemark",
              key: "processedRemark",
              ellipsis: true,
              width: 150,
              render: (text: string) => text || "-",
            },
            {
              title: t("描述"),
              dataIndex: "description",
              key: "description",
              ellipsis: true,
              width: 200,
              render: (text: string) => text || "-",
            },
            {
              title: t("状态"),
              dataIndex: "status",
              key: "status",
              width: 100,
              render: (value: number) => {
                const statusMap: { [key: number]: string } = {
                  1: t("待处理"),
                  2: t("已完成"),
                };
                return statusMap[value] || value;
              },
            },
            {
              title: t("操作"),
              key: "action",
              fixed: "right",
              width: 150,
              render: (_, record) => (
                <Space size="middle">
                  <Button
                    type="text"
                    className="_primary"
                    size="small"
                    onClick={() => handleDetails(record)}
                  >
                    {t("详情")}
                  </Button>
                  {record.status === 1 && (
                    <Button
                      type="text"
                      className="_primary"
                      size="small"
                      loading={updateLoading}
                      onClick={() => handleUpdateKYC(record)}
                    >
                      {t("更新KYC")}
                    </Button>
                  )}
                </Space>
              ),
            },
          ]}
          dataSource={data}
          pagination={{
            hideOnSinglePage: true,
            showSizeChanger: false,
            pageSize: 10,
            current,
            total,
            onChange: pageChange,
            position: ["bottomRight"],
          }}
        />
      </Card>

      {/* 详情弹窗 */}
      <Modal
        title={t("KYC更新事件详情")}
        open={detailModalOpen}
        onCancel={handleCloseModal}
        footer={[
          <Button key="close" onClick={handleCloseModal}>
            {t("关闭")}
          </Button>
        ]}
        width={800}
        centered
      >
        {selectedRecord && (
          <Descriptions
            bordered
            column={2}
            size="small"
            labelStyle={{ width: "120px", fontWeight: "bold" }}
          >
            <Descriptions.Item label={t("ID")} span={1}>
              {selectedRecord.id || "-"}
            </Descriptions.Item>
            <Descriptions.Item label={t("事件类型")} span={1}>
              {getEventTypeText(selectedRecord.eventType)}
            </Descriptions.Item>
            <Descriptions.Item label={t("KYC资料ID")} span={2}>
              {selectedRecord.kycInfoId || "-"}
            </Descriptions.Item>
            <Descriptions.Item label={t("状态")} span={1}>
              {getStatusText(selectedRecord.status)}
            </Descriptions.Item>
            <Descriptions.Item label={t("创建时间")} span={1}>
              {formatTimestamp(selectedRecord.createTime)}
            </Descriptions.Item>
            <Descriptions.Item label={t("处理角色")} span={1}>
              {getProcessedRoleText(selectedRecord.processedRole)}
            </Descriptions.Item>
            <Descriptions.Item label={t("处理人")} span={1}>
              {selectedRecord.processedBy || "-"}
            </Descriptions.Item>
            <Descriptions.Item label={t("处理时间")} span={2}>
              {formatTimestamp(selectedRecord.processedAt)}
            </Descriptions.Item>
            <Descriptions.Item label={t("处理备注")} span={2}>
              <div style={{ wordBreak: "break-word", whiteSpace: "pre-wrap" }}>
                {selectedRecord.processedRemark || "-"}
              </div>
            </Descriptions.Item>
            <Descriptions.Item label={t("描述")} span={2}>
              <div style={{ wordBreak: "break-word", whiteSpace: "pre-wrap" }}>
                {selectedRecord.description || "-"}
              </div>
            </Descriptions.Item>
          </Descriptions>
        )}
      </Modal>

      {/* KYC更新弹窗 */}
      <Modal
        title={t("更新KYC信息")}
        open={updateModalOpen}
        onCancel={handleCloseUpdateModal}
        footer={[
          <Button key="cancel" onClick={handleCloseUpdateModal}>
            {t("取消")}
          </Button>,
          <Button
            key="submit"
            type="primary"
            loading={updateLoading}
            onClick={handleSubmitUpdate}
          >
            {t("提交更新")}
          </Button>
        ]}
        width={1000}
        centered
        destroyOnClose
      >
        {kycData && (
          <KYCForm
            ref={kycFormRef}
            initialData={kycData}
            showOrganization={false}
            onDateOfBirthChange={setDateOfBirth}
            onExpiryDateChange={setExpiryDate}
            onFrontImageChange={setFrontImage}
            onBackImageChange={setBackImage}
          />
        )}
      </Modal>
    </div>
  );
};
