import {
  Card, Flex, Table, Input, message,
  Space, Button, Modal, Form, Select,
  // DatePicker
} from 'antd';
import { SearchOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { PageHeader } from '@/components';
import { useTranslation } from 'react-i18next';
import { useEffect, useRef, useState } from 'react';
// import { useNavigate } from 'react-router-dom';
 
import {
  getLlist,
  postCreate,
  postRemove,
  // postStatus,
  postResume,
} from '@/api/webhook'
// import { DebounceSelect } from '@/components/DebounceSelect/Index'

// const { RangePicker } = DatePicker;

const WebhookEventEnum = [
  { value: '*', label: 'All' },
  { value: 'account.created', label: 'Account_Created' },
  { value: 'account.balance', label: 'Account_Balance' },
  { value: 'cardaccount.created', label: 'Cardaccount_Created' },
  { value: 'cardaccount.balance', label: 'Cardaccount_Balance' },
  { value: 'cardaccount.transaction.created', label: 'Cardaccount_Transaction_Created' },
  { value: 'cardaccount.transaction.updated', label: 'Cardaccount_Transaction_Updated' },
  { value: 'cardaccount.transaction.declined', label: 'Cardaccount_Transaction_Declined' },
  { value: 'cardaccount.transaction.verification-code-delivered', label: 'Cardaccount_Transaction_Verification_Code_Delivered' },
  { value: 'cardaccount.transaction.suspicious-activity-detected', label: 'cardaccount_transaction_suspicious_activity_detected' },
  { value: 'card.issued', label: 'Card_Issued' },
  { value: 'card.activated', label: 'Card_Activated' },
  { value: 'card.suspended', label: 'Card_Suspended' },
  { value: 'card.unsuspended', label: 'Card_Unsuspended' },
  { value: 'card.locked', label: 'Card_Locked' },
  { value: 'card.unlocked', label: 'Card_Unlocked' },
  { value: 'card.cancelled', label: 'Card_Cancelled' },
  { value: 'card.spending-controls-modified', label: 'card_spending_controls_modified' },
  { value: 'payout.created', label: 'Payout_Created' },
  { value: 'payout.accepted', label: 'Payout_Accepted' },
  { value: 'payout.completed', label: 'Payout_Completed' },
  { value: 'payout.cancelled', label: 'Payout_Cancelled' },
];

const { confirm } = Modal;

export interface webHookAddDto {
  name: string;
  url: string;
  events: string[];
}

export const WebHookPage = () => {
  const { t } = useTranslation()
  const [current, setCurrent] = useState<number>(1)
  // const [total, setTotal] = useState<number>(0)
  // const total = 10
  const loadingRef = useRef(true)
  const [loading, setLoading] = useState(false)
  const [open, setOpen] = useState(false);
  const [data, setData] = useState([])
  const [activeData, setActiveData] = useState<any>({})
  // const navigate = useNavigate();
  // const [downloadShow, setDownloadShow] = useState(false);
  // const [value, setValue] = useState<any>('');
  // const [defaultList, setDefaultList] = useState([])
  const formRef: any = useRef()
  const [submitLoading, setSubmitLoading] = useState(false)


  const [formData, setFormData] = useState<webHookAddDto>({
    name: '',
    url: '',
    events: []
  });

  const handleChange = (
    field: keyof webHookAddDto,
    value: any
  ) => {
    console.log("dd",value);
    setFormData((prevFormData) => ({ ...prevFormData, [field]: value }));
  };

  const handleFieldChange = (
    field: keyof webHookAddDto,
    event: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  )=>{
    const value = event.target.value;
    handleChange(field, value)
  }

  useEffect(() => {
    if (loadingRef.current) {
      loadingRef.current = false
      query()
      // qyeryDefaultList()
    }
    if (Object.keys(activeData).length > 0) {
      formRef.current?.setFieldsValue(activeData);
    }
  }, [current])

  const pageChange = (page: number) => {
    setCurrent(page)
  }

  const query = (value?: any, type?: string) => {
    if (!loading) {
      setLoading(true)
      getLlist({
        pageNum: current,
        pageSize: 10,
        name: type == 'search' ? value : undefined
      }).then((res: any) => {
        if (res.code == 200) {
          // setTotal(res.data?.total)
          setData(res.data?.webhooks || [])
        }
      }).catch(err => {
        console.log(err);
      }).finally(() => {
        setLoading(false)
        loadingRef.current = true
      })
    }
  }

  const add = () => {
    setOpen(true);
    formRef.current?.resetFields();
  }


  const addAccount = () => {
    const api = postCreate
 
    formRef.current?.validateFields().then(( ) => {
      console.log("aaa",formData);
      setSubmitLoading(true)
      api({
        ...formData
      }).then((res: any) => {
        const { code } = res
        if (code == 200) {
          message.open({
            type: 'success',
            content: activeData.id ? t('修改成功') : t('添加成功')
          })
          close()
          query()
          return
        }
        message.open({
          type: 'error',
          content: t(res.message)
        })
      }).finally(() => setSubmitLoading(false))
    })

  }

  const close = () => {
    setOpen(false);
    // setDownloadShow(false)
    setActiveData({});
    formRef.current?.resetFields();
  }

  const resume = (row: any) => {
    confirm({
      icon: <ExclamationCircleOutlined />,
      centered: true,
      content: <>
        {t("请确认是否重连")}
        <div>{row.id}</div>
      </>,
      onOk() {
        postResume({ webhookId: row.id }).then((res: any) => {
          const { code } = res
          if (code == 200) {
            message.open({
              type: 'success',
              content: t('成功')
            })
            query()
            return
          }
          message.open({
            type: 'error',
            content: t(res.message)
          })
        }).finally(() => setSubmitLoading(false))
      },
      onCancel() {
        console.log('Cancel');
      },
    });
  }
  const del = (row: any) => {
    confirm({
      icon: <ExclamationCircleOutlined />,
      centered: true,
      content: <>
        {t("请确认是否删除")}
        <div>{row.id}</div>
      </>,
      onOk() {
        postRemove({ webhookId: row.id }).then((res: any) => {
          const { code } = res
          if (code == 200) {
            message.open({
              type: 'success',
              content: t('成功')
            })
            query()
            return
          }
          message.open({
            type: 'error',
            content: t(res.message)
          })
        }).finally(() => setSubmitLoading(false))
      },
      onCancel() {
        console.log('Cancel');
      },
    });
  }



  return (
    <div className=' relative'>
      <PageHeader
        title={('WEBHOOK s')}
        breadcrumbs={[]}
      />
      <Card className='mt-[20px]'>
        <Flex justify='space-between'>
          <Input
            prefix={<SearchOutlined />}
            allowClear
            placeholder={t('请输入关键字进行搜索')}
            className='w-[30%] max-w-[300px]'
            onChange={(e: any) => query(e.target.value, 'search')}
          />
          <Button onClick={add} type='primary'> {t('新增Hook')}</Button>
        </Flex>
        <Table
          loading={loading}
          rowKey={(row: any) => row.id}
          scroll={{ x: 1000 }}
          className='mt-[20px]'
          columns={[
            {
              title: t('ID'),
              dataIndex: 'id',
              key: 'id',
              ellipsis: true
            },
            {
              title: t('名称'),
              dataIndex: 'name',
              key: 'name',
              ellipsis: true
            },
            {
              title: t('事件'),
              dataIndex: 'events',
              key: 'events',
              ellipsis: true
            },
            {
              title: t('url'),
              dataIndex: 'url',
              key: 'url',
              ellipsis: true
            },
            {
              title: t('狀態'),
              dataIndex: 'status',
              key: 'status',
              render: (_, row: any) => {
                return row.status
              }
            },
            {
              title: t('操作'),
              key: 'action',
              fixed: "right",
              render: (_, record) => (
                <Space size="middle">
                  <Button type="text" className='_primary' onClick={resume.bind(this, record)}>{t('重连')}  </Button>
                  <Button type="text" className='_primary' onClick={del.bind(this, record)}>{t('删除')}  </Button>
                </Space>
              ),
            },
          ]}
          dataSource={data}
          pagination={{
            // hideOnSinglePage: true,
            pageSize: 10,
            current,
            total: data.length + 1,
            simple: { readOnly: true },
            onChange: pageChange,
            position: ['bottomRight']
          }}
        />
      </Card>
      <Modal
        title={t("新增Hook")}
        open={open} 
        onCancel={close}
        centered
        footer={null}
        okText={t('确认')}
        destroyOnClose={true}
        cancelText={t("取消")}
      >
        <Form
          name="sign-up-form"
          layout="horizontal"
          labelCol={{ span: 24 }}
          wrapperCol={{ span: 24 }}
          autoComplete="off"
          ref={formRef}
          requiredMark={false}
        >
          <Form.Item
            label={t('名称')}
            name="name"
            rules={[
              // { required: true, message: t('名称') },
            ]}
          >  <Input
          value={formData.name} // 绑定 value 属性
          onChange={(val) => handleFieldChange("name", val )} // 使用 onChange 更新状态
            />

          </Form.Item>
          <Form.Item
            label={t('URL')}
            name="url"
            rules={[
              { required: true, message: t('URL') },
            ]}
          >
            <Input
                      value={formData.url} // 绑定 value 属性
                      onChange={(val) => handleFieldChange("url", val )} // 使用 onChange 更新状态
            />
          </Form.Item>
          <Form.Item
            label={t('事件')}
            name="events"
            rules={[
              { required: true, message: t('事件') },
            ]}
          >
            <Select
              mode="multiple"
              options={WebhookEventEnum}

              value={formData.events} // 绑定 value 属性
              onChange={(val) => handleChange("events", val)} // 使用 onChange 更新状态

            />
          </Form.Item>

          <Flex>
            <Button className='w-full mr-[5px]' shape='round'
              onClick={close}
            >{t('取消')}</Button>
            <Button className='w-full ml-[5px]' shape='round' type='primary' loading={submitLoading} onClick={addAccount}>{t('确认')}</Button>
          </Flex>
        </Form>
      </Modal>
    </div>
  );
};
