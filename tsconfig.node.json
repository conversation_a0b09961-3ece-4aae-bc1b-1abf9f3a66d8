{
  "compilerOptions": {
    "target": "ES2022",
    "composite": true,
    "lib": [
      "ES2023"
    ],
    "module": "ESNext",
    "skipLibCheck": true,
    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "isolatedModules": true,
    "moduleDetection": "force",
    "noEmit": true,
    /* Linting */
    "strict": false,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "noImplicitAny": false,
    "strictNullChecks": false,
    "allowSyntheticDefaultImports": true
  },
  "include": [
    "vite.config.ts",
    "src"
  ]
}