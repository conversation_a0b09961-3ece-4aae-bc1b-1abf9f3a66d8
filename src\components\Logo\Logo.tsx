import { Flex, FlexProps } from 'antd';
import { CSSProperties } from 'react';


type LogoProps = {
  color: CSSProperties['color'];
  imgSize?: {
    h?: number | string;
    w?: number | string;
  };
  asLink?: boolean;
  href?: string;
  bgColor?: CSSProperties['backgroundColor'];
} & Partial<FlexProps>;

export const Logo = ({
  asLink,
  color,
  href,
  imgSize,
  bgColor,
  ...others
}: LogoProps) => {


  return <Flex gap={others.gap || 'small'} align="center" {...others}>
    <img
      src="/admin/logo.png"
      alt="logo"
      className="h-[108px]"
    />
  </Flex>
};
