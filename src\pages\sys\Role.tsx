import { Tooltip, Table, Input, Modal, Form, Select, Card, } from 'antd';
// import { useStylesContext } from '@/context';
import {
    HomeOutlined,
    RocketOutlined,
    // SearchOutlined,
} from '@ant-design/icons';
import { PageHeader } from '@/components';
// import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useEffect, useRef, useState } from 'react';
// import { useNavigate } from 'react-router-dom';
// const { Title, Text } = Typography;

interface DataType {
    key: string;
    name: string;
    age: number;
    address: string;
    tags: string[];
}

export const RolePage = () => {
    // const context = useStylesContext();
    const { t } = useTranslation()
    const [current, setCurrent] = useState<number>(1)
    const [total, setTotal] = useState<number>(0)
    const loadingRef = useRef(true)
    const [loading, setLoading] = useState(false)
    const [open, setOpen] = useState(false);
    // const navigate = useNavigate();

    const data: DataType[] = [
        {
            key: '1',
            name: '<PERSON>',
            age: 32,
            address: 'New York No. 1 Lake Park',
            tags: ['nice', 'developer'],
        },
        {
            key: '2',
            name: 'Jim Green',
            age: 42,
            address: 'London No. 1 Lake Park',
            tags: ['loser'],
        },
        {
            key: '3',
            name: 'Joe Black',
            age: 32,
            address: 'Sydney No. 1 Lake Park',
            tags: ['cool', 'teacher'],
        },
    ];




    useEffect(() => {
        if (loadingRef.current) {
            loadingRef.current = false
            query()
        }
    }, [])
    const pageChange = (page: number, pageSize: number) => {
        console.log(pageSize);

        setCurrent(page)
    }

    const query = (value?: any, type?: string) => {
        if (!loading) {
            console.log(value, type);

            setTotal(1)
            setLoading(true)
            setTimeout(() => {
                setLoading(false)
            }, 1000)
        }
    }


    const cancel = () => {
        setOpen(false);
    }
    const injection = () => {
        cancel()

    }

    return (
        <div>
            <PageHeader
                title=""
                breadcrumbs={[
                    {
                        title: (
                            <>
                                <HomeOutlined />
                                <span>{t('角色管理')}</span>
                            </>
                        ),

                    },
                ]}
            />
            <Card className='mt-[20px]'>
                {/* <Flex justify='space-between'>
          <Input
            prefix={<SearchOutlined />}
            allowClear
            placeholder={t('请输入关键字进行搜索')}
            className='w-[30%] max-w-[300px]'
            onChange={(e: any) => query(e.target.value, 'search')}
          />
          <Button onClick={add} type='primary'> {t('新增账户')}</Button>
        </Flex> */}
                <Table
                    loading={loading}
                    className='mt-[20px]'
                    columns={[
                        {
                            title: t('账户名称'),
                            dataIndex: 'name',
                            key: 'name',
                            render: (text) => <a>{text}</a>,
                        },
                        {
                            title: t('账户号码'),
                            dataIndex: 'age',
                            key: 'age',
                        },
                        {
                            title: t('价值'),
                            dataIndex: 'address',
                            key: 'address',
                        },
                        {
                            title: t('操作'),
                            key: 'action',
                            render: () => (//t('操作')
                                <Tooltip title={t('注入')} >
                                    <span className=' cursor-pointer ' onClick={() => setOpen(true)}><RocketOutlined /></span>
                                </Tooltip>
                            ),
                        },
                    ]}
                    dataSource={data}
                    pagination={{
                        hideOnSinglePage: true,
                        pageSize: 10,
                        current,
                        total,
                        onChange: pageChange,
                        position: ['bottomRight']
                    }}
                />
            </Card>
            <Modal
                maskClosable={false}
                keyboard={false}
                title={t("入金")}
                open={open}
                onOk={injection}
                onCancel={cancel}
                okText={t('确认')}
                cancelText={t("取消")}
            >
                <Form
                    name="sign-up-form"
                    layout="vertical"
                    labelCol={{ span: 24 }}
                    wrapperCol={{ span: 24 }}
                    autoComplete="off"
                    requiredMark={false}
                >
                    <Form.Item
                        label={t('账户名称')}
                        name="account"
                        rules={[
                            { required: true, message: t('请输入账户名称') },
                        ]}
                    >
                        <Input />
                    </Form.Item>
                    <Form.Item
                        label={t('资产')}
                        name="coin"
                        rules={[
                            { required: true, message: t('请选择资产') },
                        ]}
                    >
                        <Select options={[
                            { label: 'USDT', value: 'USDT' },
                            { label: 'USD', value: 'USD' }
                        ]} />
                    </Form.Item>
                    <Form.Item
                        label={t('入金金额')}
                        name="account"
                        rules={[
                            { required: true, message: t('请输入入金金额') },
                        ]}
                    >
                        <Input />
                    </Form.Item>
                    <Form.Item
                        label={t('手续费')}
                        name="account"
                        rules={[
                            { required: true, message: t('请输入手续费') },
                        ]}
                    >
                        <Input />
                    </Form.Item>
                    <Form.Item
                        label={t('汇率')}
                        name="account"
                        rules={[
                            { required: true, message: t('汇率') },
                        ]}
                    >
                        <Input />
                    </Form.Item>
                    <Form.Item
                        label={t('备注')}
                        name="account"
                        rules={[
                            { required: true, message: t('备注') },
                        ]}
                    >
                        <Input.TextArea />
                    </Form.Item>
                </Form>
            </Modal>
        </div>
    );
};
