import moment from 'moment';
import {
  Tooltip, Table, Input, Modal, Form, Select, Card,
  message, Flex, Button, DatePicker
} from 'antd';
const { RangePicker } = DatePicker;
import {
  RocketOutlined,
} from '@ant-design/icons';
import { PageHeader } from '@/components';
import { useTranslation } from 'react-i18next';
import { useEffect, useState, useRef } from 'react';
import {   postDepositRecord } from '@/api/account'
import  tenantRevenueApi from '@/api/tenantRevenue'
import  tenantAccountApi from '@/api/tenantAccount'
import { OrganizationSelect } from '@/components/OrganizationSelect';
import { set } from 'lodash';

interface InjectionData {
  id: string;
  
  companyName: string;
  accountName: string;
  tenantAccountNumber: string;
  tenantAccountAssets: Array<{
    asset: string;
    balanceAmount: string;
  }>;
  createTime: string;
  revenueAccount: string; // 添加 revenueAccount 属性
}

export const TenantRevenuePage = () => {
  const { t } = useTranslation()
  const [current, setCurrent] = useState<number>(1)
  const [total, setTotal] = useState<number>(0)
  const [loading, setLoading] = useState(false)
  const [open, setOpen] = useState(false);
  const [data, setData] = useState<InjectionData[]>([])
  const formRef: any = useRef()
  const [activeData, setActiveData] = useState<InjectionData>({
    id: '',
    companyName: '',
    accountName: '',
    tenantAccountNumber: '',
    tenantAccountAssets: [],
    createTime: '',
    revenueAccount: '', // 初始化 revenueAccount 属性
  });
  const [submitLoading, setSubmitLoading] = useState<boolean>(false)
  const loadingRef = useRef(true)
  const [dateRange, setDateRange] = useState<[string, string]>(['', ''])
  const [accountName, setAccountName] = useState<string>('');
  const [appid, setAppid] = useState<string>('');
  const [accountNumber, setAccountNumber] = useState<string>('');
  const [revenueType, setRevenueType] = useState<string | undefined>(undefined);
  const [companyName, setCompanyName] = useState<string>('');
  const [accountId, setAccountId] = useState<string>(''); // 新增 accountId 状态
  const [createAccountModalVisible, setCreateAccountModalVisible] = useState(false);
  const [selectedCurrency, setSelectedCurrency] = useState<string | undefined>(undefined);
  const [assetAccountId, setAssetAccountId] = useState<string | undefined>(undefined);

  useEffect(() => {
    if (loadingRef.current) {
      loadingRef.current = false
      query()
    }
  }, [current])

  const pageChange = (page: number) => {
    setCurrent(page)
  }

  const query = (value?: any, type?: string) => {
    if (!loading) {
      setLoading(true)
      tenantRevenueApi.listpage({
        pageNum: current,
        pageSize: 10,
        appid: appid || undefined,
        accountName: accountName || undefined,
        tenantAccountNumber: accountNumber || undefined,
        beginTime: dateRange[0] ? new Date(dateRange[0])?.getTime() : undefined,
        endTime: dateRange[1] ? new Date(dateRange[1])?.getTime() : undefined,
        revenueType: revenueType || undefined,
        companyName: companyName || undefined,
        accountId: accountId || undefined, // 新增 accountId 参数
      }).then((res: any) => {
        if (res.code === 200) {
          setTotal(res.data?.total)
          setData(res.data?.list || [])
        }
      }).catch(err => {
        console.error(err);
      }).finally(() => {
        setLoading(false)
        loadingRef.current = true
      })
    }
  }

  const cancel = () => {
    formRef?.current?.resetFields()
    setOpen(false);
    setActiveData({
      id: '',
      companyName: '',
      accountName: '',
      tenantAccountNumber: '',
      tenantAccountAssets: [],
      createTime: '',
      revenueAccount: '', // 初始化 revenueAccount 属性
    });
  }

  const injection = () => {
    formRef.current?.validateFields().then((values: any) => {
      setSubmitLoading(true)
      postDepositRecord({
        tenantAccountId: activeData?.revenueAccount,
        currency: values?.outCurrencyCode,
        ...values,
        fee: 0
      }).then((res: any) => {
        if (res.code === 200) {
          cancel()
          query()
          message.open({
            type: 'success',
            content: t('注入成功')
          })
        } else {
          message.open({
            type: 'error',
            content: t(res.message)
          })
        }
      }).finally(() => setSubmitLoading(false))
    })
  }

  const handleCreateAccount = (row) => {
    setAssetAccountId(row?.revenueAccount);
    setCreateAccountModalVisible(true);
  };

  const handleCreateAccountCancel = () => {
    setCreateAccountModalVisible(false);
    setSelectedCurrency(undefined);
  };

  const handleCreateAccountConfirm = () => {
    // 获取 accountName 的值
    const accountNameValue = formRef.current?.getFieldValue('accountName');
    const currencyValue = formRef.current?.getFieldValue('currency');
    if (accountNameValue && currencyValue) {
      setSubmitLoading(true)
      // 调用创建货币账户的接口
      tenantAccountApi.addAccountAsset({
        accountId : assetAccountId, // 假设 appid 是字符串类型，需要转换为整数
        asset : [currencyValue],
        accountName: accountNameValue,
      }).then((res: any) => {
        if (res.code === 200) {
          message.success('货币账户创建成功');
          setCreateAccountModalVisible(false);
        } else {
          message.error(res.message || '货币账户创建失败');
        }
      }).catch((err: any) => {
        console.error(err);
        message.error('货币账户创建失败');
      })
      .finally(() => setSubmitLoading(false));
    } else {
      message.error('请输入账户名称和选择货币');
    }
  };

  const columns = [
    {
      title: t('组织名称'),
      dataIndex: 'companyName',
      key: 'companyName',
      render: (text) => <a>{text}</a>,
    },
    {
      title: t('账户'),
      dataIndex: 'accountName',
      key: 'accountName',
    },
    {
      title: t('账户号码'),
      dataIndex: 'tenantAccountNumber',
      key: 'tenantAccountNumber',
    },
    {
      title: t('USD'),
      dataIndex: 'usd',
      key: 'usd',
    },
    {
      title: t('USDT'),
      dataIndex: 'usdt',
      key: 'usd',
    },
    {
      title: t('账户类型'),
      dataIndex: 'revenueType',
      key: 'revenueType',
      render: (text) => {
        switch (text) {
          case '1':
            return t('主账户');
          case '2':
            return t('奖励账户');
          default:
            return text;
        }
      },
    },
    {
      title: t('账户'),
      dataIndex: 'revenueAccount',
      key: 'revenueAccount',
    },
    {
      title: t('创建时间'),
      dataIndex: 'createTime',
      key: 'createTime',
      render: (text) => moment(text).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: t('操作'),
      key: 'action',
      render: (_, row) => (
        <>
          <Tooltip title={t('注入')} >
            <span className=' cursor-pointer ' onClick={() => {
              setActiveData(row)
              setOpen(true)
            }}><RocketOutlined /></span>
          </Tooltip>
          <Tooltip title={t('创建货币资产')} >
            <span className=' cursor-pointer ml-2' onClick={() => { handleCreateAccount(row) }}>创建货币资产</span>
          </Tooltip>
        </>
      ),
    },
  ]

  return (
    <div>
      <PageHeader
        title={t('代理主账户')}
        breadcrumbs={[]}
      />
      <Card className='mt-[20px]'>
        <Flex wrap="wrap" gap="small" className="mb-[20px]">
          <OrganizationSelect
            allowClear
            className="w-[200px]"
            placeholder={t("请选择组织")}
            onChange={(value) => setAppid(value)}
          />
          <Input
            allowClear
            className="w-[200px]"
            placeholder={t("请输入账户名称")}
            value={accountName}
            onChange={(e) => setAccountName(e.target.value)}
            onPressEnter={query}
          />
          <Input
            allowClear
            className="w-[200px]"
            placeholder={t("请输入账户号码")}
            value={accountNumber}
            onChange={(e) => setAccountNumber(e.target.value)}
            onPressEnter={query}
          />
          <Input
            allowClear
            className="w-[200px]"
            placeholder={t("请输入代理名称")}
            value={companyName}
            onChange={(e) => setCompanyName(e.target.value)}
            onPressEnter={query}
          />
          <Input // 新增 accountId 输入框
            allowClear
            className="w-[200px]"
            placeholder={t("请输入账户ID")}
            value={accountId}
            onChange={(e) => setAccountId(e.target.value)}
            onPressEnter={query}
          />
          <Select
            allowClear
            className="w-[200px]"
            placeholder={t("请选择账户类型")}
            value={revenueType}
            onChange={(value) => setRevenueType(value)}
            options={[
              { label: t('主账户'), value: '1' },
              { label: t('奖励账户'), value: '2' },
            ]}
          />
          <RangePicker
            className="w-[300px]"
            showTime
            allowClear
            onChange={(_, dateStrings) => setDateRange(dateStrings as [string, string])}
          />
          <Button type="primary" className='ml-auto' onClick={query}>
            {t("搜索")}
          </Button>
        </Flex>
        <Table
          loading={loading}
          className='mt-[20px]'
          columns={columns}
          dataSource={data}
          rowKey={(row: any) => row.id}
          pagination={{
            hideOnSinglePage: true,
            pageSize: 10,
            current,
            total,
            onChange: pageChange,
            position: ['bottomRight']
          }}
        />
      </Card>
      <Modal
        title={t("注入")}
        open={open}
        onOk={injection}
        onCancel={cancel}
        centered
        footer={null}
        okText={t('确认')}
        cancelText={t("取消")}
      >
        <Form
          name="sign-up-form"
          layout="vertical"
          ref={formRef}
          labelCol={{ span: 24 }}
          wrapperCol={{ span: 24 }}
          autoComplete="off"
          requiredMark={false}
          initialValues={{
            outCurrencyCode: 'USD'
          }}
        >
          <Form.Item
            label={t('资产')}
            name="outCurrencyCode"
            rules={[
              { required: true, message: t('请选择资产') },
            ]}
          >
            <Select options={[
              { label: 'USDT', value: 'USDT' },
              { label: 'USD', value: 'USD' }
            ]} />
          </Form.Item>
          <Form.Item
            label={t('入金金额')}
            name="inAmount"
            rules={[
              { required: true, message: t('入金金额') },
            ]}
          >
            <Input />
          </Form.Item>
          {/* <Form.Item
            label={t('手续费')}
            name="fee"
            rules={[
              { required: true, message: t('手续费') },
            ]}
          >
            <Input />
          </Form.Item> */}
          <Form.Item
            label={t('汇率')}
            name="exchangeRate"
            rules={[
              { required: true, message: t('汇率') },
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            label={t('备注')}
            name="remarks"
          >
            <Input.TextArea />
          </Form.Item>
          <Flex>
            <Button className='w-full mr-[5px]' shape='round'
              onClick={cancel}
            >{t('取消')}</Button>
            <Button className='w-full ml-[5px]' shape='round' type='primary' loading={submitLoading} onClick={injection}>{t('确认')}</Button>
          </Flex>
        </Form>
      </Modal>
      <Modal
        title={t("创建货币账户")}
        open={createAccountModalVisible}
        onCancel={handleCreateAccountCancel}
        footer={[
          <Button key="back" onClick={handleCreateAccountCancel}>
            {t("取消")}
          </Button>,
          <Button key="submit" type="primary" onClick={handleCreateAccountConfirm}>
            {t("确认")}
          </Button>,
        ]}
      >
        <Form
          name="create-account-form"
          layout="vertical"
          labelCol={{ span: 24 }}
          wrapperCol={{ span: 24 }}
          autoComplete="off"
          requiredMark={false}
          ref={formRef} // 添加 ref 引用
        >
          <Form.Item
            label={t('选择货币')}
            name="currency"
            rules={[
              { required: true, message: t('请选择货币') },
            ]}
          >
            <Select
              value={selectedCurrency}
              onChange={(value) => setSelectedCurrency(value)}
              options={[
                { label: 'USD', value: 'USD' },
                { label: 'USDT', value: 'USDT' },
              ]}
            />
          </Form.Item>
          <Form.Item
            label={t('账户名称')}
            name="accountName"
            rules={[
              { required: true, message: t('请输入账户名称') },
            ]}
          >
            <Input />
          </Form.Item>
        </Form>
      </Modal>
    </div >
  );
};
