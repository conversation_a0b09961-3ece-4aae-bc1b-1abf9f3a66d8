import request from '../request.ts'

// kyc list page
export function getKYCList(params: any) {
    return request({
        url: '/admin/kyc/listpage',
        method: 'get',
        params,
    })
}

//kyc 新增
export function postKYCAdd(data: any) {
    return request({
        url: "/admin/kyc/add",
        method: "post",
        data,
    });
}
//kyc 审核
export function postKYCAudit(data: any) {
    return request({
        url: "/admin/kyc/audit",
        method: "post",
        data,
    });
}
//kyc 详情
export function postKYCDetails(data: any) {
    return request({
        url: "/admin/kyc/detail",
        method: "post",
        data,
    });
}

//kyc 图片
export function postKYCPreviewImage(params: any) {
    return request({
        url: "/admin/kyc/docment/imgb64",
        method: "get",
        params,
    });
}

// 审核列表
export function postKYCAuditHistory(data: any) {
    return request({
        url: "/admin/kyc/audit/history",
        method: "post",
        data,
    });
}

// 快照列表
export function postKYCSnapshotHistory(data: any) {
    return request({
        url: "/admin/kyc/snapshot/history",
        method: "post",
        data,
    });
}

// 快照图片
export function postKYCSnapshotPreviewImage(params: any) {
    return request({
        url: "/admin/kyc/snapshot/docment/imgb64",
        method: "get",
        params,
    });
}

// 更新
export function poseKYCIdentityUpdate(data: any) {
    return request({
        url: "/admin/kyc/identity/update",
        method: "post",
        data,
    });
}

export default {
    getKYCList,
    postKYCAdd,
    postKYCAudit,
    postKYCDetails,
    postKYCPreviewImage,
    postKYCAuditHistory,
    postKYCSnapshotHistory,
    poseKYCIdentityUpdate,
    postKYCSnapshotPreviewImage,
}