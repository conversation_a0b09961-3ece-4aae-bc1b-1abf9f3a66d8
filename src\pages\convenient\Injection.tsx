import {
  Tooltip, Table, Input, Modal, Form, Select, Card,
  message, Flex, Button, DatePicker
} from 'antd';
const { RangePicker } = DatePicker;
import {
  // HomeOutlined,
  RocketOutlined,
  // SearchOutlined,
} from '@ant-design/icons';
import { PageHeader, CopyToClipboard } from '@/components';
import { useTranslation } from 'react-i18next';
import { useEffect, useState, useRef } from 'react';
import { getAccountList, postDepositRecord } from '@/api/account'
import { OrganizationSelect } from '@/components/OrganizationSelect';

interface InjectionData {
  id: string;
  companyName: string;
  accountName: string;
  tenantAccountNumber: string;
  tenantAccountAssets: Array<{
    asset: string;
    balanceAmount: string;
  }>;
}

export const InjectionPage = () => {
  const { t } = useTranslation()
  const [current, setCurrent] = useState<number>(1)
  const [total, setTotal] = useState<number>(0)
  const [loading, setLoading] = useState(false)
  const [open, setOpen] = useState(false);
  const [data, setData] = useState<InjectionData[]>([])
  const formRef: any = useRef()
  const [activeData, setActiveData]: any = useState({})
  const [submitLoading, setSubmitLoading]: any = useState(false)
  const loadingRef = useRef(true)
  const [dateRange, setDateRange]: any = useState([])
  const [accountName, setAccountName] = useState<string>('');
  const [appid, setAppid] = useState<string>('');
  const [accountNumber, setAccountNumber] = useState<string>('');
  const [accountId, setAccountId] = useState<string>('');
  const [pageSize, setPageSize] = useState<number>(10);
  // const navigate = useNavigate();

  useEffect(() => {
    if (loadingRef.current) {
      loadingRef.current = false
      query()
    }
  }, [current])

  const pageChange = (page: number) => {
    setCurrent(page)
  }

  const query = (value?: any, type?: string) => {
    if (!loading) {
      setLoading(true)
      getAccountList({
        pageNum: current,
        pageSize: pageSize, // 使用动态pageSize
        id: accountId || undefined, // 新增查询参数
        appid: appid || undefined,
        accountName: accountName || undefined,
        tenantAccountNumber: accountNumber || undefined,
        beginTime: new Date(dateRange[0])?.getTime() || undefined,
        endTime: new Date(dateRange[1])?.getTime() || undefined,
      }).then((res: any) => {
        if (res.code == 200) {
          setTotal(res.data?.total)
          setData(res.data?.list || [])
        }
      }).catch(err => {
        console.error(err);
      }).finally(() => {
        setLoading(false)
        loadingRef.current = true
        // loadingRef = true
      })
    }
  }


  const cancel = () => {
    formRef?.current?.resetFields()
    setOpen(false);
    setActiveData({})
  }
  const injection = () => {
    // console.log(formRef.current);

    formRef.current?.validateFields().then((values: any) => {
      setSubmitLoading(true)
      postDepositRecord({
        tenantAccountId: activeData?.id,
        currency: values?.outCurrencyCode,
        ...values,
        fee: 0
      }).then((res: any) => {
        const { code } = res
        cancel()
        query()
        if (code == 200) {
          message.open({
            type: 'success',
            content: t('注入成功')
          })

          return
        }
        message.open({
          type: 'error',
          content: t(res.message)
        })
      }).finally(() => setSubmitLoading(false))
    })
    // postDepositRecord({})
    // cancel()

  }

  return (
    <div>
      <PageHeader
        title={t('注入')}
        breadcrumbs={[]}
      />
      <Card className='mt-[20px]'>
        <Flex wrap="wrap" gap="small" className="mb-[20px]">
          {/* <OrganizationSelect
            allowClear
            className="w-[200px]"
            onChange={(value: string | undefined) => {
              if (value === undefined) {
                query(undefined, "organization");
              } else {
                query(value, "organization");
              }
            }}
            placeholder={t("请选择组织")}
          /> */}
           <OrganizationSelect
            allowClear
            className="w-[200px]"
            placeholder={t("请选择组织")}
            onChange={(value) => {
              setAppid(value);
            }}
          />
          <Input
            allowClear
            className="w-[200px]"
            placeholder={t("请输入账户名称")}
            value={accountName}
            onChange={(e) => {
              setAccountName(e.target.value);
            }}
            onPressEnter={()=>{query()}}
          />
          <Input
          allowClear
          className="w-[200px]"
          placeholder={t("请输入账户ID")}
          value={accountId} // 需要新增state
          onChange={(e) => {
            setAccountId(e.target.value);
          }}
          onPressEnter={() => query()}
        />
          <Input
            allowClear
            className="w-[200px]"
            placeholder={t("请输入账户号码")}
            value={accountNumber}
            onChange={(e) => {
              setAccountNumber(e.target.value);
            }}
            onPressEnter={() => query()}
          />
          <RangePicker
            className="w-[300px]"
            showTime
            allowClear
            onChange={(_, dateStrings) => {
              setDateRange(dateStrings as [string, string]);
            }}
          />
          <Button type="primary" className='ml-auto' onClick={() => query()}>
            {t("搜索")}
          </Button>
        </Flex>
        <Table
          loading={loading}
          className='mt-[20px]'
          columns={[
            {
              title: t('组织名称'),
              dataIndex: 'companyName',
              key: 'companyName',
              render: (text) => <a>{text}</a>,
            },
            {
              title: t('账户'),
              dataIndex: 'accountName',
              key: 'accountName',
              render: (text) => (
                <Flex align="center" gap={8}>
                  {text}
                  <CopyToClipboard text={text} />
                </Flex>
              ),
            },
            {
              title: t('账户ID'),
              dataIndex: 'id',
              key: 'id',
              render: (text) => (
                <Flex align="center" gap={8}>
                  {text}
                  <CopyToClipboard text={text} />
                </Flex>
              ),
            },
            {
              title: t('账户号码'),
              dataIndex: 'tenantAccountNumber',
              key: 'tenantAccountNumber',
              render: (text) => (
                <Flex align="center" gap={8}>
                  {text}
                  <CopyToClipboard text={text} />
                </Flex>
              ),
            },
            {
              title: t('USD'),
              dataIndex: 'usd',
              key: 'usd', 
            },
            {
              title: t('USDT'),
              dataIndex: 'usdt',
              key: 'usdt', 
            },
            // {
            //   title: t('价值'),
            //   dataIndex: 'tenantAccountAssets',
            //   key: 'tenantAccountAssets',
            //   render: (_, row: any) => {
            //     const d = (row?.tenantAccountAssets?.filter((v: any) => v?.asset == 'USD')?.map((v: any) => v?.balanceAmount)) || '0'
            //     return d && d?.length && d || '0'
            //   }
            // },
            {
              title: t('操作'),
              key: 'action',
              render: (_, row) => (//t('操作')
                <Tooltip title={t('注入')} >
                  <span className=' cursor-pointer ' onClick={() => {
                    setActiveData(row)
                    setOpen(true)
                  }}><RocketOutlined /></span>
                </Tooltip>
              ),
            },
          ]}
          dataSource={data}
          rowKey={(row: any) => row.id}
          pagination={{
            hideOnSinglePage: true,
            pageSize, // 使用动态pageSize
            current,
            total,
            onChange: pageChange,
            onShowSizeChange: (current, size) => {
              setPageSize(size);
              setCurrent(1); // 切换条数时重置到第一页
            },
            showSizeChanger: true,
            pageSizeOptions: ['10', '20', '50', '100'],
            position: ['bottomRight']
          }}
        />
      </Card>
      <Modal
        title={t("注入")}
        open={open}
        onOk={injection}
        onCancel={cancel}
        centered
        footer={null}
        okText={t('确认')}
        cancelText={t("取消")}
      >
        <Form
          name="sign-up-form"
          layout="vertical"
          ref={formRef}
          labelCol={{ span: 24 }}
          wrapperCol={{ span: 24 }}
          autoComplete="off"
          requiredMark={false}
          initialValues={{
            outCurrencyCode: 'USD'
          }}
        >
          <Form.Item
            label={t('资产')}
            name="outCurrencyCode"
            rules={[
              { required: true, message: t('请选择资产') },
            ]}
          >
            <Select options={[
              { label: 'USDT', value: 'USDT' },
              { label: 'USD', value: 'USD' }
            ]} />
          </Form.Item>
          <Form.Item
            label={t('入金金额')}
            name="inAmount"
            rules={[
              { required: true, message: t('入金金额') },
            ]}
          >
            <Input />
          </Form.Item>
          {/* <Form.Item
            label={t('手续费')}
            name="fee"
            rules={[
              { required: true, message: t('手续费') },
            ]}
          >
            <Input />
          </Form.Item> */}
          <Form.Item
            label={t('汇率')}
            name="exchangeRate"
            rules={[
              { required: true, message: t('汇率') },
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            label={t('备注')}
            name="remarks"
          >
            <Input.TextArea />
          </Form.Item>
          <Flex>
            <Button className='w-full mr-[5px]' shape='round'
              onClick={cancel}
            >{t('取消')}</Button>
            <Button className='w-full ml-[5px]' shape='round' type='primary' loading={submitLoading} onClick={injection}>{t('确认')}</Button>
          </Flex>
        </Form>
      </Modal>
    </div >
  );
};
