import request from '../request.ts'

// 卡账户
export function getCardAccountList(params: any) {
    return request({
        url: '/admin/system/cardAccount/listpage',
        method: 'get',
        params,
    })
}

// 创建卡账户
export function postCreateCardAccount(data: any) {
    return request({
        url: '/admin/system/cardAccount/create',
        method: 'post',
        data,
    })
}

// 卡列表
export function getCardList(params: any) {
    return request({
        url: '/admin/system/card/listpage',
        method: 'get',
        params,
    })
}

// 卡配置列表
export function getCardConfigList(params: any) {
    return request({
        url: '/admin/creditCard/type/list',
        method: 'get',
        params,
    })
}

// 锁卡
export function postCardLock(data: any) {
    return request({
        url: '/admin/system/card/lock',
        method: 'post',
        data,
    })
}

// 解锁
export function postCardUnlock(data: any) {
    return request({
        url: '/admin/system/card/unlock',
        method: 'post',
        data,
    })
}

// 卡账户充值
export function postCardTopup(data: any) {
    return request({
        url: '/admin/system/cardAccount/topup',
        method: 'post',
        data,
    })
}

// 充值
export function postCardApply(data: any) {
    return request({
        url: '/admin/system/card/apply',
        method: 'post',
        data,
    })
}

//限额控制
export function postCardSpendingControls(data: any) {
    return request({
        url: '/admin/system/card/spending-controls',
        method: 'post',
        data,
    })
}

//change-pin
export function postCardChangePin(data: any) {
    return request({
        url: '/admin/system/card/change-pin',
        method: 'post',
        data,
    })
}
//update-phone
export function postCardUpdatePhone(data: any) {
    return request({
        url: '/admin/system/card/update-phone',
        method: 'post',
        data,
    })
}
//卡暂停
export function postCardSuspend(data: any) {
    return request({
        url: '/admin/system/card/suspend',
        method: 'post',
        data,
    })
}


//取消卡暂停
export function postCardUNSuspend(data: any) {
    return request({
        url: '/admin/system/card/unsuspend',
        method: 'post',
        data,
    })
}


//卡取消
export function postCardCancel(data: any) {
    return request({
        url: '/admin/system/card/cancel',
        method: 'post',
        data,
    })
}
//卡激活
export function postCardActivate(data: any) {
    return request({
        url: '/admin/system/card/activate',
        method: 'post',
        data,
    })
}
//获取国家
export function postCountries(data: any) {
    return request({
        url: '/admin/utgl/countries',
        method: 'post',
        data,
    })
}
//职业
export function postOccupations(data: any) {
    return request({
        url: '/admin/utgl/occupations',
        method: 'post',
        data,
    })
}
