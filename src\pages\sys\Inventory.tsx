import {
    Table, Input, Upload,
    Modal, message, Card, Flex, Button, DatePicker, Select
} from 'antd';
import {
    InboxOutlined,
    ExclamationCircleOutlined,
    SearchOutlined,
    EyeOutlined,
    EyeInvisibleOutlined,
} from '@ant-design/icons';
import { PageHeader } from '@/components';
import { useTranslation } from 'react-i18next';
import { useEffect, useRef, useState } from 'react';
import repoApi from '@/api/repo'
import { exportArrayToExcel } from '@/utils/excel'
import * as XLSX from 'xlsx'
import { useSelector } from 'react-redux';
import { INVENTORY_CARD_EMUN } from "@/utils/enum"
import { OrganizationSelect } from '@/components/OrganizationSelect';

const { Dragger } = Upload;
const { confirm } = Modal;
const { RangePicker } = DatePicker;

interface SearchParams {
    appid?: string;
    creditCardTypeId?: string;
    cardNumber?: string;
    status?: string;
    startTime?: number;
    endTime?: number;
}

export const InventoryPage = () => {
    // const context = useStylesContext();
    const { t } = useTranslation()
    const [current, setCurrent] = useState<number>(1)
    const [total, setTotal] = useState<number>(0)
    const loadingRef = useRef(true)
    const [loading, setLoading] = useState(false)
    const [open, setOpen] = useState(false);
    const [data, setData] = useState([])
    const [importList, setImportList]: any = useState()
    const userStore = useSelector((state: any) => { return state.user });
    const [searchParams, setSearchParams] = useState<SearchParams>({});
    const [dateRange, setDateRange]: any = useState([]);

    useEffect(() => {
        if (loadingRef.current) {
            loadingRef.current = false
            query()
        }
    }, [current])

    const pageChange = (page: number) => {
        setCurrent(page)
    }

    const query = (value?: any, type?: string) => {
        if (!loading) {
            setLoading(true)
            repoApi.getInventoryList({
                pageNum: current,
                pageSize: 10,
                ...searchParams,
                beginTime: new Date(dateRange[0])?.getTime() || undefined,
                endTime: new Date(dateRange[1])?.getTime() || undefined,
            }).then((res: any) => {
                if (res.code == 200) {
                    setTotal(res.data.total)
                    setData(res.data.list?.map((v: any) => {
                        return {
                            ...v,
                            showCardNumber: false,
                            showCVC: false
                        }
                    }))
                }
            }).catch(err => {
                console.log(err);
            }).finally(() => {
                loadingRef.current = true
                setLoading(false)
            })
        }
    }


    const cancel = () => {
        setOpen(false);
        // setActive({})
    }
    const importData = () => {
        const list = importList.map((v: any) => {
            const obj = Object.values(v)
            return {
                cardTypeId: obj[0],
                cardNumber: obj[1],
                cardCvc: obj[2],
                expiryMonth: obj[3],
                expiryYear: obj[4],
            }
        })
        setLoading(true)
        repoApi.postImportList({
            records: list
        }).then((res: any) => {
            const { code } = res
            if (code == 200) {
                message.open({
                    type: 'success',
                    content: <>
                        <div>{t('当前上传共')}{res.data.totalCount} {t('条')}</div>
                        <div> {`${t('添加成功')}${res.data.successCount}`}</div>
                        <div className='text-[#f76c6c]'> {`${t('添加失败')}${res.data.failCount}`}</div>
                    </>
                })
                cancel()
                query()
                return
            }
            message.open({
                type: 'error',
                content: t(res.message)
            })
        }).finally(() => {
            setLoading(false)
        })

    }
    const add = () => {
        setOpen(true);
    }

    const props = {
        name: 'file',
        maxCount: 1,
        accept: ".xlsx, .xls",
        multiple: false,
        beforeUpload: () => false,
        onChange(info: any) {
            if (info.file) {
                importfxx(info.file)
            }
        }
    };

    const importfxx = (file: File) => {
        if (!file) return
        const reader = new FileReader();
        reader.onload = (e: ProgressEvent<FileReader>) => {
            try {
                const arrayBuffer = e.target?.result;
                if (!arrayBuffer) {
                    message.open({
                        type: 'error',
                        content: t('文件读取失败')
                    })
                    return;
                }

                // 将 ArrayBuffer 转换为 Uint8Array
                const data = new Uint8Array(arrayBuffer as ArrayBuffer);

                // 读取 Excel 数据
                const workbook = XLSX.read(data, { type: 'array' });
                const firstSheetName = workbook.SheetNames[0];
                const worksheet = workbook.Sheets[firstSheetName];
                const outdata = XLSX.utils.sheet_to_json(worksheet);

                if (outdata.length === 0) {
                    message.open({
                        type: 'error',
                        content: t('沒有數據')
                    })
                    return;
                }

                console.log(outdata);
                setImportList(outdata)
            } catch (error) {
                console.error("文件解析失败:", error);
                message.open({
                    type: 'error',
                    content: t('文件解析失败，请检查文件格式是否正确。')
                })
            }
        };

        reader.onerror = (e) => {
            console.error("文件读取失败:", e);
            message.open({
                type: 'error',
                content: t('文件读取失败，请重试。')
            })
        };

        reader.readAsArrayBuffer(file);
    };

    // 设置为预留
    const handelSetStatusReserved = (row: any) => {
      repoApi
        .reservedCardSuffix({ id: row.id, status: 4 })
        .then((res: any) => {
          const { code } = res;
          if (code == 200) {
            message.open({
              type: "success",
              content: t("成功"),
            });
            query();
            return;
          }
          message.open({
            type: "error",
            content: t(res.message),
          });
        })
        .finally(() => setLoading(false));
    };

    // 取消预留
    const handelSetStatusAvailable = (row: any) => {
      repoApi
        .reservedCardSuffix({ id: row.id, status: 1 })
        .then((res: any) => {
          const { code } = res;
          if (code == 200) {
            message.open({
              type: "success",
              content: t("成功"),
            });
            query();
            return;
          }
          message.open({
            type: "error",
            content: t(res.message),
          });
        })
        .finally(() => setLoading(false));
    };

    const del = (row: any) => {
        confirm({
            icon: <ExclamationCircleOutlined />,
            centered: true,
            content: <>
                {t("请确认是否删除此卡")}
                <div>{t("组织名称")}: {row.companyName}</div>
                <div>{t("卡号")}: {row.cardNumber}</div>
            </>,
            onOk() {
                repoApi.postDelInventorCard({ ids: [row.id] }).then((res: any) => {
                    const { code } = res
                    if (code == 200) {
                        message.open({
                            type: 'success',
                            content: t('成功')
                        })
                        query()
                        return
                    }
                    message.open({
                        type: 'error',
                        content: t(res.message)
                    })
                }).finally(() => setLoading(false))
            },
            onCancel() {
                console.log('Cancel');
            },
        });
    }



    const downloadModal = async () => {

        const title = await `${t('信用卡类型模版')}${Date.now().toString().slice(-6)}`
        exportArrayToExcel({
            filename: title,
            autoWidth: true,
            data: [],
            key: [],
            title: [
                t('卡类型ID'),
                t('卡号'),
                t('卡CVC'),
                t('卡有效期月'),
                t('卡有效期年')
            ],
        })
    }

    const queryCVC = async (row: any) => {
        return await repoApi.postQueryCardCVC({ id: row.id })//row.cardNumber })
    }

    return (
        <div>
            <PageHeader
                title="库存管理"
                breadcrumbs={[

                ]}
            />
            <Card className='mt-[20px]'>
                <Flex wrap="wrap" gap="small" className="mb-[20px]">
                    <OrganizationSelect
                        allowClear
                        className="w-[200px]"
                        placeholder={t("请选择组织")}
                        onChange={(value) => {
                            setSearchParams(prev => ({ ...prev, appid: value }));
                        }}
                    />
                    <Input
                        allowClear
                        className="w-[200px]"
                        placeholder={t("请输入产品ID")}
                        onChange={(e) => {
                            setSearchParams(prev => ({ ...prev, creditCardTypeId: e.target.value }));
                        }}
                        onPressEnter={() => query()}
                    />
                    <Input
                        allowClear
                        className="w-[200px]"
                        placeholder={t("请输入卡号")}
                        onChange={(e) => {
                            setSearchParams(prev => ({ ...prev, cardNumber: e.target.value }));
                        }}
                        onPressEnter={() => query()}
                    />
                    <Select
                        allowClear
                        className="w-[200px]"
                        placeholder={t("请选择状态")}
                        onChange={(value) => {
                            setSearchParams(prev => ({ ...prev, status: value }));
                        }}
                        options={Object.keys(INVENTORY_CARD_EMUN()).map((v) => ({
                            label: INVENTORY_CARD_EMUN()[v],
                            value: v
                        }))}
                    />
                    <RangePicker
                        showTime
                        allowClear
                        className="w-[300px]"
                        onChange={(_, dateStrings) => {
                            setDateRange(dateStrings);
                        }}
                    />
                    <Flex className="ml-auto gap-[10px]">
                        <Button type="primary" onClick={() => query()}>
                            {t("搜索")}
                        </Button>
                        {userStore?.user?.user?.roleType === "0" && 
                            <Button onClick={add} type="primary">
                                {t("导入")}
                            </Button>
                        }
                    </Flex>
                </Flex>
                <Table
                    loading={loading}
                    className='mt-[20px]'
                    rowKey={(row: any) => row.id}
                    columns={[
                        {
                            title: t('产品ID'),
                            dataIndex: 'creditCardTypeId',
                            key: 'creditCardTypeId',
                        },
                        {
                            title: t('组织名称'),
                            dataIndex: 'companyName',
                            key: 'companyName',
                        },
                        {
                            title: t('卡号'),
                            dataIndex: 'cardNumber',
                            key: 'cardNumber',
                            render: (text: any, row: any) => <>
                                {row.showCardNumber ? text && text : `${text.slice(0, 4)} **** **** ${text.slice(-4)}`}
                                <span className='ml-[10px] cursor-pointer'
                                    onClick={
                                        () => {
                                            row.showCardNumber = !row.showCardNumber
                                            setData([...data])
                                        }
                                    }
                                >{row.showCardNumber ? <EyeOutlined /> : <EyeInvisibleOutlined />}</span>
                            </>
                        },
                        {
                            title: t('创建人'),
                            dataIndex: 'createUserName',
                            key: 'createUserName',
                        },
                        {
                            title: t('信用卡名称'),
                            dataIndex: 'creditCardName',
                            key: 'creditCardName',
                        },
                        {
                            title: t('卡有效期月'),
                            dataIndex: 'expiryMonth',
                            key: 'expiryMonth',
                        },
                        {
                            title: t('卡有效期年'),
                            dataIndex: 'expiryYear',
                            key: 'expiryYear',
                        },
                        {
                            title: t('CVC'),
                            dataIndex: 'cvc',
                            key: 'cvc',
                            render: (text: any, row: any) => <>
                                {row.showCVC ? text : '****'}
                                <span className='ml-[10px] cursor-pointer'
                                    onClick={
                                        async () => {
                                            const res = await queryCVC(row)
                                            row.showCVC = !row.showCVC
                                            row.cvc = res.data
                                            setData([...data])
                                        }
                                    }
                                >{row.showCVC ? <EyeOutlined /> : <EyeInvisibleOutlined />}</span>
                            </>
                        },
                        {
                            title: t('状态'),
                            dataIndex: 'status',
                            key: 'status',
                            render: (status: any) => INVENTORY_CARD_EMUN()?.[status]
                        },
                        {
                            title: t('创建时间'),
                            dataIndex: 'addTime',
                            key: 'addTime',
                            render: (text: any) => new Date(text).toLocaleString() || '-'
                        },
                        {
                            title: t('操作'),
                            key: 'action',
                            render: (_, row: any) => (
                                <>
                                    <Button type='text' className='cursor-pointer _primary' onClick={() => del(row)}>{t('删除')}</Button>
                                    {row.status === 1 && (
                                        <Button type='text' className='cursor-pointer _primary' onClick={() => handelSetStatusReserved(row)}>
                                            {t('设置预留')}
                                        </Button>
                                    )}
                                    {row.status === 4 && (
                                        <Button type='text' className='cursor-pointer _primary' onClick={() => handelSetStatusAvailable(row)}>
                                            {t('取消预留')} {/* 建议将文案改为「取消预留」更合理 */}
                                        </Button>
                                    )}
                                </>
                            ),
                        },
                    ]}
                    dataSource={data}
                    pagination={{
                        hideOnSinglePage: true,
                        pageSize: 10,
                        current,
                        total,
                        onChange: pageChange,
                        position: ['bottomRight']
                    }}
                />
            </Card>
            <Modal
                maskClosable={false}
                keyboard={false}
                title={t("导入KYC列表")}
                open={open}
                onOk={importData}
                onCancel={cancel} 
                centered
                okText={t('导入')}
                confirmLoading={loading}
                width={800}
                destroyOnClose={true}
                cancelText={t("取消")}
            >
                <Button color="primary" variant="text" onClick={downloadModal}>{t('下载模版')}</Button>
                <Dragger {...props}>
                    <p className="ant-upload-drag-icon">
                        <InboxOutlined />
                    </p>
                    <p className="ant-upload-text">{t('单击或拖动文件到此区域进行上传')}</p>
                    <p className="ant-upload-hint">{t('严禁上传公司数据或其他被禁止的文件')}
                    </p>
                </Dragger>
            </Modal>
        </div>
    );
};
