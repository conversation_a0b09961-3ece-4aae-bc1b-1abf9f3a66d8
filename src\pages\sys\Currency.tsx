import {
    Table, Input, Switch,
    Modal, Form, message, Card, Flex, Button
} from 'antd';
import {
    HomeOutlined,
    // RocketOutlined,
    SearchOutlined
} from '@ant-design/icons';
import { PageHeader } from '@/components';
import { useTranslation } from 'react-i18next';
import { useEffect, useRef, useState } from 'react';
import {
    getCurrencyList,
    postCurrencyAdd,
    postCurrencyUpdate
} from '@/api/sys'
// import { useNavigate } from 'react-router-dom';

export const CurrencyPage = () => {
    // const context = useStylesContext();
    const { t } = useTranslation()
    const [current, setCurrent] = useState<number>(1)
    const [total, setTotal] = useState<number>(0)
    const loadingRef = useRef(true)
    const [loading, setLoading] = useState(false)
    const [open, setOpen] = useState(false);
    const [data, setData] = useState([])
    const formRef: any = useRef()
    const [activeData, setActive] = useState<any>()
    // const navigate = useNavigate();

    useEffect(() => {
        if (loadingRef.current) {
            loadingRef.current = false
            query()
        }
    }, [])
    const pageChange = (page: number) => {
        setCurrent(page)
    }

    const query = (value?: any, type?: string) => {
        if (!loading) {
            getCurrencyList({
                pageNum: 1,
                pageSize: 20,
                companyName: type == 'search' ? value : undefined
            }).then((res: any) => {
                if (res.code == 200) {
                    setTotal(res.data)
                    setData(res.data || [])
                }
            }).catch(err => {
                console.log(err);
            }).finally(() => {
                setLoading(false)
            })
        }
    }


    const cancel = () => {
        setOpen(false);
        setActive({})
    }
    const addAccount = () => {
        let api = postCurrencyAdd
        if (activeData?.id) api = postCurrencyUpdate
        formRef.current?.validateFields().then((values: any) => {
            api({
                ...values,
                status: values?.status ? 1 : 0,
                id: activeData?.id || undefined
            }).then((res: any) => {
                const { code } = res
                if (code == 200) {
                    message.open({
                        type: 'success',
                        content: activeData?.id ? t('修改成功') : t('添加成功')
                    })
                    cancel()
                    query()
                    return
                }
                message.open({
                    type: 'error',
                    content: t(res.message)
                })
            })
        })

    }
    const add = () => {
        setOpen(true);
    }

    // const toUrl = (row: any) => {
    //     navigate('/sys/organiz/apiConfig', { state: { id: row?.appid } });
    // }
    // const toUrl2 = (row: any) => {
    //     navigate('/sys/organiz/cardConfig', { state: { id: row?.appid } });
    // }

    const EMUN: any = { true: t('正常'), false: t('停止') }
    return (
        <div>
            <PageHeader
                title=""
                breadcrumbs={[
                    {
                        title: (
                            <>
                                <HomeOutlined />
                                <span>{t('货币管理')}</span>
                            </>
                        ),

                    },
                ]}
            />
            <Card className='mt-[20px]'>
                <Flex justify='space-between'>
                    <Input
                        prefix={<SearchOutlined />}
                        allowClear
                        placeholder={t('请输入关键字进行搜索')}
                        className='w-[30%] max-w-[300px]'
                        onBlur={(e: any) => query(e.target.value, 'search')}
                    />
                    <Button onClick={add} type='primary'> {t('新增配置')}</Button>
                </Flex>
                <Table
                    loading={loading}
                    className='mt-[20px]'
                    rowKey={(row: any) => row.id}
                    columns={[
                        {
                            title: t('货币名称'),
                            dataIndex: 'currencyName',
                            key: 'currencyName',
                        },
                        {
                            title: t('货币代码'),
                            dataIndex: 'currencyCode',
                            key: 'currencyCode',
                        },
                        {
                            title: t('精度'),
                            dataIndex: 'precisionNum',
                            key: 'precisionNum',
                        },
                        {
                            title: t('限额'),
                            dataIndex: 'limitUse',
                            key: 'limitUse',
                        },
                        {
                            title: t('状态'),
                            dataIndex: 'status',
                            key: 'status',
                            render: (status: any) => EMUN?.[status]
                        },
                        {
                            title: t('更新时间'),
                            dataIndex: 'updateTime',
                            key: 'updateTime',
                            render: (text: any) => new Date(text).toLocaleString() || '-'
                        },
                        {
                            title: t('操作'),
                            key: 'action',
                            render: (_, row: any) => (
                                <>
                                    <Button type='text' className=' cursor-pointer _primary ' onClick={() => {
                                        setActive(row)
                                        setOpen(true)
                                        setTimeout(() => {
                                            formRef.current?.setFieldsValue({ ...row })
                                        }, 10)
                                    }}>{t('修改')}</Button>

                                </>
                            ),
                        },
                    ]}
                    dataSource={data}
                    pagination={{
                        hideOnSinglePage: true,
                        pageSize: 10,
                        current,
                        total,
                        onChange: pageChange,
                        position: ['bottomRight']
                    }}
                />
            </Card>
            <Modal
                maskClosable={false}
                keyboard={false}
                title={activeData?.id ? t("修改货币配置") : t("新增货币")}
                open={open}
                onOk={addAccount}
                onCancel={cancel} 
                centered
                okText={t('确认')}
                destroyOnClose={true}
                cancelText={t("取消")}
            >
                <Form
                    ref={formRef}
                    name="sign-up-form"
                    labelCol={{ flex: '120px' }}
                    labelWrap
                    wrapperCol={{ flex: 1 }}
                    autoComplete="off"
                    defaultValue={{ ...activeData }}
                    requiredMark={false}
                >
                    <Form.Item
                        label={t('货币名称')}
                        name="currencyName"
                        rules={[
                            { required: true, message: t('货币名称') },
                        ]}
                    >
                        <Input />
                    </Form.Item>
                    <Form.Item
                        label={t('货币代码')}
                        name="currencyCode"
                        rules={[
                            { required: true, message: t('货币代码') },
                        ]}
                    >
                        <Input />
                    </Form.Item>
                    <Form.Item
                        label={t('排序')}
                        name="sortNum"
                        rules={[
                            { required: true, message: t('排序') },
                        ]}
                    >
                        <Input />
                    </Form.Item>
                    <Form.Item
                        label={t('精度')}
                        name="precisionNum"
                        rules={[
                            { required: true, message: t('精度') },
                        ]}
                    >
                        <Input />
                    </Form.Item>
                    <Form.Item
                        label={t('限额')}
                        name="limitUse"
                        rules={[
                            { required: true, message: t('限额') },
                        ]}
                    >
                        <Input />
                    </Form.Item>
                    <Form.Item
                        label={t('状态')}
                        name="status"
                    >
                        <Switch />
                    </Form.Item>

                </Form>
            </Modal>
        </div>
    );
};
