import request from '../request.ts'

 
export function postCardAccountClientIdentityUpdate(data: any) {
    return request({
        url: '/admin/system/cardAccount/client-identity/update',
        method: 'post',
        data,
    })
}
 
 
export function postCardAccountClientIdentityQuery(data: any) {
    return request({
        url: '/admin/system/cardAccount/client-identity/query',
        method: 'post',
        data,
    })
}
 

export default {
    postCardAccountClientIdentityUpdate,
    postCardAccountClientIdentityQuery,
}