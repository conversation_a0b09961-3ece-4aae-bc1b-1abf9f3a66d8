import request from "../request.ts";

import { ResponseResult } from "../api_model.ts";

interface UsAccountWithdrawLimitReq {
  accountId: string;
  limitAmount: string;
  limitCount: string;
}

interface UsAccountLimitGetReq {
  accountId: string;
}

interface UsAccountWithdrawLimitResp {
  accountId: string;
  limitAmount: string;
  limitCount: string;
}

export async function accountLimitUpdate(
  data: UsAccountLimitGetReq
): Promise<ResponseResult<UsAccountWithdrawLimitResp>> {
  return request({
    url: "/admin/system/accountlimit/update",
    method: "post",
    data,
  });
}

export async function accountLimitQuery(
  data: UsAccountWithdrawLimitReq
): Promise<ResponseResult<UsAccountWithdrawLimitResp>> {
  return request({
    url: "/admin/system/accountlimit/get",
    method: "post",
    data,
  });
}

export default {
  accountLimitUpdate,
  accountLimitQuery,
};
