import request from "../request.ts";

import { ResponseResult } from "../api_model.ts";

interface UsTenantWithdrawLimitReq {
  appId: string;
  groupType: string;
  singleLimit: string;
  singleLimitCount: string;
  totalLimit: string;
  remark: string;
}

interface UsTenantLimitGetReq {
  appId: string;
}

interface UsTenantWithdrawLimitResp {
  singleLimit: string;
  appId: string;
  singleLimitCount: string;
  totalLimit: string;
  remark: string;
}

export async function tenantLimitCreate(
  data: UsTenantWithdrawLimitReq
): Promise<ResponseResult<UsTenantWithdrawLimitResp>> {
  return request({
    url: "/admin/tenant/limit/create",
    method: "post",
    data,
  });
}

export async function tenantLimitUpdate(
  data: UsTenantWithdrawLimitReq
): Promise<ResponseResult<UsTenantWithdrawLimitResp>> {
  return request({
    url: "/admin/tenant/limit/update",
    method: "post",
    data,
  });
}

export async function tenantLimitQuery(
  data: UsTenantLimitGetReq
): Promise<ResponseResult<UsTenantWithdrawLimitResp>> {
  return request({
    url: "/admin/tenant/limit/get",
    method: "post",
    data,
  });
}

export default {
  tenantLimitCreate,
  tenantLimitUpdate,
  tenantLimitQuery,
};
