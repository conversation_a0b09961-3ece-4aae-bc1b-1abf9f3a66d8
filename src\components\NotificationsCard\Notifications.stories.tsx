// import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
// import NotificationsData from '../../../public/mocks/Notifications.json';

import { NotificationsCard } from './NotificationsCard.tsx';

const meta = {
  title: 'Components/Notifications/List',
  component: NotificationsCard,
  parameters: {
    layout: 'centered',
  },
}

export default meta;


export const Default: any = {
  args: {
    data: [],
    style: { width: 500 },
  },
};

export const Loading: any = {
  args: {
    loading: true,
    style: { width: 500 },
  },
};

export const Error: any = {
  args: {
    error: 'Error fetching items',
    style: { width: 500 },
  },
};

export const Empty: any = {
  args: {
    data: [],
    style: { width: 500 },
  },
};
