import { Flex, Card } from 'antd';
// import { useStylesContext } from '@/context';
// import {
//   HomeOutlined,
//   UserOutlined,
//   SearchOutlined,
// } from '@ant-design/icons';
import { PageHeader } from '@/components';
// import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Base64_Img1 } from '@/components/SVG_Components/Base64_Img1'
import { SVGCard9 } from '@/components/SVG_Components/SVG_Card9'
import { useEffect, useRef, useState } from 'react';
// const { Title, Text } = Typography;
// import { useNavigate } from 'react-router-dom';
// interface DataType {
//   key: string;
//   name: string;
//   age: number;
//   address: string;
//   tags: string[];
// }

// const { RangePicker } = DatePicker;

// const defaultData: DataType[] = [
//   {
//     key: '1',
//     name: '<PERSON>',
//     age: 32,
//     address: 'New York No. 1 Lake Park',
//     tags: ['nice', 'developer'],
//   },
//   {
//     key: '2',
//     name: '<PERSON>',
//     age: 42,
//     address: 'London No. 1 Lake Park',
//     tags: ['loser'],
//   },
//   {
//     key: '3',
//     name: 'Joe Black',
//     age: 32,
//     address: 'Sydney No. 1 Lake Park',
//     tags: ['cool', 'teacher'],
//   },
// ];

export const SecurityPage = () => {
  // const context = useStylesContext();
  const { t } = useTranslation()
  const loadingRef = useRef(true)
  const [loading, setLoading] = useState(false)
  // const navigate = useNavigate();


  useEffect(() => {
    if (loadingRef.current) {
      loadingRef.current = false
      query()
    }
  }, [])


  const query = (value?: any, type?: string) => {
    if (!loading) {
      console.log(value, type);

      setLoading(true)
      setTimeout(() => {
        setLoading(false)
      }, 1000)
    }
  }


  // const goDetails = (row: any) => {
  //   // setActiveData(row)
  //   // setIsDetails(true)
  //   navigate('/acccess/account-management/details')

  // }

  return (
    <div className=' relative'>
      <PageHeader
        title={('安全')}
        breadcrumbs={[
        ]}
      />
      <Card className='mt-[20px] '>
        <div className='text-[12px]  font-bold mb-[20px]'>{t('提高您账户的安全性 ')}</div>
        <Flex>
          <Card className='w-[32%]'>
            <Flex>
              <Base64_Img1 />
              <div className='ml-[20px]'>
                <div className='flex items-center text-[#389e0d]' >  <SVGCard9 /> <span className='font-bold text-[16px] text-[#000] ml-[20px]'>{t('谷歌两部验证')}</span></div>
                <div className='text-[13px] text-[#666]'>
                  {t('用谷歌身份验证器取代短讯 (或替代的 TOTP 兼容身份验证器) 进行两步验证。')}
                </div>
              </div>
            </Flex>
          </Card>
        </Flex>
      </Card>
    </div>
  );
};
