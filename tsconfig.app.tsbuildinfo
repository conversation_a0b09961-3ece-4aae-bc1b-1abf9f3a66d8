{"root": ["./src/app.tsx", "./src/main.tsx", "./src/vite-env.d.ts", "./src/api/api_model.ts", "./src/api/login.ts", "./src/api/request.ts", "./src/api/account/index.ts", "./src/api/accountlimit/index.ts", "./src/api/card/index.ts", "./src/api/cardaccountsync/index.ts", "./src/api/cardaccounttransactionlog/index.ts", "./src/api/kyc/index.ts", "./src/api/repo/index.ts", "./src/api/sys/index.ts", "./src/api/tenantaccount/index.ts", "./src/api/tenantlimit/index.ts", "./src/api/tenantrevenue/index.ts", "./src/api/transactions/index.ts", "./src/api/user/index.ts", "./src/api/webhook/index.ts", "./src/components/index.ts", "./src/components/accountlimitmodal/accountlimitmodal.tsx", "./src/components/accounttopupmodal/accounttopupmodal.tsx", "./src/components/backbtn/backbtn.stories.tsx", "./src/components/backbtn/backbtn.tsx", "./src/components/contactform/contactform.stories.tsx", "./src/components/contactform/contactform.tsx", "./src/components/container/container.tsx", "./src/components/container/index.ts", "./src/components/copytoclipboard/copytoclipboard.tsx", "./src/components/debounceselect/index.tsx", "./src/components/employeecard/employeecard.stories.tsx", "./src/components/employeecard/employeecard.tsx", "./src/components/faqcollapse/faqcollapse.tsx", "./src/components/flex/flex.tsx", "./src/components/language/language.tsx", "./src/components/loader/loader.stories.tsx", "./src/components/loader/loader.tsx", "./src/components/logo/logo.stories.tsx", "./src/components/logo/logo.tsx", "./src/components/notificationscard/notifications.stories.tsx", "./src/components/notificationscard/notificationscard.tsx", "./src/components/notificationsitem/notificationsitem.stories.tsx", "./src/components/notificationsitem/notificationsitem.tsx", "./src/components/nprogress/bar.tsx", "./src/components/nprogress/container.tsx", "./src/components/nprogress/progress.tsx", "./src/components/nprogress/spinner.tsx", "./src/components/nprogress/index.ts", "./src/components/organizationselect/index.tsx", "./src/components/pageheader/pageheader.stories.tsx", "./src/components/pageheader/pageheader.tsx", "./src/components/refreshbtn/refreshbtn.stories.tsx", "./src/components/refreshbtn/refreshbtn.tsx", "./src/components/svg_components/base64_img1.tsx", "./src/components/svg_components/svg_card.tsx", "./src/components/svg_components/svg_card10.tsx", "./src/components/svg_components/svg_card2.tsx", "./src/components/svg_components/svg_card3.tsx", "./src/components/svg_components/svg_card4.tsx", "./src/components/svg_components/svg_card5.tsx", "./src/components/svg_components/svg_card6.tsx", "./src/components/svg_components/svg_card7.tsx", "./src/components/svg_components/svg_card8.tsx", "./src/components/svg_components/svg_card9.tsx", "./src/components/tenantlimitbutton/tenantlimitbutton.tsx", "./src/components/tenantlimitmodal/tenantlimitmodal.tsx", "./src/constants/index.ts", "./src/constants/routes.ts", "./src/context/index.ts", "./src/context/styles.tsx", "./src/hooks/index.ts", "./src/hooks/usefetchdata.tsx", "./src/hooks/usepagecontext.tsx", "./src/i18n/index.ts", "./src/i18n/locales/en.ts", "./src/i18n/locales/zh.ts", "./src/i18n/locales/zh_hk.ts", "./src/layouts/index.ts", "./src/layouts/app/app.tsx", "./src/layouts/app/footernav.tsx", "./src/layouts/app/headernav.tsx", "./src/layouts/app/sidenav.tsx", "./src/layouts/app/index.ts", "./src/layouts/dashboards/index.tsx", "./src/pages/about.tsx", "./src/pages/cardaccountsync.tsx", "./src/pages/cardaccounttransactionlog.tsx", "./src/pages/kyc.tsx", "./src/pages/overview.tsx", "./src/pages/sitemap.tsx", "./src/pages/transactions.tsx", "./src/pages/webhook.tsx", "./src/pages/index.ts", "./src/pages/access/accountdetails.tsx", "./src/pages/access/accountmanagement.tsx", "./src/pages/access/cardaccount.tsx", "./src/pages/access/cardaccountdetails.tsx", "./src/pages/access/cardlist.tsx", "./src/pages/access/payment.tsx", "./src/pages/access/index.ts", "./src/pages/authentication/signin.tsx", "./src/pages/authentication/verifyemail.tsx", "./src/pages/authentication/index.ts", "./src/pages/convenient/exchange.tsx", "./src/pages/convenient/injection copy.tsx", "./src/pages/convenient/injection.tsx", "./src/pages/convenient/payment.tsx", "./src/pages/convenient/tenantrevenue.tsx", "./src/pages/convenient/index.ts", "./src/pages/errors/error.tsx", "./src/pages/errors/error400.tsx", "./src/pages/errors/error403.tsx", "./src/pages/errors/error404.tsx", "./src/pages/errors/error500.tsx", "./src/pages/errors/error503.tsx", "./src/pages/errors/index.ts", "./src/pages/profile/info.tsx", "./src/pages/profile/security.tsx", "./src/pages/sys/cardmerchant.tsx", "./src/pages/sys/currency.tsx", "./src/pages/sys/inventory.tsx", "./src/pages/sys/menu.tsx", "./src/pages/sys/organiz.tsx", "./src/pages/sys/organizapi.tsx", "./src/pages/sys/organizcard.tsx", "./src/pages/sys/role.tsx", "./src/pages/sys/systemuser.tsx", "./src/pages/sys/index.tsx", "./src/redux/store.ts", "./src/redux/language/index.ts", "./src/redux/theme/themeslice.ts", "./src/redux/user/index.ts", "./src/routes/routes.tsx", "./src/types/organization.ts", "./src/utils/enum.ts", "./src/utils/excel.ts", "./src/utils/index.ts"], "version": "5.6.3"}