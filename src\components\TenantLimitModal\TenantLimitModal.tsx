import { Modal, Form, Input, Button, message } from 'antd';
import { useEffect, useState } from 'react';
import { tenantLimitQuery, tenantLimitCreate, tenantLimitUpdate } from '../../api/tenantLimit';

interface TenantLimitModalProps {
  visible: boolean;
  onCancel: () => void;
  appId: string;
  remark: string;
}

interface LimitFormValues {
  singleLimit: string;
  singleLimitCount: string;
  totalLimit: string;
}

const TenantLimitModal = ({ visible, onCancel, appId, remark }: TenantLimitModalProps) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [hasData, setHasData] = useState(false);

  useEffect(() => {
    if (visible) {
      fetchLimitData();
    }
  }, [visible]);

  const fetchLimitData = async () => {
    setLoading(true);
    try {
      const res = await tenantLimitQuery({ appId });
      if (res.code === 200 && res.data) {
        form.setFieldsValue(res.data);
        setHasData(true);
      } else {
        form.resetFields();
        setHasData(false);
        if (res?.code !== 200) {
          // message.error(res?.message || '獲取限額數據失敗');
        }
      }
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (values: LimitFormValues) => {
    setLoading(true);
    const data = {
      appId,
      remark,
      groupType: 'TENANT',
      ...values
    };

    try {
      const res = hasData 
        ? await tenantLimitUpdate(data)
        : await tenantLimitCreate(data);

      if (res?.code === 200) {
        message.success(res?.message || (hasData ? '限额更新成功' : '限额创建成功'));
        onCancel();
      } else {
        // message.error(res?.message || '操作失败');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title="设置USAFE限额"
      visible={visible}
      onCancel={onCancel}
      footer={null}
      destroyOnClose
    >
      <Form form={form} onFinish={handleSubmit} layout="vertical">
        <Form.Item
          label="组织ID"
        >
          <Input value={appId} disabled />
        </Form.Item>
        <Form.Item
          label="组织名称"
        >
          <Input value={remark}   />
        </Form.Item>
        <Form.Item
          label="单笔限额"
          name="singleLimit"
          rules={[
            { required: true, message: '请输入单笔限额' },
            { 
              validator: (_, value) => 
                parseFloat(value) >= 0 ? Promise.resolve() : Promise.reject('不能小于0')
            }
          ]}
        >
          <Input type="number" onChange={(e) => String(e.target.value)} />
        </Form.Item>

        <Form.Item
          label="单笔限额次数"
          name="singleLimitCount"
          rules={[
            { required: true, message: '请输入单笔限额次数' },
            { 
              validator: (_, value) => 
                parseFloat(value) >= 0 ? Promise.resolve() : Promise.reject('不能小于0')
            }
          ]}
        >
          <Input type="number" onChange={(e) => String(e.target.value)} />
        </Form.Item>

        <Form.Item
          label="总限额"
          name="totalLimit"
          rules={[
            { required: true, message: '请输入总限额' },
            { 
              validator: (_, value) => 
                parseFloat(value) >= 0 ? Promise.resolve() : Promise.reject('不能小于0')
            }
          ]}
        >
          <Input type="number" onChange={(e) => String(e.target.value)} />
        </Form.Item>

        <Form.Item>
          <div style={{ display: 'flex', justifyContent: 'flex-end', gap: 8 }}>
            <Button onClick={onCancel}>取消</Button>
            <Button type="primary" htmlType="submit" loading={loading}>
              确认
            </Button>
          </div>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default TenantLimitModal;
