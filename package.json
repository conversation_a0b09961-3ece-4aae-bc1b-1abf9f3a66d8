{"name": "beu", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ant-design/charts": "^2.2.1", "@ant-design/colors": "^7.2.0", "@ant-design/icons": "^5.6.0", "@reduxjs/toolkit": "^2.2.8", "@tanem/react-nprogress": "^5.0.51", "@types/react-transition-group": "^4.4.11", "antd": "^5.21.4", "autoprefixer": "^10.4.20", "axios": "^1.7.7", "dayjs": "^1.11.13", "i18next": "^23.16.0", "js-md5": "^0.8.3", "lodash": "^4.17.21", "moment": "^2.30.1", "path": "^0.12.7", "postcss": "^8.4.47", "react": "^18.3.1", "react-countup": "^6.5.3", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-i18next": "^15.0.3", "react-redux": "^9.1.2", "react-responsive": "^10.0.0", "react-router-dom": "^6.26.2", "react-transition-group": "^4.4.5", "redux": "^5.0.1", "redux-persist": "^6.0.0", "tailwindcss": "^3.4.13", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/js": "^9.11.1", "@types/lodash": "^4.17.12", "@types/react": "^18.3.10", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.2", "eslint": "^9.11.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.12", "globals": "^15.9.0", "sass-embedded": "^1.79.4", "typescript": "^5.5.3", "typescript-eslint": "^8.7.0", "vite": "^5.4.8"}}