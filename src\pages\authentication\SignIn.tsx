import {
  Button, Col, Flex,
  Form, Input, message,
  Row, Typography,
} from 'antd';
import { Logo } from '@/components';
import { useMediaQuery } from 'react-responsive';
import { useNavigate } from 'react-router-dom';
import { useEffect, useState, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { Languange } from '@/components/Language/Language.tsx'
import "@/assets/css/login.scss"
import { login, captchaImage } from '@/api/login';
import { useDispatch } from 'react-redux';
import { setuserSlice } from '@/redux/user'

const { Title } = Typography;
type FieldType = {
  code?: string;
  password?: string;
  username?: string;
};


export const SignInPage = () => {
  const { t } = useTranslation();
  const isMobile = useMediaQuery({ maxWidth: 769 });
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [imgData, setImageData] = useState<any>({})
  const dispatch = useDispatch()
  const getImageRef = useRef(true)

  const onFinish = (values: any) => {
    setLoading(true);
    login({
      ...values,
      deviceId: 'a12390',
      platformType: "1",
      uuid: imgData?.uuid
    }).then((res: any) => {
      if (res.message) {
        message.error(t(res.message))
      }
      if (res.code == 200) {
        dispatch(setuserSlice(res.data))
        message.open({
          type: 'success',
          content: t("登录成功"),
        });
        setTimeout(() => {
          navigate('/overview');
        }, 300)
      }

    }).catch((err: any) => {
      console.log(err);
    }).finally(() => {
      setLoading(false);
      getCaptchaImage()
    })
  };

  const onFinishFailed = (errorInfo: any) => {
    console.log('Failed:', errorInfo);
  };

  const getCaptchaImage = () => {
    if (getImageRef.current) {
      captchaImage().then((res: any) => {
        if (res.code == 200) {
          setImageData(res.data)
        }
      }).catch((err: any) => {
        console.log(err);
      }).finally(() => {
        getImageRef.current = true
      })
    }
    getImageRef.current = false
  }

  useEffect(() => {
    getCaptchaImage()
  }, [])

  return (
    <Row style={{ minHeight: isMobile ? 'auto' : '100vh', overflow: 'hidden' }}>
      <Col xs={24} lg={12}>
        <div
          className='h-full login_bg'
        >
          <Flex
            vertical
            align="center"
            justify="center"
            className="text-center h-full"
          >
            <Logo color="white" />
            {
              isMobile ? <span className="text-white pb-[20px]"></span> : <Title level={2} className="text-white">
                <span className="text-white">{t('欢迎登录BEU')}</span>
              </Title>
            }

          </Flex>
        </div>
      </Col>
      <Col xs={24} lg={12}>
        <Flex
          vertical
          align={'center'}
          justify="center"
          gap="middle"
          style={{ height: '100%' }}
        >
          <Title className="m-0">{t('登录')}</Title>
          <Form
            name="sign-up-form"
            layout="vertical"
            labelCol={{ span: 24 }}
            wrapperCol={{ span: 24 }}
            initialValues={{
              username: '',
              password: '',
              code: '',
            }}
            onFinish={onFinish}
            onFinishFailed={onFinishFailed}
            autoComplete="off"
            className='w-[80%]'
            requiredMark={false}
          >

            <Form.Item<FieldType>
              label={t('账号')}
              name="username"
              rules={[
                { required: true, message: t('请输入账号') },
              ]}
            >
              <Input />
            </Form.Item>

            <Form.Item<FieldType>
              label={t('密码')}
              name="password"
              rules={[
                { required: true, message: t('请输入密码') },
              ]}
            >
              <Input.Password />
            </Form.Item>

            <Form.Item<FieldType>
              label={t('验证码')}
              name="code"
              rules={[
                { required: true, message: t('请输入验证码') },
              ]}
            >
              <Flex>
                <Input
                />
                {
                  !imgData?.img ? '' :
                    <div className='ml-[40px] relative w-[100px] cursor-pointer'>
                      <img
                        onClick={getCaptchaImage}
                        className='absolute left-0 -top-[5px] h-[40px]'
                        src={`data:image/gif;base64,${imgData?.img}`} alt="" />
                    </div>
                }
              </Flex>
            </Form.Item>
            <Form.Item>
              <Flex align="center" justify="center">
                <Button
                  type="primary"
                  htmlType="submit"
                  size="large"
                  className='w-[100%]'
                  loading={loading}
                >
                  {t('登录')}
                </Button>
                {/* <Link href={PATH_AUTH.passwordReset}>Forgot password?</Link> */}
              </Flex>
            </Form.Item>
          </Form>
          <div className=' fixed right-[20px] top-[20px]'>
            <Languange />
          </div>
        </Flex>
      </Col>
    </Row>
  );
};
