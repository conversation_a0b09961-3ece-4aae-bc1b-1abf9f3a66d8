import {
  F<PERSON>, Divider, Avatar, message,
  Card, Button, Modal, Form, Input
} from 'antd';
import {
  UserOutlined,
} from '@ant-design/icons';
import { PageHeader } from '@/components';
// import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useEffect, useRef, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { updataPwd } from '@/api/user'
import { useNavigate } from 'react-router-dom'
import { setuserSlice } from '@/redux/user';


export const InfoPage = () => {
  const navigate = useNavigate();
  const { t } = useTranslation()
  const loadingRef = useRef(true)
  const [loading, setLoading] = useState(false)
  const userStore: any = useSelector((state: any) => { return state.user });
  const [open, setOpen] = useState(false);
  const formRef: any = useRef()
  const dispatch = useDispatch()

  useEffect(() => {
    if (loadingRef.current) {
      loadingRef.current = false
      query()
    }
  }, [])

  // const pageChange = (page: number, pageSize: number) => {
  //   setCurrent(page)
  // }

  const query = () => {
    if (!loading) {
      // setTotal(1)
      setLoading(true)
      setTimeout(() => {
        setLoading(false)
      }, 1000)
    }
  }

  const updatePWD = () => {
    formRef.current?.validateFields().then((values: any) => {
      updataPwd({
        ...values,
      }).then((res: any) => {
        const { code } = res
        if (code == 200) {
          message.open({
            type: 'success',
            content: t('修改成功')
          })
          dispatch(setuserSlice(''))
          navigate('/')
        }
        message.open({
          type: 'error',
          content: t(res.message)
        })
      })
    })
  }

  // const hideModal = () => {
  //   setOpen(false);
  // };

  const cancel = () => {
    setOpen(false);
  }

  // const download = () => {
  //   close()
  // }
  // const close = () => {
  //   setDownloadShow(false)
  // }


  // const goDetails = (row: any) => {
  //   // setActiveData(row)
  //   // setIsDetails(true)
  //   navigate('/acccess/account-management/details')

  // }

  return (
    <div className=' relative'>
      <PageHeader
        title={('个人资料')}
        breadcrumbs={[
          // {
          //   title: (
          //     <>
          //       <HomeOutlined />
          //       <span>{t('账户管理')}</span>
          //     </>
          //   ),

          // },
        ]}
      />
      {/* <Button onClick={() => setDownloadShow(true)}
        shape="round"
        style={{ backgroundImage: 'linear-gradient(90deg, rgb(243, 227, 180), rgb(227, 179, 84))' }}
        className=' absolute right-[20px] top-[10px]'> {t('下载交易报告')}</Button> */}
      <Card className='mt-[20px] max-w-[500px]'>
        <div className='text-[12px]  font-bold'>{t('个人信息')}</div>
        <div className='flex justify-center items-center flex-col'>
          <Avatar size={64} icon={<UserOutlined />} />
          <div className=' font-bold mt-[12px]'>{userStore?.user?.user?.userName || '-'}</div>
          <div className='text-[#23bd6a]'>{t('已验证')}</div>
        </div>
        <Divider className='my-[15px]' />
        <Flex justify='space-between' align='center'>
          <span className='text-[#000000a6]'>{t('户口号码')}</span>
          <span>****-****-****</span>
        </Flex>
        <Divider className='my-[15px]' />
        <Flex justify='space-between' align='center'>
          <span className='text-[#000000a6]'>{t('帐户级别')}</span>
          <span>
            {t('等級')}
            0
          </span>
        </Flex>
        <Divider className='my-[15px]' />
        <Flex justify='space-between' align='center'>
          <span className='text-[#000000a6]'>{t('信托成立日期')}</span>
          <span>****-**-**</span>
        </Flex>
        <Divider className='my-[15px]' />
        <Flex justify='space-between' align='center'>
          <span className='text-[#000000a6]'>{t('电话号码')}</span>
          <span>-</span>
        </Flex>
        <Divider className='my-[15px]' />
        <Flex justify='space-between' align='center'>
          <span className='text-[#000000a6]'>{t('地址')}</span>
          <span>-</span>
        </Flex>
        <Divider className='my-[15px]' />
        <Flex justify='space-between' align='center'>
          <span className='text-[#000000a6]'>{t('密码')}</span>
          <Button onClick={() => setOpen(true)}>{t('修改密码')}</Button>
        </Flex>
      </Card>
      <Modal
        title={t("修改密码")}
        open={open}
        onOk={updatePWD}
        onCancel={cancel} 
        okText={t('确认')}
        destroyOnClose={true}
        cancelText={t("取消")}
      >
        <Form
          ref={formRef}
          name="sign-up-form"
          labelCol={{ flex: '100px' }}
          labelWrap
          wrapperCol={{ flex: 1 }}
          autoComplete="off"
          requiredMark={false}
        >
          <Form.Item
            label={t('旧密码')}
            name="oldPassword"
            rules={[
              { required: true, message: t('旧密码') },
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            label={t('新密码')}
            name="newPassword"
            rules={[
              { required: true, message: t('新密码') },
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            label={t('确认新密码')}
            name="confirmNewPassword"
            rules={[
              { required: true, message: t('确认新密码') },
            ]}
          >
            <Input />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};
